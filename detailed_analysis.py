#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析脚本 - 分析开奖房间与投入数据的详细关系
"""

import re
from collections import defaultdict

def parse_betting_log(file_path):
    """解析投注日志文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取投注记录
    betting_records = []
    betting_pattern = r'\| (\d{2}:\d{2}:\d{2}) \| (\d+) \| (\d+) \| ([\d.]+)元 \| (\d+) \| (获胜|失败) \| ([+-][\d.]+)元 \|'
    
    matches = re.findall(betting_pattern, content)
    for match in matches:
        time, period, bet_room, bet_amount, win_room, result, profit = match
        betting_records.append({
            'time': time,
            'period': int(period),
            'bet_room': int(bet_room),
            'bet_amount': float(bet_amount),
            'win_room': int(win_room),
            'result': result,
            'profit': float(profit.replace('+', ''))
        })
    
    # 提取房间统计信息
    room_stats = {}
    period_pattern = r'### 期号 (\d+)\n\n\| 房间号 \| 投入金额 \| 分享金额 \| 投入道具 \| 金额\+道具累计 \| 房间人数 \| 人均投入 \|\n\|.*?\n((?:\| 房间\d+ \| .*?\n)+)'
    
    period_matches = re.findall(period_pattern, content, re.MULTILINE | re.DOTALL)
    
    for period_match in period_matches:
        period_num = int(period_match[0])
        room_data_text = period_match[1]
        
        room_pattern = r'\| 房间(\d+) \| ([\d.]+)元 \| ([\d.]+)元 \| (\d+)道具 \| ([\d.]+)元✓ \| (\d+)人 \| ([\d.]+)元 \|'
        room_matches = re.findall(room_pattern, room_data_text)
        
        period_rooms = {}
        for room_match in room_matches:
            room_num, invest_amount, share_amount, props, total_invest, people_count, avg_invest = room_match
            period_rooms[int(room_num)] = {
                'invest_amount': float(invest_amount),
                'share_amount': float(share_amount),
                'props': int(props),
                'total_invest': float(total_invest),
                'people_count': int(people_count),
                'avg_invest': float(avg_invest)
            }
        
        room_stats[period_num] = period_rooms
    
    return betting_records, room_stats

def detailed_analysis(betting_records, room_stats):
    """详细分析开奖房间与投入数据的关系"""
    print("=" * 80)
    print("🎯 详细分析报告：开奖房间与投入数据关系")
    print("=" * 80)
    
    # 统计各种情况
    win_room_stats = defaultdict(int)  # 各房间开奖次数
    invest_vs_win = defaultdict(list)  # 投入金额与开奖关系
    total_invest_vs_win = defaultdict(list)  # 总投入与开奖关系
    people_vs_win = defaultdict(list)  # 人数与开奖关系
    
    valid_periods = 0
    
    for record in betting_records:
        period = record['period']
        if period not in room_stats:
            continue
            
        win_room = record['win_room']
        period_room_data = room_stats[period]
        
        if win_room not in period_room_data:
            continue
            
        valid_periods += 1
        win_room_stats[win_room] += 1
        
        # 获取开奖房间的数据
        win_room_data = period_room_data[win_room]
        
        # 计算排名
        all_rooms_data = list(period_room_data.values())
        
        # 按投入金额排名
        invest_amounts = [room['invest_amount'] for room in all_rooms_data]
        invest_amounts_sorted = sorted(invest_amounts, reverse=True)
        invest_rank = invest_amounts_sorted.index(win_room_data['invest_amount']) + 1
        
        # 按总投入排名
        total_invests = [room['total_invest'] for room in all_rooms_data]
        total_invests_sorted = sorted(total_invests, reverse=True)
        total_rank = total_invests_sorted.index(win_room_data['total_invest']) + 1
        
        # 按人数排名
        people_counts = [room['people_count'] for room in all_rooms_data]
        people_counts_sorted = sorted(people_counts, reverse=True)
        people_rank = people_counts_sorted.index(win_room_data['people_count']) + 1
        
        invest_vs_win[invest_rank].append(win_room_data['invest_amount'])
        total_invest_vs_win[total_rank].append(win_room_data['total_invest'])
        people_vs_win[people_rank].append(win_room_data['people_count'])
    
    print(f"📊 有效分析期数: {valid_periods}")
    print()
    
    # 各房间开奖统计
    print("🏠 各房间开奖统计:")
    for room in range(1, 9):
        count = win_room_stats[room]
        percentage = (count / valid_periods) * 100
        print(f"  房间{room}: {count}次 ({percentage:.1f}%)")
    print()
    
    # 投入金额排名与开奖关系
    print("💰 投入金额排名与开奖关系:")
    for rank in range(1, 9):
        count = len(invest_vs_win[rank])
        percentage = (count / valid_periods) * 100
        if count > 0:
            avg_amount = sum(invest_vs_win[rank]) / count
            print(f"  第{rank}名: {count}次 ({percentage:.1f}%) - 平均投入: {avg_amount:.1f}元")
        else:
            print(f"  第{rank}名: {count}次 ({percentage:.1f}%)")
    print()
    
    # 总投入排名与开奖关系
    print("🏆 总投入排名与开奖关系:")
    for rank in range(1, 9):
        count = len(total_invest_vs_win[rank])
        percentage = (count / valid_periods) * 100
        if count > 0:
            avg_total = sum(total_invest_vs_win[rank]) / count
            print(f"  第{rank}名: {count}次 ({percentage:.1f}%) - 平均总投入: {avg_total:.1f}元")
        else:
            print(f"  第{rank}名: {count}次 ({percentage:.1f}%)")
    print()
    
    # 人数排名与开奖关系
    print("👥 人数排名与开奖关系:")
    for rank in range(1, 9):
        count = len(people_vs_win[rank])
        percentage = (count / valid_periods) * 100
        if count > 0:
            avg_people = sum(people_vs_win[rank]) / count
            print(f"  第{rank}名: {count}次 ({percentage:.1f}%) - 平均人数: {avg_people:.1f}人")
        else:
            print(f"  第{rank}名: {count}次 ({percentage:.1f}%)")
    print()
    
    # 统计分析
    print("📈 统计分析:")
    
    # 前三名vs后三名对比
    top3_invest = len(invest_vs_win[1]) + len(invest_vs_win[2]) + len(invest_vs_win[3])
    bottom3_invest = len(invest_vs_win[6]) + len(invest_vs_win[7]) + len(invest_vs_win[8])
    
    top3_total = len(total_invest_vs_win[1]) + len(total_invest_vs_win[2]) + len(total_invest_vs_win[3])
    bottom3_total = len(total_invest_vs_win[6]) + len(total_invest_vs_win[7]) + len(total_invest_vs_win[8])
    
    top3_people = len(people_vs_win[1]) + len(people_vs_win[2]) + len(people_vs_win[3])
    bottom3_people = len(people_vs_win[6]) + len(people_vs_win[7]) + len(people_vs_win[8])
    
    print(f"  投入金额前3名开奖: {top3_invest}次 ({(top3_invest/valid_periods)*100:.1f}%)")
    print(f"  投入金额后3名开奖: {bottom3_invest}次 ({(bottom3_invest/valid_periods)*100:.1f}%)")
    print(f"  前3名 vs 后3名比例: {top3_invest/bottom3_invest:.2f}:1" if bottom3_invest > 0 else "  前3名 vs 后3名比例: ∞:1")
    print()
    
    print(f"  总投入前3名开奖: {top3_total}次 ({(top3_total/valid_periods)*100:.1f}%)")
    print(f"  总投入后3名开奖: {bottom3_total}次 ({(bottom3_total/valid_periods)*100:.1f}%)")
    print(f"  前3名 vs 后3名比例: {top3_total/bottom3_total:.2f}:1" if bottom3_total > 0 else "  前3名 vs 后3名比例: ∞:1")
    print()
    
    print(f"  人数前3名开奖: {top3_people}次 ({(top3_people/valid_periods)*100:.1f}%)")
    print(f"  人数后3名开奖: {bottom3_people}次 ({(bottom3_people/valid_periods)*100:.1f}%)")
    print(f"  前3名 vs 后3名比例: {top3_people/bottom3_people:.2f}:1" if bottom3_people > 0 else "  前3名 vs 后3名比例: ∞:1")
    print()
    
    # 结论
    print("📋 详细分析结论:")
    expected_percentage = 12.5  # 8个房间，每个房间理论上12.5%的概率
    
    # 检查是否有明显偏向
    invest_bias = top3_invest > bottom3_invest * 1.2
    total_bias = top3_total > bottom3_total * 1.2
    people_bias = top3_people > bottom3_people * 1.2
    
    if invest_bias:
        print("  ✅ 开奖明显偏向投入金额较高的房间")
    else:
        print("  ❓ 开奖房间与投入金额关系不明显")
    
    if total_bias:
        print("  ✅ 开奖明显偏向总投入较高的房间")
    else:
        print("  ❓ 开奖房间与总投入关系不明显")
    
    if people_bias:
        print("  ✅ 开奖明显偏向人数较多的房间")
    else:
        print("  ❓ 开奖房间与人数关系不明显")

def main():
    """主函数"""
    file_path = "betting_log_20250803_081244.md"
    
    print("🔄 正在解析投注日志文件...")
    betting_records, room_stats = parse_betting_log(file_path)
    
    print(f"✅ 解析完成! 共找到 {len(betting_records)} 条投注记录")
    print(f"✅ 共找到 {len(room_stats)} 期房间统计数据")
    print()
    
    # 进行详细分析
    detailed_analysis(betting_records, room_stats)

if __name__ == "__main__":
    main()
