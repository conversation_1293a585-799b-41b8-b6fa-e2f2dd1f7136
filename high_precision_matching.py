#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度算法匹配
使用2000个数据点进行全面的PRNG算法匹配
"""

import numpy as np
from typing import List, Tuple, Dict
import time
import random

class HighPrecisionMatcher:
    def __init__(self, target_sequence: List[int]):
        """初始化高精度匹配器"""
        self.target_sequence = target_sequence
        self.n = len(target_sequence)
        print(f"目标序列长度: {self.n}")
        
    def calculate_match_rate(self, generated: List[int], sample_size: int = None) -> float:
        """计算匹配率"""
        if sample_size is None:
            sample_size = min(len(generated), self.n)
        
        matches = sum(1 for i in range(sample_size) 
                     if generated[i] == self.target_sequence[i])
        return matches / sample_size
    
    def test_mersenne_twister_variants(self) -> List[Tuple]:
        """测试Mersenne Twister变体"""
        print("=== 测试Mersenne Twister变体 ===")
        
        results = []
        
        # 测试不同的种子
        test_seeds = [1, 2, 3, 7, 11, 13, 17, 19, 23, 31, 37, 41, 43, 47, 51, 59, 61, 67, 71, 73, 
                     100, 123, 456, 789, 1000, 1234, 5678, 9999, 12345, 54321]
        
        for seed in test_seeds:
            # 使用Python的random模块（基于Mersenne Twister）
            random.seed(seed)
            generated = []
            
            for _ in range(self.n):
                # 生成1-8范围的随机数
                val = random.randint(1, 8)
                generated.append(val)
            
            match_rate = self.calculate_match_rate(generated)
            
            if match_rate > 0.15:  # 匹配率超过15%
                results.append(("MersenneTwister", seed, match_rate, generated))
                print(f"  MT种子 {seed}: 匹配率 {match_rate:.4f}")
        
        return sorted(results, key=lambda x: x[2], reverse=True)
    
    def test_xorshift_family(self) -> List[Tuple]:
        """测试Xorshift系列算法"""
        print(f"\n=== 测试Xorshift系列算法 ===")
        
        results = []
        
        # Xorshift32参数组合
        xorshift_params = [
            (13, 17, 5), (17, 13, 5), (5, 17, 13), (7, 13, 17),
            (1, 3, 10), (3, 1, 14), (1, 1, 18), (13, 19, 12),
            (6, 21, 7), (2, 5, 15), (7, 9, 8), (23, 18, 3),
            (15, 10, 4), (4, 15, 25), (25, 8, 21), (21, 12, 4)
        ]
        
        for a, b, c in xorshift_params:
            best_match = 0
            best_seed = 0
            best_sequence = []
            
            # 测试不同种子
            for seed in [1, 2, 3, 7, 11, 13, 17, 19, 23, 31, 37, 41, 43, 47, 51, 59, 61, 67, 71, 73,
                        100, 123, 456, 789, 1000, 1234, 5678, 9999]:
                if seed == 0:
                    continue
                
                # Xorshift32算法
                generated = []
                x = seed
                for _ in range(self.n):
                    x ^= x << a
                    x ^= x >> b
                    x ^= x << c
                    x &= 0xFFFFFFFF  # 保持32位
                    mapped = (x % 8) + 1
                    generated.append(mapped)
                
                match_rate = self.calculate_match_rate(generated)
                
                if match_rate > best_match:
                    best_match = match_rate
                    best_seed = seed
                    best_sequence = generated.copy()
            
            if best_match > 0.15:
                results.append(("Xorshift", (a, b, c), best_seed, best_match, best_sequence))
                print(f"  Xorshift({a}, {b}, {c}) 种子 {best_seed}: 匹配率 {best_match:.4f}")
        
        return sorted(results, key=lambda x: x[3], reverse=True)
    
    def test_lfsr_algorithms(self) -> List[Tuple]:
        """测试线性反馈移位寄存器(LFSR)"""
        print(f"\n=== 测试LFSR算法 ===")
        
        results = []
        
        # LFSR多项式（本原多项式）
        lfsr_polynomials = [
            0x80000057,  # 32位
            0x80000062,
            0x8000007B,
            0x80000095,
            0x800000AF,
            0x800000C6,
            0x800000D6,
            0x800000F1,
            0x8408,      # 16位
            0x8810,
            0x9009,
            0xA001,
            0xB400,
            0xC002,
            0xD008,
            0xF00F
        ]
        
        for poly in lfsr_polynomials:
            best_match = 0
            best_seed = 0
            best_sequence = []
            
            # 测试不同种子
            for seed in [1, 3, 7, 15, 31, 63, 127, 255, 511, 1023, 2047, 4095]:
                if seed == 0:
                    continue
                
                # LFSR算法
                generated = []
                lfsr = seed
                
                for _ in range(self.n):
                    # 计算反馈位
                    feedback = 0
                    temp = lfsr & poly
                    while temp:
                        feedback ^= temp & 1
                        temp >>= 1
                    
                    # 移位并插入反馈位
                    lfsr = (lfsr >> 1) | (feedback << 31)
                    mapped = (lfsr % 8) + 1
                    generated.append(mapped)
                
                match_rate = self.calculate_match_rate(generated)
                
                if match_rate > best_match:
                    best_match = match_rate
                    best_seed = seed
                    best_sequence = generated.copy()
            
            if best_match > 0.15:
                results.append(("LFSR", hex(poly), best_seed, best_match, best_sequence))
                print(f"  LFSR({hex(poly)}) 种子 {best_seed}: 匹配率 {best_match:.4f}")
        
        return sorted(results, key=lambda x: x[3], reverse=True)
    
    def test_custom_algorithms(self) -> List[Tuple]:
        """测试自定义算法"""
        print(f"\n=== 测试自定义算法 ===")
        
        results = []
        
        # 测试基于位置的复杂函数
        print("  测试复杂位置函数...")
        for a in range(1, 10):
            for b in range(0, 10):
                for c in range(1, 8):
                    for d in range(1, 5):
                        # 算法: ((a * i^2 + b * i + c) * d) % 8 + 1
                        generated = []
                        for i in range(self.n):
                            val = ((a * i * i + b * i + c) * d % 8) + 1
                            generated.append(val)
                        
                        match_rate = self.calculate_match_rate(generated, 200)  # 只测试前200个
                        
                        if match_rate > 0.3:
                            full_match = self.calculate_match_rate(generated)
                            if full_match > 0.2:
                                results.append(("PositionQuadratic", (a, b, c, d), 0, full_match, generated))
                                print(f"    二次位置函数 ({a}, {b}, {c}, {d}): 匹配率 {full_match:.4f}")
        
        # 测试混合算法
        print("  测试混合算法...")
        for seed in [1, 7, 13, 31, 47]:
            # 混合LCG和位运算
            generated = []
            x = seed
            for i in range(self.n):
                x = (x * 1103515245 + 12345) & 0x7FFFFFFF
                # 混合位运算
                mixed = x ^ (x >> 16) ^ (i * 17)
                mapped = (mixed % 8) + 1
                generated.append(mapped)
            
            match_rate = self.calculate_match_rate(generated)
            
            if match_rate > 0.15:
                results.append(("MixedLCG", seed, 0, match_rate, generated))
                print(f"    混合LCG 种子 {seed}: 匹配率 {match_rate:.4f}")
        
        return sorted(results, key=lambda x: x[3], reverse=True)
    
    def run_high_precision_matching(self) -> Dict:
        """运行高精度匹配"""
        print("开始高精度算法匹配...")
        print(f"目标序列前20个: {self.target_sequence[:20]}")
        print()
        
        start_time = time.time()
        
        # 测试各种算法
        mt_results = self.test_mersenne_twister_variants()
        xorshift_results = self.test_xorshift_family()
        lfsr_results = self.test_lfsr_algorithms()
        custom_results = self.test_custom_algorithms()
        
        # 合并所有结果
        all_results = []
        
        for result in mt_results:
            all_results.append(("MersenneTwister", result))
        
        for result in xorshift_results:
            all_results.append(("Xorshift", result))
            
        for result in lfsr_results:
            all_results.append(("LFSR", result))
            
        for result in custom_results:
            all_results.append(("Custom", result))
        
        # 按匹配率排序
        all_results.sort(key=lambda x: x[1][2] if x[0] == "MersenneTwister" else x[1][3], reverse=True)
        
        elapsed_time = time.time() - start_time
        print(f"\n高精度匹配完成，耗时 {elapsed_time:.2f} 秒")
        
        return {
            'mersenne_twister': mt_results,
            'xorshift': xorshift_results,
            'lfsr': lfsr_results,
            'custom': custom_results,
            'all_results': all_results,
            'search_time': elapsed_time
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    matcher = HighPrecisionMatcher(sequence)
    results = matcher.run_high_precision_matching()
    
    print(f"\n=== 高精度匹配结果总结 ===")
    
    if results['all_results']:
        print("最佳算法匹配:")
        for i, (algo_type, result) in enumerate(results['all_results'][:5], 1):
            if algo_type == "MersenneTwister":
                algo_name, seed, match_rate, sequence = result
                print(f"\n{i}. {algo_name}")
                print(f"   种子: {seed}")
                print(f"   匹配率: {match_rate:.4f}")
            elif algo_type == "Xorshift":
                algo_name, params, seed, match_rate, sequence = result
                print(f"\n{i}. {algo_name}{params}")
                print(f"   种子: {seed}")
                print(f"   匹配率: {match_rate:.4f}")
            elif algo_type == "LFSR":
                algo_name, poly, seed, match_rate, sequence = result
                print(f"\n{i}. {algo_name}")
                print(f"   多项式: {poly}")
                print(f"   种子: {seed}")
                print(f"   匹配率: {match_rate:.4f}")
            elif algo_type == "Custom":
                algo_name, params, _, match_rate, sequence = result
                print(f"\n{i}. {algo_name}")
                print(f"   参数: {params}")
                print(f"   匹配率: {match_rate:.4f}")
    else:
        print("未找到高匹配率的算法")
    
    print(f"\n总搜索时间: {results['search_time']:.2f} 秒")
