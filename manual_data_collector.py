#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动数据收集器
绕过回调机制问题，直接收集开奖数据
"""

import time
import json
from datetime import datetime
from collections import Counter
from api_framework import GameAPIClient

class ManualDataCollector:
    """手动数据收集器"""
    
    def __init__(self, api_client: GameAPIClient):
        """初始化"""
        self.api_client = api_client
        self.history_file = "game_history.json"
        self.history = []
        self.last_processed_issue = 0
        
        # 加载历史数据
        self.load_history_data()
        
        print("🎯 手动数据收集器已初始化")
        print(f"📊 已加载历史数据: {len(self.history)}期")
    
    def load_history_data(self):
        """加载历史数据"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.history = data.get('history', [])
                self.last_processed_issue = data.get('last_processed_issue', 0)
                
                print(f"✅ 成功加载历史数据:")
                print(f"   开奖历史: {len(self.history)}期")
                print(f"   最后处理期号: {self.last_processed_issue}")
                if len(self.history) >= 10:
                    print(f"   最近10期: {self.history[-10:]}")
                    
        except FileNotFoundError:
            print("📝 未找到历史数据文件，从零开始收集")
            self.history = []
        except Exception as e:
            print(f"⚠️  加载历史数据失败: {e}")
            self.history = []
    
    def save_history_data(self):
        """保存历史数据"""
        try:
            data = {
                'history': self.history,
                'last_processed_issue': self.last_processed_issue,
                'last_updated': datetime.now().isoformat(),
                'data_count': len(self.history)
            }
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 历史数据已保存: {len(self.history)}期")
            
        except Exception as e:
            print(f"❌ 保存历史数据失败: {e}")
    
    def analyze_frequency(self):
        """分析频率统计"""
        
        if len(self.history) < 5:
            print(f"📊 数据不足，无法分析频率")
            return
        
        # 分析最近数据的频率
        recent_data = self.history[-20:] if len(self.history) >= 20 else self.history
        counter = Counter(recent_data)
        
        print(f"\n📊 频率分析 (基于最近{len(recent_data)}期):")
        print(f"   频率分布: {dict(counter)}")
        
        # 找出出现频率最低的房间
        min_freq = min(counter.values())
        least_frequent = [room for room, freq in counter.items() if freq == min_freq]
        
        print(f"   最少出现: 房间{least_frequent} (各{min_freq}次)")
        print(f"   建议投注: {least_frequent[:3]}")  # 推荐前3个
        
        # 计算每个房间的投注价值
        total_samples = len(recent_data)
        betting_values = {}
        
        for room in range(1, 9):
            frequency = counter.get(room, 0)
            betting_values[room] = 1 - (frequency / total_samples)
        
        sorted_rooms = sorted(betting_values.items(), key=lambda x: x[1], reverse=True)
        print(f"   投注价值排序: {sorted_rooms[:5]}")
    
    def process_new_result(self, kill_number: int, issue: int):
        """处理新的开奖结果"""
        
        print(f"\n🎯 手动处理开奖: 期号{issue}, 开出房间{kill_number}")
        
        # 避免重复处理
        if issue <= self.last_processed_issue:
            print(f"⚠️  期号{issue}已处理过，跳过")
            return False
        
        # 更新历史记录
        if kill_number > 0:
            self.history.append(kill_number)
            self.last_processed_issue = issue
            
            # 保留最近200期数据
            if len(self.history) > 200:
                self.history = self.history[-200:]
            
            # 立即保存历史数据
            self.save_history_data()
            
            print(f"📊 历史数据更新: 共{len(self.history)}期")
            print(f"   最近10期: {self.history[-10:]}")
            
            # 检查里程碑
            if len(self.history) == 10:
                print(f"\n🎉 里程碑: 已收集10期数据，可以开始频率分析!")
            elif len(self.history) == 20:
                print(f"\n🎯 重要提醒: 已收集20期数据!")
                print("现在可以考虑切换到预测规则策略")
            elif len(self.history) == 50:
                print(f"\n🚀 数据充足: 已收集50期数据!")
                print("强烈建议切换到预测规则策略")
            
            # 进行频率分析
            if len(self.history) >= 5:
                self.analyze_frequency()
            
            return True
        
        return False
    
    def start_manual_collection(self):
        """启动手动数据收集"""
        
        print("🚀 启动手动数据收集器...")
        print("策略: 手动检测开奖并收集数据")
        print("按 Ctrl+C 停止")
        print()
        
        try:
            while True:
                # 获取游戏状态
                state = self.api_client.get_game_state()
                
                if state:
                    current_time = datetime.now().strftime('%H:%M:%S')
                    status = "🟢 已开奖" if state.state == 2 else "🟡 等待开奖"
                    
                    print(f"📊 [{current_time}] 期号: {state.issue}, 状态: {status}, 开出房间: {state.kill_number}")
                    
                    # 检查是否有新的开奖
                    if state.state == 2 and state.kill_number > 0:
                        if state.issue > self.last_processed_issue:
                            # 处理新开奖
                            success = self.process_new_result(state.kill_number, state.issue)
                            if success:
                                print(f"✅ 成功收集期号{state.issue}的数据")
                    
                    # 等待5秒
                    time.sleep(5)
                else:
                    print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] 无法获取游戏状态")
                    time.sleep(5)
                    
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号")
        finally:
            self.stop_manual_collection()
    
    def stop_manual_collection(self):
        """停止手动收集"""
        
        print("\n🛑 停止手动数据收集器...")
        
        # 保存数据
        self.save_history_data()
        
        print(f"\n📊 数据收集总结:")
        print(f"   收集期数: {len(self.history)}")
        print(f"   数据文件: {self.history_file}")
        print(f"   最后期号: {self.last_processed_issue}")
        
        if len(self.history) >= 10:
            print(f"   最近10期: {self.history[-10:]}")
            
            # 最终频率分析
            self.analyze_frequency()
        
        if len(self.history) >= 50:
            print(f"\n🎯 建议下次使用预测规则策略:")
            print(f"   python enhanced_prediction_system.py")
        elif len(self.history) >= 20:
            print(f"\n🚀 数据已充足，可以尝试增强预测策略")
        else:
            print(f"\n📈 继续收集数据，还需要 {20 - len(self.history)} 期")
        
        print("✅ 手动数据收集器已停止")

def main():
    """主函数"""
    
    print("🎯 手动数据收集器")
    print("=" * 50)
    print("绕过回调机制问题，直接收集开奖数据")
    print()
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    # 创建API客户端
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    
    # 创建手动数据收集器
    collector = ManualDataCollector(api_client)
    
    print("功能说明:")
    print("  • 直接监控游戏状态")
    print("  • 手动检测开奖结果")
    print("  • 立即保存到 game_history.json")
    print("  • 实时频率分析")
    
    response = input("\n是否启动手动数据收集器？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        collector.start_manual_collection()
    else:
        print("系统未启动")

if __name__ == "__main__":
    main()
