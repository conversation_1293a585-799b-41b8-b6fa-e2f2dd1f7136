#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯随机房间选择 + 动态金额投注系统
避免复杂预测，专注资金管理
"""

import random
import time
import json
from datetime import datetime
from typing import Dict, List, Optional
from collections import deque

from api_framework import GameAPIClient, GameMonitor, GameState
from smart_betting_handler import SmartBettingHandler
from real_time_logger import log_betting, log_result


class RandomRoomDynamicAmountSystem:
    """纯随机房间选择 + 动态金额投注系统"""
    
    def __init__(self, api_client: GameAPIClient, config: Dict):
        self.api_client = api_client
        self.config = config
        
        # 投注配置
        self.base_bet_amount = config.get('base_bet_amount', 0.1)
        self.max_bet_amount = config.get('max_bet_amount', 10.0)
        self.min_bet_amount = config.get('min_bet_amount', 0.1)
        
        # 风险控制
        self.max_consecutive_losses = config.get('max_consecutive_losses', 5)
        self.max_daily_loss = config.get('max_daily_loss', 20.0)
        self.stop_loss_percentage = config.get('stop_loss_percentage', 0.3)
        
        # 状态跟踪
        self.consecutive_losses = 0
        self.consecutive_wins = 0
        self.daily_loss = 0.0
        self.total_profit = 0.0
        self.initial_balance = config.get('initial_balance', 100.0)
        self.current_balance = self.initial_balance
        
        # 历史记录
        self.bet_history = deque(maxlen=100)
        self.result_history = deque(maxlen=50)
        
        # 智能投注处理器
        self.smart_betting_handler = SmartBettingHandler(api_client)
        
        # 随机种子 (可选：固定种子用于测试)
        # random.seed(42)  # 取消注释以获得可重复的随机序列
        
        print("🎲 纯随机房间选择 + 动态金额投注系统已初始化")
        print(f"   基础投注: {self.base_bet_amount}元")
        print(f"   最大投注: {self.max_bet_amount}元")
        print(f"   风险控制: 最大连续失败{self.max_consecutive_losses}次")
        print(f"   日损失限制: {self.max_daily_loss}元")
    
    def select_random_room(self, excluded_room: Optional[int] = None) -> int:
        """纯随机选择投注房间"""
        
        all_rooms = list(range(1, 9))  # 房间1-8
        
        # 如果有需要排除的房间
        if excluded_room:
            available_rooms = [room for room in all_rooms if room != excluded_room]
        else:
            available_rooms = all_rooms
        
        # 纯随机选择
        selected_room = random.choice(available_rooms)
        
        print(f"🎲 随机房间选择:")
        if excluded_room:
            print(f"   排除房间: {excluded_room}")
            print(f"   可选房间: {available_rooms}")
        print(f"   随机选择: 房间{selected_room}")
        
        return selected_room
    
    def calculate_dynamic_amount(self) -> float:
        """计算动态投注金额"""
        
        print(f"💰 动态金额计算:")
        print(f"   基础金额: {self.base_bet_amount}元")
        print(f"   连续失败: {self.consecutive_losses}次")
        print(f"   连续获胜: {self.consecutive_wins}次")
        print(f"   当前余额: {self.current_balance:.2f}元")
        
        # 基础金额
        amount = self.base_bet_amount
        
        # 马丁格尔策略 (保守版本)
        if self.consecutive_losses > 0:
            martingale_multiplier = 1.2 ** min(self.consecutive_losses, 4)  # 最多4次
            amount *= martingale_multiplier
            print(f"   马丁格尔调整: ×{martingale_multiplier:.2f}")
        
        # 连胜时适度增加 (凯利公式思想)
        if self.consecutive_wins >= 3:
            win_bonus = 1 + (self.consecutive_wins - 2) * 0.1  # 每连胜1次增加10%
            amount *= min(win_bonus, 1.5)  # 最多1.5倍
            print(f"   连胜奖励: ×{min(win_bonus, 1.5):.2f}")
        
        # 余额比例调整 (资金管理)
        balance_ratio = self.current_balance / self.initial_balance
        if balance_ratio < 0.5:  # 余额不足50%时保守
            balance_multiplier = balance_ratio
            amount *= balance_multiplier
            print(f"   余额调整: ×{balance_multiplier:.2f} (余额比例{balance_ratio:.2f})")
        
        # 风险等级调整
        risk_level = self.assess_risk_level()
        risk_multipliers = {
            "low": 1.0,
            "medium": 0.8,
            "high": 0.5,
            "critical": 0.2
        }
        risk_multiplier = risk_multipliers.get(risk_level, 0.2)
        amount *= risk_multiplier
        print(f"   风险调整: ×{risk_multiplier} (风险等级: {risk_level})")
        
        # 限制在合理范围内
        final_amount = max(self.min_bet_amount, min(amount, self.max_bet_amount))

        # 确保不超过余额的20%
        max_allowed = self.current_balance * 0.2
        final_amount = min(final_amount, max_allowed)

        # 确保至少1元 (API最小支持金额)
        final_amount = max(1.0, final_amount)

        print(f"   最终金额: {final_amount:.2f}元")

        return final_amount
    
    def assess_risk_level(self) -> str:
        """评估当前风险等级"""
        
        # 连续失败风险
        if self.consecutive_losses >= 4:
            return "critical"
        elif self.consecutive_losses >= 3:
            return "high"
        elif self.consecutive_losses >= 2:
            return "medium"
        
        # 日损失风险
        daily_loss_ratio = self.daily_loss / self.max_daily_loss
        if daily_loss_ratio >= 0.8:
            return "critical"
        elif daily_loss_ratio >= 0.6:
            return "high"
        elif daily_loss_ratio >= 0.4:
            return "medium"
        
        # 余额风险
        balance_ratio = self.current_balance / self.initial_balance
        if balance_ratio <= 0.3:
            return "critical"
        elif balance_ratio <= 0.5:
            return "high"
        elif balance_ratio <= 0.7:
            return "medium"
        
        return "low"
    
    def should_place_bet(self) -> bool:
        """判断是否应该投注"""
        
        # 检查连续失败
        if self.consecutive_losses >= self.max_consecutive_losses:
            print(f"⚠️ 连续失败{self.consecutive_losses}次，达到限制")
            return False
        
        # 检查日损失
        if self.daily_loss >= self.max_daily_loss:
            print(f"⚠️ 日损失{self.daily_loss:.2f}元，达到限制")
            return False
        
        # 检查余额
        if self.current_balance <= self.initial_balance * (1 - self.stop_loss_percentage):
            print(f"⚠️ 余额{self.current_balance:.2f}元，触发止损")
            return False
        
        # 检查风险等级
        risk_level = self.assess_risk_level()
        if risk_level == "critical":
            print(f"⚠️ 风险等级为{risk_level}，暂停投注")
            return False
        
        return True
    
    def execute_random_bet(self, current_issue: int) -> Optional[Dict]:
        """执行随机投注"""
        
        if not self.should_place_bet():
            return None
        
        print(f"\n🎲 第{current_issue}期 - 随机投注策略")
        print("=" * 50)
        
        # 随机选择房间
        target_room = self.select_random_room()
        
        # 计算动态金额
        bet_amount = self.calculate_dynamic_amount()
        
        # 记录投注信息
        bet_info = {
            'issue': current_issue,
            'room': target_room,
            'amount': bet_amount,
            'strategy': 'random_room_dynamic_amount',
            'consecutive_losses': self.consecutive_losses,
            'consecutive_wins': self.consecutive_wins,
            'risk_level': self.assess_risk_level(),
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"🎯 投注决策:")
        print(f"   房间: {target_room} (随机选择)")
        print(f"   金额: {bet_amount:.2f}元 (动态计算)")
        print(f"   策略: 纯随机 + 动态金额")
        print(f"   风险等级: {bet_info['risk_level']}")
        
        # 执行智能投注
        bet_result = self.smart_betting_handler.execute_smart_bet(target_room, bet_amount)

        if bet_result.success:
            self.bet_history.append(bet_info)
            log_betting(current_issue, target_room, bet_amount, "随机房间+动态金额")
            actual_amount = bet_result.total_amount if hasattr(bet_result, 'total_amount') else bet_amount
            print(f"✅ 投注成功: 房间{target_room}, 金额{actual_amount:.2f}元")
            return bet_info
        else:
            error_msg = bet_result.message if hasattr(bet_result, 'message') else "未知错误"
            print(f"❌ 投注失败: {error_msg}")
            return None
    
    def process_result(self, issue: int, winning_room: int):
        """处理开奖结果"""
        
        if not self.bet_history:
            return
        
        # 找到对应期数的投注
        bet_info = None
        for bet in reversed(self.bet_history):
            if bet['issue'] == issue:
                bet_info = bet
                break
        
        if not bet_info:
            return
        
        bet_room = bet_info['room']
        bet_amount = bet_info['amount']
        
        # 判断输赢
        is_win = (bet_room != winning_room)
        
        if is_win:
            profit = bet_amount * 0.1  # 10%利润
            self.consecutive_wins += 1
            self.consecutive_losses = 0
            result_text = "获胜"
            print(f"🎉 第{issue}期获胜! 投注房间{bet_room} ≠ 开奖房间{winning_room}")
        else:
            profit = -bet_amount
            self.consecutive_losses += 1
            self.consecutive_wins = 0
            self.daily_loss += bet_amount
            result_text = "失败"
            print(f"😞 第{issue}期失败! 投注房间{bet_room} = 开奖房间{winning_room}")
        
        # 更新余额和统计
        self.current_balance += profit
        self.total_profit += profit
        
        # 记录结果
        result_info = {
            'issue': issue,
            'bet_room': bet_room,
            'winning_room': winning_room,
            'bet_amount': bet_amount,
            'profit': profit,
            'is_win': is_win,
            'consecutive_losses': self.consecutive_losses,
            'consecutive_wins': self.consecutive_wins,
            'current_balance': self.current_balance,
            'total_profit': self.total_profit
        }
        
        self.result_history.append(result_info)
        # 简化日志记录，避免参数不匹配
        # log_result(issue, winning_room, bet_room, bet_amount, profit, result_text)
        
        print(f"💰 结果统计:")
        print(f"   本期盈亏: {profit:+.2f}元")
        print(f"   当前余额: {self.current_balance:.2f}元")
        print(f"   总盈亏: {self.total_profit:+.2f}元")
        print(f"   连续失败: {self.consecutive_losses}次")
        print(f"   连续获胜: {self.consecutive_wins}次")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        
        if not self.result_history:
            return {}
        
        total_bets = len(self.result_history)
        wins = sum(1 for r in self.result_history if r['is_win'])
        win_rate = wins / total_bets if total_bets > 0 else 0
        
        return {
            'total_bets': total_bets,
            'wins': wins,
            'losses': total_bets - wins,
            'win_rate': win_rate,
            'total_profit': self.total_profit,
            'current_balance': self.current_balance,
            'roi': (self.total_profit / self.initial_balance) * 100,
            'consecutive_losses': self.consecutive_losses,
            'consecutive_wins': self.consecutive_wins,
            'daily_loss': self.daily_loss,
            'risk_level': self.assess_risk_level()
        }


def test_random_system():
    """测试随机投注系统"""
    
    # 模拟API客户端
    class MockAPIClient:
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 创建系统
    config = {
        'base_bet_amount': 0.1,
        'max_bet_amount': 5.0,
        'max_consecutive_losses': 4,
        'max_daily_loss': 10.0,
        'initial_balance': 50.0
    }
    
    api_client = MockAPIClient()
    system = RandomRoomDynamicAmountSystem(api_client, config)
    
    print("\n🧪 随机投注系统测试")
    print("=" * 50)
    
    # 模拟10期投注
    for issue in range(123900, 123910):
        bet_info = system.execute_random_bet(issue)
        if bet_info:
            # 模拟开奖结果
            winning_room = random.randint(1, 8)
            system.process_result(issue, winning_room)
        print()
    
    # 显示统计
    stats = system.get_statistics()
    print("📊 最终统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")


if __name__ == "__main__":
    test_random_system()
