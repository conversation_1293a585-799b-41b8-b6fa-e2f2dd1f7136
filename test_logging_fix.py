#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试记录修复
验证投注记录是否正确写入文件
"""

import time
from datetime import datetime

def test_logging_functions():
    """测试记录功能"""
    
    print("🧪 测试投注记录功能")
    print("=" * 50)
    
    try:
        from real_time_logger import log_room_selection, log_betting, log_result, get_summary
        
        # 测试期号
        test_issue = 130018
        
        print(f"1️⃣ 测试房间选择记录...")
        log_room_selection(test_issue, list(range(1, 9)), 5, "LCG算法", {"algorithm": "LCG"})
        print("   ✅ 房间选择记录完成")
        
        print(f"2️⃣ 测试投注记录...")
        log_betting(test_issue, 5, 2.0, True)
        print("   ✅ 投注记录完成")
        
        print(f"3️⃣ 测试结果记录...")
        log_result(test_issue, 3, "获胜", 0.2)
        print("   ✅ 结果记录完成")
        
        print(f"4️⃣ 获取摘要...")
        summary = get_summary()
        print(f"   总投注: {summary['total_bets']}次")
        print(f"   总获胜: {summary['total_wins']}次")
        print(f"   总盈亏: {summary['total_profit']:.2f}元")
        
        print("\n✅ 记录功能测试完成")
        print("请检查生成的记录文件:")
        print("   - betting_log_*.json")
        print("   - betting_log_*.csv") 
        print("   - betting_log_*.md")
        
        return True
        
    except Exception as e:
        print(f"❌ 记录功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_betting_flow():
    """测试真实投注流程的记录"""
    
    print("\n🎯 测试真实投注流程记录")
    print("=" * 50)
    
    try:
        from lcg_betting_system_main import LCGBettingSystemMain
        
        # 创建系统实例
        main_system = LCGBettingSystemMain()
        
        # 初始化真实投注模式
        print("🚀 初始化真实投注模式...")
        system = main_system.initialize_system(real_betting=True)
        
        if system and main_system.real_betting_mode:
            print("✅ 真实投注模式初始化成功")
            
            # 模拟执行一次真实投注记录
            print("🎯 模拟真实投注记录流程...")
            
            test_issue = 130019
            
            # 模拟LCG选择房间
            lcg_room = system.select_optimal_random_room()
            print(f"   🎲 LCG选择房间: {lcg_room}")
            
            # 记录房间选择
            from real_time_logger import log_room_selection, log_betting, log_result
            log_room_selection(test_issue, list(range(1, 9)), lcg_room, "LCG算法", {"algorithm": "LCG"})
            print("   📝 房间选择已记录")
            
            # 记录投注
            bet_amount = 1.5
            log_betting(test_issue, lcg_room, bet_amount, True)
            print("   📝 投注信息已记录")
            
            # 模拟开奖结果
            winning_room = 7 if lcg_room != 7 else 3  # 确保获胜
            result = "获胜" if lcg_room != winning_room else "失败"
            profit = 0.15 if result == "获胜" else -bet_amount
            
            log_result(test_issue, winning_room, result, profit)
            print(f"   📝 开奖结果已记录: 房间{winning_room}, {result}")
            
            print("✅ 真实投注流程记录测试完成")
            
        else:
            print("⚠️ 真实投注模式初始化失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 真实投注流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    
    print("🔧 投注记录修复测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行测试
    test1_result = test_logging_functions()
    test2_result = test_real_betting_flow()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   记录功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   真实投注流程测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过! 记录功能已修复")
        print("\n💡 现在您可以:")
        print("   1. 重新运行真实投注")
        print("   2. 检查生成的记录文件")
        print("   3. 查看完整的投注记录")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题")
    
    return 0 if (test1_result and test2_result) else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
