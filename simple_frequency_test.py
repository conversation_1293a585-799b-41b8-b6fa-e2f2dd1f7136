#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的频率投注测试
验证28期数据的频率投注功能
"""

import json
from collections import Counter

def test_frequency_betting():
    """测试频率投注功能"""
    
    print("🧪 简化频率投注测试")
    print("=" * 50)
    
    # 加载历史数据
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
        
        print(f"✅ 成功加载历史数据: {len(history)}期")
        print(f"📊 最近10期: {history[-10:]}")
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return
    
    if len(history) < 10:
        print("❌ 数据不足，无法进行频率分析")
        return
    
    # 频率分析
    print(f"\n📊 频率分析 (基于{len(history)}期数据):")
    
    # 分析最近数据的频率
    recent_data = history[-20:] if len(history) >= 20 else history
    counter = Counter(recent_data)
    
    print(f"基于最近{len(recent_data)}期的频率分布:")
    for room in range(1, 9):
        count = counter.get(room, 0)
        percentage = (count / len(recent_data)) * 100
        print(f"  房间{room}: {count}次 ({percentage:.1f}%)")
    
    # 计算投注价值
    total_samples = len(recent_data)
    betting_values = {}
    
    for room in range(1, 9):
        frequency = counter.get(room, 0)
        betting_values[room] = 1 - (frequency / total_samples)
    
    # 按投注价值排序
    sorted_rooms = sorted(betting_values.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n🎯 投注价值排序:")
    for i, (room, value) in enumerate(sorted_rooms, 1):
        freq = counter.get(room, 0)
        print(f"  {i}. 房间{room}: 价值{value:.3f} (出现{freq}次)")
    
    # 推荐投注房间
    recommended = [room for room, value in sorted_rooms[:4]]
    print(f"\n💰 推荐投注房间: {recommended}")
    
    # 模拟投注逻辑
    print(f"\n🎯 模拟投注策略:")
    print(f"如果现在投注，会选择:")
    
    # 选择前2个最有价值的房间
    target_rooms = recommended[:2]
    bet_amount = 0.1
    
    for room in target_rooms:
        freq = counter.get(room, 0)
        print(f"  • 房间{room}: 投注{bet_amount}元 (出现{freq}次，价值{betting_values[room]:.3f})")
    
    total_bet = len(target_rooms) * bet_amount
    print(f"  • 总投注金额: {total_bet:.2f}元")
    
    # 计算预期收益
    # 假设赔率1:1.1，胜率按频率策略预期85%计算
    expected_win_rate = 0.85
    expected_return = total_bet * 1.1 * expected_win_rate
    expected_profit = expected_return - total_bet
    
    print(f"\n📈 预期分析:")
    print(f"  • 预期胜率: {expected_win_rate*100:.0f}%")
    print(f"  • 预期回报: {expected_return:.2f}元")
    print(f"  • 预期盈利: {expected_profit:.2f}元")
    print(f"  • ROI: {(expected_profit/total_bet)*100:.1f}%")
    
    print(f"\n✅ 频率投注功能测试完成!")
    print(f"系统已准备好进行实际投注")

def simulate_next_betting():
    """模拟下一期投注"""
    
    print(f"\n🎮 模拟下一期投注流程")
    print("-" * 30)
    
    # 加载数据
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
    except:
        print("❌ 无法加载历史数据")
        return
    
    # 频率分析
    recent_data = history[-20:] if len(history) >= 20 else history
    counter = Counter(recent_data)
    
    # 计算投注价值
    betting_values = {}
    for room in range(1, 9):
        frequency = counter.get(room, 0)
        betting_values[room] = 1 - (frequency / len(recent_data))
    
    # 选择投注房间
    sorted_rooms = sorted(betting_values.items(), key=lambda x: x[1], reverse=True)
    target_rooms = [room for room, value in sorted_rooms[:2]]
    
    print(f"当前历史数据: {len(history)}期")
    print(f"最近10期: {history[-10:]}")
    print(f"推荐投注房间: {target_rooms}")
    
    # 模拟开奖结果检查
    print(f"\n如果下期开奖结果是:")
    for test_room in range(1, 9):
        if test_room in target_rooms:
            result = "❌ 失败"
        else:
            result = "✅ 获胜"
        print(f"  房间{test_room}: {result}")
    
    win_probability = (8 - len(target_rooms)) / 8
    print(f"\n理论胜率: {win_probability*100:.1f}% ({8-len(target_rooms)}/8)")

def main():
    """主函数"""
    
    print("🎯 频率投注系统测试工具")
    print("=" * 50)
    print("验证28期数据的频率投注功能")
    print()
    
    # 测试频率投注
    test_frequency_betting()
    
    # 模拟下一期投注
    simulate_next_betting()
    
    print(f"\n🚀 建议:")
    print("1. 如果测试结果正常，直接运行:")
    print("   python frequency_based_betting.py")
    print()
    print("2. 或者使用修改后的策略选择器:")
    print("   python strategy_selector.py")
    print()
    print("✅ 系统已准备好进行实际投注!")

if __name__ == "__main__":
    main()
