#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略选择器
根据历史数据量自动选择最佳投注策略
"""

import json
import os
from datetime import datetime

def check_history_data():
    """检查历史数据状态"""
    
    history_file = "game_history.json"
    
    try:
        with open(history_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
            total_bets = data.get('total_bets', 0)
            total_wins = data.get('total_wins', 0)
            total_profit = data.get('total_profit', 0.0)
            last_updated = data.get('last_updated', '未知')
            
            return {
                'exists': True,
                'count': len(history),
                'history': history,
                'total_bets': total_bets,
                'total_wins': total_wins,
                'total_profit': total_profit,
                'win_rate': total_wins / total_bets if total_bets > 0 else 0,
                'last_updated': last_updated
            }
            
    except FileNotFoundError:
        return {
            'exists': False,
            'count': 0,
            'message': '未找到历史数据文件'
        }
    except Exception as e:
        return {
            'exists': False,
            'count': 0,
            'message': f'读取历史数据失败: {e}'
        }

def recommend_strategy(data_info):
    """根据数据情况推荐策略"""
    
    count = data_info['count']
    
    if count == 0:
        return {
            'strategy': 'frequency',
            'reason': '无历史数据，需要从头开始收集',
            'description': '频率统计策略 - 数据收集阶段',
            'script': 'frequency_based_betting.py',
            'expected_phases': '收集20-50期数据'
        }
    elif count < 40:  # 调整阈值从20到40
        return {
            'strategy': 'frequency',
            'reason': f'数据积累中({count}期)，继续使用频率策略',
            'description': '频率统计策略 - 数据积累阶段',
            'script': 'frequency_based_betting.py',
            'expected_phases': f'建议收集到40期后再考虑预测策略'
        }
    elif count < 50:
        return {
            'strategy': 'enhanced',
            'reason': f'数据适中({count}期)，可以尝试预测',
            'description': '增强预测策略 - 动态规则生成',
            'script': 'enhanced_prediction_system.py',
            'expected_phases': '动态规则 + 频率备选'
        }
    else:
        return {
            'strategy': 'enhanced',
            'reason': f'数据充足({count}期)，使用完整预测',
            'description': '增强预测策略 - 完整预测系统',
            'script': 'enhanced_prediction_system.py',
            'expected_phases': '静态规则 + 动态规则'
        }

def display_strategy_info():
    """显示策略信息"""
    
    print("🎯 投注策略选择器")
    print("=" * 50)
    
    # 检查历史数据
    data_info = check_history_data()
    
    print("📊 历史数据状态:")
    if data_info['exists']:
        print(f"   ✅ 数据文件存在")
        print(f"   📈 收集期数: {data_info['count']}")
        print(f"   🎲 最近10期: {data_info['history'][-10:] if len(data_info['history']) >= 10 else data_info['history']}")
        
        if data_info['total_bets'] > 0:
            print(f"   💰 历史投注: {data_info['total_bets']}次")
            print(f"   🎯 历史胜率: {data_info['win_rate']*100:.1f}%")
            print(f"   💵 累计盈亏: {data_info['total_profit']:.2f}")
        
        print(f"   🕒 最后更新: {data_info['last_updated']}")
    else:
        print(f"   ❌ {data_info['message']}")
    
    print()
    
    # 推荐策略
    recommendation = recommend_strategy(data_info)
    
    print("🚀 推荐策略:")
    print(f"   策略类型: {recommendation['description']}")
    print(f"   推荐原因: {recommendation['reason']}")
    print(f"   执行脚本: {recommendation['script']}")
    print(f"   预期阶段: {recommendation['expected_phases']}")
    
    print()
    
    # 策略详细说明
    print("📋 策略详细说明:")
    
    if recommendation['strategy'] == 'frequency':
        print("🎯 频率统计策略:")
        print("   • 原理: 投注出现频率最低的房间")
        print("   • 优势: 简单可靠，立即可用")
        print("   • 预期收益: 84.76% ROI")
        print("   • 数据收集: 同时收集开奖历史数据")
        print("   • 适用阶段: 数据不足时的最佳选择")
        
    else:  # enhanced
        print("🚀 增强预测策略:")
        print("   • 原理: 动态规则 + 静态规则 + 频率备选")
        print("   • 优势: 更高精度，自适应学习")
        print("   • 预期收益: 102.68% ROI")
        print("   • 数据要求: 至少20期历史数据")
        print("   • 适用阶段: 数据充足时的最佳选择")
    
    print()
    
    # 风险提醒
    print("⚠️  风险提醒:")
    print("   • 投注有风险，请合理控制资金")
    print("   • 建议先用小额资金测试")
    print("   • 设置止损点，避免过度投注")
    print("   • 历史表现不代表未来结果")
    
    return recommendation

def auto_start_strategy():
    """自动启动推荐的策略"""
    
    recommendation = display_strategy_info()
    
    print("=" * 50)
    response = input("是否启动推荐的策略？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        script = recommendation['script']
        print(f"\n🚀 启动策略: {script}")
        
        import subprocess
        import sys
        
        try:
            # 启动推荐的脚本
            subprocess.run([sys.executable, script], check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ 启动失败: {e}")
        except FileNotFoundError:
            print(f"❌ 脚本文件不存在: {script}")
            print("请确保所有必需的文件都在当前目录")
    else:
        print("未启动策略")
        print(f"\n手动启动命令:")
        print(f"python {recommendation['script']}")

def show_strategy_comparison():
    """显示策略对比"""
    
    print("\n📊 策略对比表:")
    print("=" * 80)
    print(f"{'策略类型':<15} {'适用数据量':<12} {'预期ROI':<10} {'复杂度':<8} {'稳定性':<8}")
    print("-" * 80)
    print(f"{'频率统计':<15} {'0-50期':<12} {'84.76%':<10} {'简单':<8} {'高':<8}")
    print(f"{'增强预测':<15} {'20期以上':<12} {'102.68%':<10} {'中等':<8} {'中等':<8}")
    print(f"{'完整预测':<15} {'50期以上':<12} {'102.68%+':<10} {'复杂':<8} {'高':<8}")
    print("-" * 80)
    
    print("\n🎯 策略选择建议:")
    print("• 新手/数据不足: 使用频率统计策略")
    print("• 有一定数据: 使用增强预测策略")
    print("• 数据充足: 使用完整预测系统")
    
    print("\n📈 策略演进路径:")
    print("频率统计(收集数据) → 增强预测(动态学习) → 完整预测(最优效果)")

def main():
    """主函数"""
    
    print("🎮 智能投注策略选择器")
    print("=" * 50)
    print("根据历史数据自动推荐最佳策略")
    print()
    
    while True:
        print("请选择操作:")
        print("1. 查看策略推荐")
        print("2. 自动启动推荐策略")
        print("3. 策略对比分析")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            display_strategy_info()
            
        elif choice == '2':
            auto_start_strategy()
            break
            
        elif choice == '3':
            show_strategy_comparison()
            
        elif choice == '4':
            print("退出策略选择器")
            break
            
        else:
            print("无效选择，请重新输入")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
