#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LCG逆向分析工具
专门用于分析线性同余生成器的参数和种子
"""

import numpy as np
from typing import List, Tuple, Optional
import itertools
from math import gcd

class LCGReverseAnalyzer:
    def __init__(self, target_sequence: List[int]):
        """初始化LCG逆向分析器"""
        self.target_sequence = target_sequence
        self.n = len(target_sequence)
        
    def lcg_generator(self, seed: int, a: int, c: int, m: int, count: int) -> List[int]:
        """LCG生成器"""
        sequence = []
        x = seed
        for _ in range(count):
            x = (a * x + c) % m
            # 映射到1-8范围
            mapped = (x % 8) + 1
            sequence.append(mapped)
        return sequence
    
    def extended_gcd(self, a: int, b: int) -> Tuple[int, int, int]:
        """扩展欧几里得算法"""
        if a == 0:
            return b, 0, 1
        gcd_val, x1, y1 = self.extended_gcd(b % a, a)
        x = y1 - (b // a) * x1
        y = x1
        return gcd_val, x, y
    
    def mod_inverse(self, a: int, m: int) -> Optional[int]:
        """计算模逆"""
        gcd_val, x, _ = self.extended_gcd(a, m)
        if gcd_val != 1:
            return None
        return (x % m + m) % m
    
    def brute_force_small_lcg(self) -> List[Tuple[int, int, int, int, float]]:
        """暴力搜索小参数的LCG"""
        results = []
        print("暴力搜索小参数LCG...")
        
        # 测试小的模数
        for m in [8, 16, 32, 64, 128, 256, 512, 1024]:
            print(f"  测试模数 m = {m}")
            
            # 对于每个模数，测试不同的a和c
            for a in range(1, min(m, 100)):
                if gcd(a, m) != 1:  # a和m必须互质
                    continue
                    
                for c in range(0, min(m, 50)):
                    best_match = 0
                    best_seed = 0
                    
                    # 测试不同的种子
                    for seed in range(1, min(m, 100)):
                        try:
                            generated = self.lcg_generator(seed, a, c, m, self.n)
                            matches = sum(1 for i in range(self.n) 
                                        if generated[i] == self.target_sequence[i])
                            match_rate = matches / self.n
                            
                            if match_rate > best_match:
                                best_match = match_rate
                                best_seed = seed
                                
                        except (OverflowError, ZeroDivisionError):
                            continue
                    
                    if best_match > 0.3:  # 匹配率超过30%
                        results.append((a, c, m, best_seed, best_match))
                        print(f"    找到匹配: LCG({a}, {c}, {m}), seed={best_seed}, 匹配率={best_match:.3f}")
        
        return sorted(results, key=lambda x: x[4], reverse=True)
    
    def analyze_internal_state(self, a: int, c: int, m: int, seed: int) -> dict:
        """分析LCG的内部状态序列"""
        print(f"\n分析LCG({a}, {c}, {m})的内部状态, seed={seed}")
        
        # 生成内部状态序列
        internal_states = []
        mapped_values = []
        x = seed
        
        for i in range(self.n):
            x = (a * x + c) % m
            internal_states.append(x)
            mapped = (x % 8) + 1
            mapped_values.append(mapped)
        
        # 显示前20个状态
        print("内部状态序列 (前20个):")
        for i in range(min(20, len(internal_states))):
            print(f"  {i:2d}: 内部={internal_states[i]:6d}, 映射={mapped_values[i]}, 目标={self.target_sequence[i]}")
        
        # 计算匹配率
        matches = sum(1 for i in range(self.n) if mapped_values[i] == self.target_sequence[i])
        match_rate = matches / self.n
        
        return {
            'internal_states': internal_states,
            'mapped_values': mapped_values,
            'match_rate': match_rate,
            'matches': matches
        }
    
    def test_different_mappings(self, a: int, c: int, m: int, seed: int) -> List[Tuple[str, float]]:
        """测试不同的映射方式"""
        print(f"\n测试不同映射方式 for LCG({a}, {c}, {m}), seed={seed}")
        
        # 生成内部状态
        internal_states = []
        x = seed
        for _ in range(self.n):
            x = (a * x + c) % m
            internal_states.append(x)
        
        mapping_results = []
        
        # 测试不同的映射函数
        mappings = [
            ("(x % 8) + 1", lambda x: (x % 8) + 1),
            ("((x // 8) % 8) + 1", lambda x: ((x // 8) % 8) + 1),
            ("((x >> 3) % 8) + 1", lambda x: ((x >> 3) % 8) + 1),
            ("((x >> 4) % 8) + 1", lambda x: ((x >> 4) % 8) + 1),
            ("((x >> 8) % 8) + 1", lambda x: ((x >> 8) % 8) + 1),
            ("(x % 7) + 1", lambda x: (x % 7) + 1 if (x % 7) + 1 <= 8 else 8),
            ("((x * 8) // m) + 1", lambda x: min(((x * 8) // m) + 1, 8)),
        ]
        
        for name, mapping_func in mappings:
            try:
                mapped_values = [mapping_func(state) for state in internal_states]
                matches = sum(1 for i in range(self.n) 
                            if mapped_values[i] == self.target_sequence[i])
                match_rate = matches / self.n
                
                if match_rate > 0.1:  # 只显示匹配率超过10%的
                    mapping_results.append((name, match_rate))
                    print(f"  {name}: 匹配率 {match_rate:.3f}")
                    
            except (ZeroDivisionError, OverflowError):
                continue
        
        return sorted(mapping_results, key=lambda x: x[1], reverse=True)
    
    def comprehensive_lcg_search(self) -> dict:
        """综合LCG搜索"""
        print("=== LCG逆向分析 ===")
        
        # 1. 暴力搜索小参数
        small_lcg_results = self.brute_force_small_lcg()
        
        # 2. 对最佳结果进行详细分析
        best_results = []
        if small_lcg_results:
            print(f"\n找到 {len(small_lcg_results)} 个候选LCG参数")
            
            for i, (a, c, m, seed, match_rate) in enumerate(small_lcg_results[:5]):
                print(f"\n--- 分析候选 {i+1}: LCG({a}, {c}, {m}), seed={seed} ---")
                
                # 分析内部状态
                state_analysis = self.analyze_internal_state(a, c, m, seed)
                
                # 测试不同映射
                mapping_results = self.test_different_mappings(a, c, m, seed)
                
                best_results.append({
                    'parameters': (a, c, m, seed),
                    'base_match_rate': match_rate,
                    'state_analysis': state_analysis,
                    'mapping_results': mapping_results
                })
        
        return {
            'small_lcg_results': small_lcg_results,
            'detailed_analysis': best_results
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机生成1-8.txt")
    analyzer = LCGReverseAnalyzer(sequence)
    results = analyzer.comprehensive_lcg_search()
    
    print("\n=== 最终总结 ===")
    if results['detailed_analysis']:
        print("最佳LCG候选:")
        for i, result in enumerate(results['detailed_analysis'][:3]):
            a, c, m, seed = result['parameters']
            base_rate = result['base_match_rate']
            print(f"\n{i+1}. LCG({a}, {c}, {m}), seed={seed}")
            print(f"   基础匹配率: {base_rate:.3f}")
            
            if result['mapping_results']:
                best_mapping = result['mapping_results'][0]
                print(f"   最佳映射: {best_mapping[0]} (匹配率: {best_mapping[1]:.3f})")
    else:
        print("未找到高匹配率的LCG参数")
