#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接预测策略
基于条件概率规则直接预测开出号码，然后避开投注
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import json

class DirectPredictionStrategy:
    def __init__(self, sequence: List[int]):
        """初始化直接预测策略"""
        self.sequence = sequence
        self.n = len(sequence)
        self.prediction_rules = []
        
    def extract_prediction_rules(self) -> List[Dict]:
        """提取高精度预测规则"""
        print("=== 提取高精度预测规则 ===")
        
        all_rules = []
        
        # 重点关注高置信度的3-5元条件规则
        for condition_length in [3, 4, 5]:
            print(f"\n分析 {condition_length} 元预测规则:")
            
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            # 只保留高置信度规则
            min_confidence = 0.6 if condition_length == 3 else 0.7 if condition_length == 4 else 0.8
            min_support = 3
            
            rules_found = 0
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    if confidence >= min_confidence:
                        all_rules.append({
                            'condition': condition,
                            'predicted_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length
                        })
                        rules_found += 1
            
            print(f"  发现 {rules_found} 个高置信度规则 (置信度≥{min_confidence})")
        
        # 按置信度排序
        all_rules.sort(key=lambda x: x['confidence'], reverse=True)
        
        print(f"\n总共发现 {len(all_rules)} 个高精度预测规则")
        if all_rules:
            print("最佳预测规则 (前15个):")
            for i, rule in enumerate(all_rules[:15], 1):
                print(f"  {i:2d}. {rule['condition']} -> {rule['predicted_value']} "
                      f"(置信度: {rule['confidence']:.3f}, 支持度: {rule['support']})")
        
        return all_rules
    
    def validate_prediction_accuracy(self, rules: List[Dict], test_ratio: float = 0.2) -> Dict:
        """验证预测准确率"""
        print(f"\n=== 验证预测准确率 ===")
        
        test_size = int(self.n * test_ratio)
        train_end = self.n - test_size
        
        print(f"训练集: {train_end} 个样本")
        print(f"测试集: {test_size} 个样本")
        
        # 预测统计
        total_predictions = 0
        correct_predictions = 0
        rule_usage = defaultdict(int)
        rule_success = defaultdict(int)
        
        prediction_log = []
        
        for i in range(train_end + 6, self.n):
            actual = self.sequence[i]
            predicted = None
            used_rule = None
            confidence = 0
            
            # 尝试使用规则预测
            for rule in rules:
                condition_length = rule['length']
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predicted = rule['predicted_value']
                        confidence = rule['confidence']
                        used_rule = rule
                        break
            
            if predicted is not None:
                rule_id = str(used_rule['condition'])
                rule_usage[rule_id] += 1
                
                is_correct = (predicted == actual)
                if is_correct:
                    correct_predictions += 1
                    rule_success[rule_id] += 1
                
                total_predictions += 1
                
                prediction_log.append({
                    'position': i,
                    'condition': used_rule['condition'],
                    'predicted': predicted,
                    'actual': actual,
                    'correct': is_correct,
                    'confidence': confidence
                })
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        print(f"预测结果:")
        print(f"  总预测次数: {total_predictions}")
        print(f"  正确预测次数: {correct_predictions}")
        print(f"  预测准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 分析最佳规则
        best_rules = []
        for rule_id, usage in rule_usage.items():
            if usage >= 3:  # 至少使用3次
                success = rule_success[rule_id]
                rule_accuracy = success / usage
                if rule_accuracy >= 0.7:  # 准确率70%以上
                    best_rules.append((rule_id, rule_accuracy, success, usage))
        
        best_rules.sort(key=lambda x: (x[1], x[3]), reverse=True)
        
        print(f"\n表现最佳的预测规则 (准确率≥70%, 使用≥3次):")
        for rule_id, acc, success, usage in best_rules[:10]:
            print(f"  {rule_id}: {acc:.3f} ({success}/{usage})")
        
        return {
            'total_predictions': total_predictions,
            'correct_predictions': correct_predictions,
            'accuracy': accuracy,
            'best_rules': best_rules,
            'prediction_log': prediction_log[-20:]
        }
    
    def calculate_betting_strategy(self, rules: List[Dict], validation: Dict) -> Dict:
        """基于预测结果计算投注策略"""
        print(f"\n=== 基于预测的投注策略 ===")
        
        prediction_accuracy = validation['accuracy']
        
        print(f"预测准确率: {prediction_accuracy:.4f}")
        
        # 计算避开预测数字的期望收益
        if prediction_accuracy > 0:
            # 如果预测准确率为P，那么：
            # - P的概率预测正确，避开后100%获胜
            # - (1-P)的概率预测错误，避开后获胜概率为6/7
            
            win_prob_when_correct = 1.0  # 预测正确时避开，100%获胜
            win_prob_when_wrong = 6/7    # 预测错误时避开，7个数字中6个获胜
            
            overall_win_prob = (prediction_accuracy * win_prob_when_correct + 
                              (1 - prediction_accuracy) * win_prob_when_wrong)
            
            expected_return = overall_win_prob * 1.1 - (1 - overall_win_prob) * 1.0
            
            print(f"基于预测的投注分析:")
            print(f"  预测正确时获胜概率: {win_prob_when_correct:.4f}")
            print(f"  预测错误时获胜概率: {win_prob_when_wrong:.4f}")
            print(f"  综合获胜概率: {overall_win_prob:.4f}")
            print(f"  期望收益: {expected_return:.4f} ({expected_return*100:.2f}%)")
            
            # 与随机投注对比
            random_win_prob = 7/8
            random_expected = random_win_prob * 1.1 - (1 - random_win_prob) * 1.0
            
            improvement = expected_return - random_expected
            print(f"\n与随机投注对比:")
            print(f"  随机投注期望收益: {random_expected:.4f}")
            print(f"  预测策略期望收益: {expected_return:.4f}")
            print(f"  改进幅度: {improvement:.4f} ({improvement*100:.2f}%)")
            
            return {
                'prediction_accuracy': prediction_accuracy,
                'overall_win_probability': overall_win_prob,
                'expected_return': expected_return,
                'improvement_over_random': improvement,
                'is_profitable': expected_return > random_expected
            }
        else:
            print("无法进行有效预测，建议使用频率统计策略")
            return {'prediction_accuracy': 0, 'is_profitable': False}
    
    def simulate_prediction_betting(self, rules: List[Dict], test_size: int = 1000) -> Dict:
        """模拟基于预测的投注效果"""
        print(f"\n=== 模拟预测投注效果 ===")
        
        if self.n < test_size + 50:
            test_size = max(100, self.n - 50)
        
        start_idx = self.n - test_size
        
        total_bets = 0
        total_wins = 0
        total_return = 0
        
        prediction_made = 0
        prediction_correct = 0
        
        bet_log = []
        
        for i in range(start_idx, self.n):
            actual = self.sequence[i]
            predicted = None
            confidence = 0
            
            # 尝试预测
            for rule in rules:
                condition_length = rule['length']
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predicted = rule['predicted_value']
                        confidence = rule['confidence']
                        break
            
            if predicted is not None and confidence >= 0.6:  # 只在高置信度时投注
                prediction_made += 1
                is_prediction_correct = (predicted == actual)
                if is_prediction_correct:
                    prediction_correct += 1
                
                # 投注策略：避开预测的数字，选择其他7个数字中的一个
                # 为简化，选择数字1（如果预测是1则选择2）
                bet_number = 1 if predicted != 1 else 2
                
                won = (bet_number != actual)
                
                total_bets += 1
                if won:
                    total_wins += 1
                    total_return += 1.1
                else:
                    total_return -= 1.0
                
                bet_log.append({
                    'position': i,
                    'predicted': predicted,
                    'actual': actual,
                    'bet_number': bet_number,
                    'prediction_correct': is_prediction_correct,
                    'bet_won': won,
                    'confidence': confidence
                })
        
        # 计算结果
        win_rate = total_wins / total_bets if total_bets > 0 else 0
        prediction_accuracy = prediction_correct / prediction_made if prediction_made > 0 else 0
        roi = (total_return / total_bets) * 100 if total_bets > 0 else 0
        
        print(f"模拟结果:")
        print(f"  预测次数: {prediction_made}")
        print(f"  预测准确率: {prediction_accuracy:.4f} ({prediction_correct}/{prediction_made})")
        print(f"  投注次数: {total_bets}")
        print(f"  投注胜率: {win_rate:.4f} ({total_wins}/{total_bets})")
        print(f"  总收益: {total_return:.2f}")
        print(f"  投资回报率: {roi:.2f}%")
        
        return {
            'prediction_made': prediction_made,
            'prediction_accuracy': prediction_accuracy,
            'total_bets': total_bets,
            'win_rate': win_rate,
            'total_return': total_return,
            'roi': roi,
            'bet_log': bet_log[-10:]
        }
    
    def run_direct_prediction_analysis(self) -> Dict:
        """运行直接预测分析"""
        print("开始直接预测策略分析...")
        print(f"基于 {self.n} 个样本")
        print("策略：预测开出号码，然后避开投注")
        print()
        
        # 1. 提取预测规则
        rules = self.extract_prediction_rules()
        
        if not rules:
            print("未发现足够的高精度预测规则")
            return {'success': False, 'message': '无法建立有效的预测模型'}
        
        # 2. 验证预测准确率
        validation = self.validate_prediction_accuracy(rules)
        
        # 3. 计算投注策略
        betting_strategy = self.calculate_betting_strategy(rules, validation)
        
        # 4. 模拟投注效果
        simulation = self.simulate_prediction_betting(rules)
        
        return {
            'success': True,
            'rules_count': len(rules),
            'validation': validation,
            'betting_strategy': betting_strategy,
            'simulation': simulation
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机23607.txt")
    strategy = DirectPredictionStrategy(sequence)
    results = strategy.run_direct_prediction_analysis()
    
    if results['success']:
        print(f"\n=== 直接预测策略总结 ===")
        print(f"发现预测规则: {results['rules_count']} 个")
        print(f"预测准确率: {results['validation']['accuracy']:.4f}")
        
        if results['betting_strategy']['is_profitable']:
            print(f"✅ 预测策略显示正收益！")
            print(f"   期望收益: {results['betting_strategy']['expected_return']:.4f}")
            print(f"   相比随机投注改进: {results['betting_strategy']['improvement_over_random']:.4f}")
            print(f"   模拟ROI: {results['simulation']['roi']:.2f}%")
        else:
            print(f"❌ 预测策略未显示明显优势")
            print(f"   预测准确率不足以产生正收益")
        
        print(f"\n模拟投注结果:")
        print(f"   投注胜率: {results['simulation']['win_rate']:.4f}")
        print(f"   投资回报率: {results['simulation']['roi']:.2f}%")
    else:
        print(f"❌ {results['message']}")
    
    print(f"\n💡 结论:")
    if results['success'] and results['betting_strategy']['is_profitable']:
        print(f"   直接预测策略可行！建议进一步验证")
    else:
        print(f"   直接预测策略效果有限，建议使用频率统计策略")
