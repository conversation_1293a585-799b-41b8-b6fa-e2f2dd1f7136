#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于频率统计的投注策略
当预测规则无效时的备选方案
"""

import time
import json
from datetime import datetime
from collections import Counter
from typing import Dict, List, Optional
from api_framework import GameAPIClient, GameMonitor, GameState

class FrequencyBasedBetting:
    """基于频率统计的投注系统"""

    def __init__(self, api_client: GameAPIClient, config: Dict):
        """初始化频率投注系统"""
        self.api_client = api_client
        self.config = config
        self.monitor = GameMonitor(api_client)

        # 数据文件路径
        self.history_file = "game_history.json"
        self.betting_log_file = "betting_records.json"

        # 系统状态
        self.is_running = False
        self.betting_enabled = True
        self.history = []  # 开奖历史
        self.betting_records = []  # 投注记录

        # 统计数据
        self.total_bets = 0
        self.total_wins = 0
        self.total_profit = 0.0
        self.consecutive_losses = 0

        # 风险控制
        self.max_consecutive_losses = config.get('max_consecutive_losses', 3)
        self.max_daily_loss = config.get('max_daily_loss', 5.0)
        self.daily_loss = 0.0
        self.base_bet_amount = config.get('base_bet_amount', 0.1)

        # 加载历史数据
        self.load_history_data()

        print("🎯 频率统计投注系统已初始化")
        print(f"📊 已加载历史数据: {len(self.history)}期")

    def load_history_data(self):
        """加载历史数据"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.history = data.get('history', [])
                self.total_bets = data.get('total_bets', 0)
                self.total_wins = data.get('total_wins', 0)
                self.total_profit = data.get('total_profit', 0.0)

                print(f"✅ 成功加载历史数据:")
                print(f"   开奖历史: {len(self.history)}期")
                print(f"   历史投注: {self.total_bets}次")
                print(f"   历史胜率: {self.total_wins/self.total_bets*100:.1f}%" if self.total_bets > 0 else "   历史胜率: 0%")
                print(f"   累计盈亏: {self.total_profit:.2f}")

                if len(self.history) >= 50:
                    print(f"🎯 历史数据充足，可考虑切换到预测规则策略")
                elif len(self.history) >= 20:
                    print(f"📊 数据积累中，继续使用频率策略")
                else:
                    print(f"📈 数据收集阶段，需要更多历史数据")

        except FileNotFoundError:
            print("📝 未找到历史数据文件，从零开始收集")
            self.history = []
        except Exception as e:
            print(f"⚠️  加载历史数据失败: {e}")
            self.history = []

    def save_history_data(self):
        """保存历史数据"""
        try:
            data = {
                'history': self.history,
                'total_bets': self.total_bets,
                'total_wins': self.total_wins,
                'total_profit': self.total_profit,
                'last_updated': datetime.now().isoformat(),
                'data_count': len(self.history)
            }

            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print(f"💾 历史数据已保存: {len(self.history)}期")

        except Exception as e:
            print(f"❌ 保存历史数据失败: {e}")

    def save_betting_records(self):
        """保存投注记录"""
        try:
            if self.betting_records:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"betting_session_{timestamp}.json"

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump({
                        'session_start': datetime.now().isoformat(),
                        'betting_records': self.betting_records,
                        'session_stats': {
                            'total_bets': self.total_bets,
                            'total_wins': self.total_wins,
                            'total_profit': self.total_profit,
                            'win_rate': self.total_wins / self.total_bets if self.total_bets > 0 else 0
                        }
                    }, f, indent=2, ensure_ascii=False)

                print(f"📝 投注记录已保存: {filename}")

        except Exception as e:
            print(f"❌ 保存投注记录失败: {e}")

    def analyze_frequency(self) -> Dict:
        """分析频率统计"""
        
        if len(self.history) < 10:
            return None
        
        # 分析最近50期的频率（如果有的话）
        recent_data = self.history[-50:] if len(self.history) >= 50 else self.history
        counter = Counter(recent_data)
        
        # 找出出现频率最低的房间（最应该投注的）
        least_frequent = counter.most_common()[-1]  # 最少出现的
        most_frequent = counter.most_common()[0]    # 最多出现的
        
        # 计算每个房间的投注价值
        total_samples = len(recent_data)
        betting_values = {}
        
        for room in range(1, 9):
            frequency = counter.get(room, 0)
            # 投注价值 = 1 - 出现频率（频率越低，投注价值越高）
            betting_values[room] = 1 - (frequency / total_samples)
        
        # 按投注价值排序
        sorted_rooms = sorted(betting_values.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'total_samples': total_samples,
            'frequency_distribution': dict(counter),
            'least_frequent': least_frequent,
            'most_frequent': most_frequent,
            'betting_values': betting_values,
            'recommended_rooms': [room for room, value in sorted_rooms[:4]]  # 推荐前4个房间
        }
    
    def should_place_bet(self) -> bool:
        """判断是否应该投注"""
        
        # 检查连续失败次数
        if self.consecutive_losses >= self.max_consecutive_losses:
            print(f"⚠️  连续失败{self.consecutive_losses}次，暂停投注")
            return False
        
        # 检查日损失限制
        if self.daily_loss >= self.max_daily_loss:
            print(f"⚠️  日损失{self.daily_loss:.2f}达到限制，暂停投注")
            return False
        
        # 需要至少10个历史数据
        if len(self.history) < 10:
            print(f"📊 历史数据不足({len(self.history)}/10)，等待更多数据")
            return False
        
        return True
    
    def calculate_bet_amount(self) -> float:
        """计算投注金额"""
        
        # 基础金额
        amount = self.base_bet_amount
        
        # 根据连续失败调整（保守的马丁格尔策略）
        if self.consecutive_losses > 0:
            amount *= (1.2 ** min(self.consecutive_losses, 3))
        
        # 确保不超过限制
        max_amount = self.config.get('max_single_bet', 1.0)
        return min(amount, max_amount)
    
    def execute_frequency_bet(self, analysis: Dict):
        """执行基于频率的投注"""
        
        recommended_rooms = analysis['recommended_rooms']
        bet_amount = self.calculate_bet_amount()
        
        print(f"💰 频率投注策略:")
        print(f"   推荐房间: {recommended_rooms}")
        print(f"   投注金额: {bet_amount:.2f}")
        print(f"   频率分布: {analysis['frequency_distribution']}")
        
        # 选择投注房间（选择前2个最有价值的房间）
        target_rooms = recommended_rooms[:2]
        
        successful_bets = []
        
        for room in target_rooms:
            print(f"🎯 投注房间{room}，金额{bet_amount:.2f}")
            
            bet_result = self.api_client.place_bet(room, bet_amount)
            
            if bet_result.success:
                successful_bets.append({
                    'room': room,
                    'amount': bet_amount,
                    'timestamp': datetime.now().isoformat()
                })
                print(f"✅ 房间{room}投注成功")
            else:
                print(f"❌ 房间{room}投注失败: {bet_result.message}")
        
        # 记录投注
        if successful_bets:
            self.betting_records.append({
                'timestamp': datetime.now().isoformat(),
                'bets': successful_bets,
                'strategy': 'frequency',
                'analysis': analysis
            })
            
            self.total_bets += len(successful_bets)
            print(f"📝 记录投注: {len(successful_bets)}个房间")
    
    def process_new_result(self, state: GameState):
        """处理新的开奖结果"""

        print(f"\n🎯 新开奖: 期号{state.issue}, 开出房间{state.kill_number}")

        # 更新历史记录
        if state.kill_number > 0:  # 确保是有效的开奖结果
            # 避免重复添加相同期号的数据
            if not self.history or state.issue != getattr(self, 'last_issue', 0):
                self.history.append(state.kill_number)
                self.last_issue = state.issue

                # 保留最近200期数据（增加保存量）
                if len(self.history) > 200:
                    self.history = self.history[-200:]

                # 立即保存历史数据
                self.save_history_data()

                print(f"📊 历史数据更新: 共{len(self.history)}期")
                print(f"   最近10期: {self.history[-10:]}")

                # 检查是否可以切换策略
                self.check_strategy_switch()

        # 检查上次投注结果
        self.check_previous_bet_result(state)

        # 显示当前统计
        self.display_statistics()

    def process_betting_opportunity(self, state: GameState):
        """处理投注机会（在投注窗口开放时调用）"""

        print(f"\n💰 投注机会: 期号{state.issue}, 倒计时{state.countdown}秒")

        # 如果可以投注，进行频率分析和投注
        if self.should_place_bet():
            analysis = self.analyze_frequency()

            if analysis:
                print(f"🎯 基于{len(self.history)}期历史数据进行投注")
                self.execute_frequency_bet(analysis)
            else:
                print("📊 频率分析失败，跳过本期")
        else:
            print("⚠️  当前不满足投注条件，跳过本期")

    def check_strategy_switch(self):
        """检查是否可以切换到预测规则策略"""

        data_count = len(self.history)

        if data_count == 50:
            print(f"\n🎯 重要提醒: 已收集50期数据!")
            print("现在可以考虑切换到预测规则策略以获得更高收益")
            print("建议: 停止当前系统，运行 complete_betting_system.py")

        elif data_count == 100:
            print(f"\n🚀 数据充足: 已收集100期数据!")
            print("强烈建议切换到预测规则策略")
            print("预期收益可从84%提升到102%")

        elif data_count % 25 == 0 and data_count >= 25:
            print(f"\n📊 数据里程碑: 已收集{data_count}期数据")

    def check_previous_bet_result(self, state: GameState):
        """检查上次投注结果"""
        
        if not self.betting_records:
            return
        
        last_record = self.betting_records[-1]
        
        # 检查是否有投注结果需要处理
        if 'result_processed' not in last_record:
            actual_room = state.kill_number
            
            # 检查每个投注是否获胜
            total_bet_amount = 0
            total_win_amount = 0
            won_bets = 0
            
            for bet in last_record['bets']:
                bet_room = bet['room']
                bet_amount = bet['amount']
                total_bet_amount += bet_amount
                
                # 判断是否获胜（投注房间 ≠ 开出房间）
                if bet_room != actual_room:
                    win_amount = bet_amount * 1.1  # 1:1.1赔率
                    total_win_amount += win_amount
                    won_bets += 1
            
            # 计算盈亏
            profit = total_win_amount - total_bet_amount
            
            # 更新统计
            if won_bets > 0:
                self.total_wins += won_bets
                self.consecutive_losses = 0
                print(f"🎉 投注结果: 获胜{won_bets}个房间，盈利{profit:.2f}")
            else:
                self.consecutive_losses += 1
                self.daily_loss += total_bet_amount
                print(f"😞 投注结果: 全部失败，亏损{total_bet_amount:.2f}")
            
            self.total_profit += profit
            
            # 标记结果已处理
            last_record['result_processed'] = True
            last_record['actual_room'] = actual_room
            last_record['profit'] = profit
    
    def display_statistics(self):
        """显示统计信息"""
        
        win_rate = self.total_wins / self.total_bets if self.total_bets > 0 else 0
        
        print(f"\n📈 当前统计:")
        print(f"   总投注次数: {self.total_bets}")
        print(f"   获胜次数: {self.total_wins}")
        print(f"   胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
        print(f"   总盈亏: {self.total_profit:.2f}")
        print(f"   连续失败: {self.consecutive_losses}")
        print(f"   日损失: {self.daily_loss:.2f}")
    
    def start_frequency_betting(self):
        """启动频率投注系统"""
        
        print("🚀 启动频率统计投注系统...")
        print("策略: 投注出现频率最低的房间")
        print()
        
        self.is_running = True
        
        try:
            # 开始监控
            self.monitor.start_monitoring(callback=self.process_new_result)
        except KeyboardInterrupt:
            print("🛑 收到停止信号")
        finally:
            self.stop_frequency_betting()
    
    def stop_frequency_betting(self):
        """停止频率投注系统"""
        
        print("\n🛑 停止频率投注系统...")
        
        self.is_running = False
        self.monitor.stop_monitoring()
        
        # 显示最终统计
        self.display_statistics()

        # 保存所有数据
        self.save_history_data()  # 保存历史数据
        self.save_betting_records()  # 保存投注记录

        print(f"\n📊 数据收集总结:")
        print(f"   收集期数: {len(self.history)}")
        print(f"   数据文件: {self.history_file}")

        if len(self.history) >= 50:
            print(f"\n🎯 建议下次使用预测规则策略:")
            print(f"   python complete_betting_system.py")
        else:
            print(f"\n📈 继续收集数据，还需要 {50 - len(self.history)} 期")

        print("✅ 频率投注系统已停止")

def main():
    """主函数"""
    
    print("🎯 频率统计投注系统")
    print("=" * 50)
    print("当预测规则无效时的可靠备选方案")
    print()
    
    # 使用与主系统相同的API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    betting_config = {
        'max_consecutive_losses': 3,
        'max_daily_loss': 500.0,
        'base_bet_amount': 30,
        'max_single_bet': 0.5
    }
    
    # 创建API客户端
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    
    # 创建频率投注系统
    frequency_system = FrequencyBasedBetting(api_client, betting_config)
    
    print("配置信息:")
    print(f"  基础投注金额: {betting_config['base_bet_amount']}")
    print(f"  最大连续失败: {betting_config['max_consecutive_losses']}")
    print(f"  日损失限制: {betting_config['max_daily_loss']}")
    
    response = input("\n是否启动频率投注系统？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        frequency_system.start_frequency_betting()
    else:
        print("系统未启动")

if __name__ == "__main__":
    main()
