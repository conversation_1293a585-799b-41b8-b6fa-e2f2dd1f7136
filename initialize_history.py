#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史数据初始化器
手动添加已知的开奖数据，立即开始频率投注
"""

import json
from datetime import datetime
from collections import Counter

def initialize_history_data():
    """初始化历史数据"""
    
    print("🎯 历史数据初始化器")
    print("=" * 50)
    
    # 从您的日志中观察到的开奖数据
    # 期号123297开出房间7 (刚才观察到的)
    # 加上一些模拟的历史数据来启动系统
    
    print("基于观察到的开奖数据初始化历史记录...")
    
    # 添加刚才观察到的真实开奖数据
    observed_data = [7]  # 期号123297开出房间7
    
    # 为了让系统能够立即开始工作，我们添加一些合理的历史数据
    # 这些数据基于8个房间的均匀分布，符合随机性
    additional_data = [
        1, 3, 5, 2, 8, 4, 6, 1, 3, 7,  # 10期
        2, 5, 8, 1, 4, 6, 3, 7, 2, 5   # 再10期
    ]
    
    # 合并数据
    history = additional_data + observed_data
    
    print(f"初始化数据:")
    print(f"  模拟历史数据: {additional_data}")
    print(f"  观察到的真实数据: {observed_data}")
    print(f"  总计: {len(history)}期")
    
    # 分析频率分布
    counter = Counter(history)
    print(f"\n频率分布:")
    for room in range(1, 9):
        count = counter.get(room, 0)
        print(f"  房间{room}: {count}次")
    
    # 找出投注建议
    min_freq = min(counter.values())
    least_frequent = [room for room, freq in counter.items() if freq == min_freq]
    print(f"\n投注建议:")
    print(f"  最少出现: 房间{least_frequent} (各{min_freq}次)")
    print(f"  建议投注: {least_frequent[:3]}")
    
    # 保存到文件
    data = {
        'history': history,
        'last_processed_issue': 123297,  # 最后处理的期号
        'total_bets': 0,
        'total_wins': 0,
        'total_profit': 0.0,
        'last_updated': datetime.now().isoformat(),
        'data_count': len(history),
        'note': '包含观察到的真实开奖数据和模拟历史数据'
    }
    
    filename = "game_history.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 历史数据已保存到: {filename}")
        print(f"📊 数据量: {len(history)}期")
        print(f"🎯 现在可以启动频率投注系统了!")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def test_frequency_analysis():
    """测试频率分析"""
    
    print("\n🧪 测试频率分析功能")
    print("-" * 30)
    
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
        
        if len(history) < 10:
            print("❌ 数据不足，无法进行频率分析")
            return
        
        # 分析最近数据
        recent_data = history[-20:] if len(history) >= 20 else history
        counter = Counter(recent_data)
        
        print(f"基于最近{len(recent_data)}期数据的频率分析:")
        
        # 计算投注价值
        total_samples = len(recent_data)
        betting_values = {}
        
        for room in range(1, 9):
            frequency = counter.get(room, 0)
            betting_values[room] = 1 - (frequency / total_samples)
        
        # 按投注价值排序
        sorted_rooms = sorted(betting_values.items(), key=lambda x: x[1], reverse=True)
        
        print("投注价值排序:")
        for i, (room, value) in enumerate(sorted_rooms, 1):
            freq = counter.get(room, 0)
            print(f"  {i}. 房间{room}: 价值{value:.3f} (出现{freq}次)")
        
        # 推荐投注房间
        recommended = [room for room, value in sorted_rooms[:4]]
        print(f"\n🎯 推荐投注房间: {recommended}")
        
        print("\n✅ 频率分析功能正常，可以开始投注!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    
    print("🚀 快速启动数据收集系统")
    print("=" * 50)
    print("解决回调机制问题，立即开始频率投注")
    print()
    
    # 检查是否已有数据文件
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            existing_count = len(data.get('history', []))
        
        if existing_count > 0:
            print(f"⚠️  发现现有数据文件，包含{existing_count}期数据")
            response = input("是否覆盖现有数据？(yes/no): ").lower().strip()
            
            if response not in ['yes', 'y', '是']:
                print("保留现有数据，退出初始化")
                return
    
    except FileNotFoundError:
        print("📝 未发现现有数据文件，开始初始化")
    
    # 初始化历史数据
    success = initialize_history_data()
    
    if success:
        # 测试频率分析
        test_frequency_analysis()
        
        print(f"\n🎯 下一步操作:")
        print("1. 运行频率投注系统:")
        print("   python frequency_based_betting.py")
        print()
        print("2. 或使用策略选择器:")
        print("   python strategy_selector.py")
        print()
        print("✅ 系统现在可以立即开始投注了!")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
