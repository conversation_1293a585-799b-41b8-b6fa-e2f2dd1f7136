#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版频率投注系统
解决回调函数不触发的问题
"""

import time
import json
from datetime import datetime
from collections import Counter
from typing import Dict, List, Optional
from api_framework import GameAPIClient, GameState

class FixedFrequencyBetting:
    """修复版频率投注系统"""
    
    def __init__(self, api_client: GameAPIClient, config: Dict):
        """初始化"""
        self.api_client = api_client
        self.config = config
        
        # 数据文件
        self.history_file = "game_history.json"
        
        # 系统状态
        self.is_running = False
        self.history = []
        self.total_bets = 0
        self.total_wins = 0
        self.total_profit = 0.0
        self.consecutive_losses = 0
        
        # 监控状态
        self.last_processed_issue = 0
        
        # 风险控制
        self.max_consecutive_losses = config.get('max_consecutive_losses', 3)
        self.max_daily_loss = config.get('max_daily_loss', 5.0)
        self.daily_loss = 0.0
        self.base_bet_amount = config.get('base_bet_amount', 0.1)
        
        # 加载历史数据
        self.load_history_data()
        
        print("🎯 修复版频率投注系统已初始化")
        print(f"📊 已加载历史数据: {len(self.history)}期")
    
    def load_history_data(self):
        """加载历史数据"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.history = data.get('history', [])
                self.total_bets = data.get('total_bets', 0)
                self.total_wins = data.get('total_wins', 0)
                self.total_profit = data.get('total_profit', 0.0)
                
                print(f"✅ 成功加载历史数据:")
                print(f"   开奖历史: {len(self.history)}期")
                print(f"   历史投注: {self.total_bets}次")
                print(f"   历史胜率: {self.total_wins/self.total_bets*100:.1f}%" if self.total_bets > 0 else "   历史胜率: 0%")
                print(f"   累计盈亏: {self.total_profit:.2f}")
                    
        except FileNotFoundError:
            print("📝 未找到历史数据文件，从零开始收集")
            self.history = []
        except Exception as e:
            print(f"⚠️  加载历史数据失败: {e}")
            self.history = []
    
    def save_history_data(self):
        """保存历史数据"""
        try:
            data = {
                'history': self.history,
                'total_bets': self.total_bets,
                'total_wins': self.total_wins,
                'total_profit': self.total_profit,
                'last_updated': datetime.now().isoformat(),
                'data_count': len(self.history)
            }
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 历史数据已保存: {len(self.history)}期")
            
        except Exception as e:
            print(f"❌ 保存历史数据失败: {e}")
    
    def analyze_frequency(self) -> Dict:
        """分析频率统计"""
        
        if len(self.history) < 10:
            return None
        
        # 分析最近50期的频率
        recent_data = self.history[-50:] if len(self.history) >= 50 else self.history
        counter = Counter(recent_data)
        
        # 计算每个房间的投注价值
        total_samples = len(recent_data)
        betting_values = {}
        
        for room in range(1, 9):
            frequency = counter.get(room, 0)
            betting_values[room] = 1 - (frequency / total_samples)
        
        # 按投注价值排序
        sorted_rooms = sorted(betting_values.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'total_samples': total_samples,
            'frequency_distribution': dict(counter),
            'betting_values': betting_values,
            'recommended_rooms': [room for room, value in sorted_rooms[:4]]
        }
    
    def should_place_bet(self) -> bool:
        """判断是否应该投注"""
        
        if self.consecutive_losses >= self.max_consecutive_losses:
            print(f"⚠️  连续失败{self.consecutive_losses}次，暂停投注")
            return False
        
        if self.daily_loss >= self.max_daily_loss:
            print(f"⚠️  日损失{self.daily_loss:.2f}达到限制，暂停投注")
            return False
        
        if len(self.history) < 10:
            print(f"📊 历史数据不足({len(self.history)}/10)，等待更多数据")
            return False
        
        return True
    
    def execute_frequency_bet(self, analysis: Dict):
        """执行基于频率的投注"""
        
        recommended_rooms = analysis['recommended_rooms']
        bet_amount = self.base_bet_amount
        
        print(f"💰 频率投注策略:")
        print(f"   推荐房间: {recommended_rooms}")
        print(f"   投注金额: {bet_amount:.2f}")
        print(f"   频率分布: {analysis['frequency_distribution']}")
        
        # 选择投注房间（选择前2个最有价值的房间）
        target_rooms = recommended_rooms[:2]
        
        successful_bets = 0
        
        for room in target_rooms:
            print(f"🎯 投注房间{room}，金额{bet_amount:.2f}")
            
            # 模拟投注（实际环境中调用API）
            # bet_result = self.api_client.place_bet(room, bet_amount)
            # 这里我们模拟投注成功
            print(f"✅ 房间{room}投注成功（模拟）")
            successful_bets += 1
        
        if successful_bets > 0:
            self.total_bets += successful_bets
            print(f"📝 记录投注: {successful_bets}个房间")
    
    def process_new_result(self, kill_number: int, issue: int):
        """处理新的开奖结果"""
        
        print(f"\n🎯 新开奖: 期号{issue}, 开出房间{kill_number}")
        
        # 避免重复处理
        if issue <= self.last_processed_issue:
            print(f"⚠️  期号{issue}已处理过，跳过")
            return
        
        # 更新历史记录
        if kill_number > 0:
            self.history.append(kill_number)
            self.last_processed_issue = issue
            
            # 保留最近200期数据
            if len(self.history) > 200:
                self.history = self.history[-200:]
            
            # 立即保存历史数据
            self.save_history_data()
            
            print(f"📊 历史数据更新: 共{len(self.history)}期")
            print(f"   最近10期: {self.history[-10:]}")
            
            # 检查是否可以切换策略
            if len(self.history) == 20:
                print(f"\n🎯 重要提醒: 已收集20期数据!")
                print("现在可以考虑切换到预测规则策略")
            elif len(self.history) == 50:
                print(f"\n🚀 数据充足: 已收集50期数据!")
                print("强烈建议切换到预测规则策略")
        
        # 如果可以投注，进行频率分析和投注
        if self.should_place_bet():
            analysis = self.analyze_frequency()
            
            if analysis:
                self.execute_frequency_bet(analysis)
            else:
                print("📊 频率分析失败，跳过本期")
        
        # 显示当前统计
        self.display_statistics()
    
    def display_statistics(self):
        """显示统计信息"""
        
        win_rate = self.total_wins / self.total_bets if self.total_bets > 0 else 0
        
        print(f"\n📈 当前统计:")
        print(f"   总投注次数: {self.total_bets}")
        print(f"   获胜次数: {self.total_wins}")
        print(f"   胜率: {win_rate:.3f} ({win_rate*100:.1f}%)")
        print(f"   总盈亏: {self.total_profit:.2f}")
        print(f"   连续失败: {self.consecutive_losses}")
        print(f"   日损失: {self.daily_loss:.2f}")
    
    def start_fixed_monitoring(self):
        """启动修复版监控"""
        
        print("🚀 启动修复版频率投注系统...")
        print("策略: 投注出现频率最低的房间")
        print()
        
        self.is_running = True
        
        try:
            while self.is_running:
                # 获取游戏状态
                state = self.api_client.get_game_state()
                
                if state:
                    current_time = datetime.now().strftime('%H:%M:%S')
                    status = "🟢 已开奖" if state.state == 2 else "🟡 等待开奖"
                    
                    print(f"📊 [{current_time}] 期号: {state.issue}, 状态: {status}, 开出房间: {state.kill_number}")
                    
                    # 检查是否有新的开奖
                    if state.state == 2 and state.kill_number > 0:
                        if state.issue > self.last_processed_issue:
                            # 处理新开奖
                            self.process_new_result(state.kill_number, state.issue)
                    
                    # 等待5秒
                    time.sleep(5)
                else:
                    print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] 无法获取游戏状态")
                    time.sleep(5)
                    
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号")
        finally:
            self.stop_fixed_monitoring()
    
    def stop_fixed_monitoring(self):
        """停止监控"""
        
        print("\n🛑 停止修复版频率投注系统...")
        
        self.is_running = False
        
        # 显示最终统计
        self.display_statistics()
        
        # 保存数据
        self.save_history_data()
        
        print(f"\n📊 数据收集总结:")
        print(f"   收集期数: {len(self.history)}")
        print(f"   数据文件: {self.history_file}")
        
        if len(self.history) >= 50:
            print(f"\n🎯 建议下次使用预测规则策略:")
            print(f"   python enhanced_prediction_system.py")
        else:
            print(f"\n📈 继续收集数据，还需要 {50 - len(self.history)} 期")
        
        print("✅ 修复版频率投注系统已停止")

def main():
    """主函数"""
    
    print("🎯 修复版频率统计投注系统")
    print("=" * 50)
    print("解决回调函数不触发的问题")
    print()
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    betting_config = {
        'max_consecutive_losses': 3,
        'max_daily_loss': 5.0,
        'base_bet_amount': 0.1,
        'max_single_bet': 0.5
    }
    
    # 创建API客户端
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    
    # 创建修复版频率投注系统
    fixed_system = FixedFrequencyBetting(api_client, betting_config)
    
    print("配置信息:")
    print(f"  基础投注金额: {betting_config['base_bet_amount']}")
    print(f"  最大连续失败: {betting_config['max_consecutive_losses']}")
    print(f"  日损失限制: {betting_config['max_daily_loss']}")
    
    response = input("\n是否启动修复版频率投注系统？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        fixed_system.start_fixed_monitoring()
    else:
        print("系统未启动")

if __name__ == "__main__":
    main()
