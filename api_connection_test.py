#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API连接测试脚本
专门用于测试和调试API连接问题
"""

import requests
import json
import time
from datetime import datetime

def test_api_connection():
    """测试API连接"""
    
    print("🧪 API连接测试")
    print("=" * 50)
    
    # 使用您配置的实际API信息
    base_url = 'https://fks-api.lucklyworld.com'
    headers = {
        'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
        'packageId': 'com.caike.union',
        'version': '5.2.2',
        'channel': 'official',
        'androidId': 'e21953ffb86fa7a8',
        'userId': '8607652',
        'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
        'IMEI': '',
        'ts': str(int(time.time() * 1000)),
        'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Host': 'fks-api.lucklyworld.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    print("📡 测试游戏状态API...")
    print(f"URL: {base_url}/v11/api/stroke/data")
    print(f"Headers: {json.dumps({k: v[:20] + '...' if len(str(v)) > 20 else v for k, v in headers.items()}, indent=2)}")
    
    try:
        # 方法1: 直接POST请求
        print("\n🔄 方法1: 直接POST请求")
        url = f"{base_url}/v11/api/stroke/data"
        response = session.post(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON响应成功:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return True
            except json.JSONDecodeError:
                print(f"❌ JSON解析失败")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
        
        # 方法2: 带参数的POST请求
        print("\n🔄 方法2: 带参数的POST请求")
        params = {
            'uid': headers['userId'],
            'version': headers['version']
        }
        response = session.post(url, params=params, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 带参数请求成功:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return True
            except json.JSONDecodeError:
                print(f"❌ JSON解析失败")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
        
        # 方法3: 表单数据POST请求
        print("\n🔄 方法3: 表单数据POST请求")
        form_data = {
            'uid': headers['userId'],
            'version': headers['version'],
            'ts': headers['ts']
        }
        response = session.post(url, data=form_data, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 表单请求成功:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return True
            except json.JSONDecodeError:
                print(f"❌ JSON解析失败")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
        
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知异常: {e}")
        return False

def test_token_validity():
    """测试token有效性"""
    
    print("\n🔐 Token有效性测试")
    print("=" * 30)
    
    token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo'
    
    try:
        # 解析JWT token (简单解析，不验证签名)
        import base64
        
        # 分割token
        parts = token.split('.')
        if len(parts) != 3:
            print("❌ Token格式错误")
            return False
        
        # 解析payload
        payload = parts[1]
        # 添加padding
        payload += '=' * (4 - len(payload) % 4)
        
        decoded = base64.b64decode(payload)
        payload_data = json.loads(decoded)
        
        print("Token信息:")
        print(json.dumps(payload_data, indent=2))
        
        # 检查过期时间
        exp = payload_data.get('exp', 0)
        current_time = int(time.time())
        
        if exp > current_time:
            remaining = exp - current_time
            print(f"✅ Token有效，剩余时间: {remaining}秒 ({remaining/3600:.1f}小时)")
            return True
        else:
            print(f"❌ Token已过期")
            return False
            
    except Exception as e:
        print(f"❌ Token解析失败: {e}")
        return False

def continuous_monitoring_test():
    """连续监控测试"""
    
    print("\n🔄 连续监控测试")
    print("=" * 30)
    print("将每5秒请求一次API，按Ctrl+C停止")
    
    base_url = 'https://fks-api.lucklyworld.com'
    headers = {
        'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
        'packageId': 'com.caike.union',
        'version': '5.2.2',
        'channel': 'official',
        'androidId': 'e21953ffb86fa7a8',
        'userId': '8607652',
        'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
        'IMEI': '',
        'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Host': 'fks-api.lucklyworld.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    count = 0
    last_issue = 0
    
    try:
        while True:
            count += 1
            current_time = datetime.now().strftime("%H:%M:%S")
            
            # 更新时间戳
            headers['ts'] = str(int(time.time() * 1000))
            session.headers.update(headers)
            
            try:
                url = f"{base_url}/v11/api/stroke/data"
                response = session.post(url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        issue = data.get('issue', 0)
                        state = data.get('state', 0)
                        kill_number = data.get('killNumber', 0)
                        
                        status = "🟢 已开奖" if state == 2 else "🟡 等待开奖"
                        
                        if issue != last_issue:
                            print(f"🎯 [{current_time}] 第{count}次 - 期号: {issue}, {status}, 开出: {kill_number}")
                            last_issue = issue
                        else:
                            print(f"📊 [{current_time}] 第{count}次 - 期号: {issue}, {status}")
                        
                    except json.JSONDecodeError:
                        print(f"❌ [{current_time}] 第{count}次 - JSON解析失败")
                else:
                    print(f"❌ [{current_time}] 第{count}次 - HTTP错误: {response.status_code}")
                
            except requests.exceptions.RequestException as e:
                print(f"❌ [{current_time}] 第{count}次 - 网络异常: {e}")
            
            time.sleep(5)
            
    except KeyboardInterrupt:
        print(f"\n🛑 监控已停止，共执行{count}次请求")

def main():
    """主函数"""
    
    print("🔧 API连接诊断工具")
    print("=" * 50)
    
    # 1. 测试token有效性
    token_valid = test_token_validity()
    
    if not token_valid:
        print("\n⚠️  Token可能已过期，但仍会继续测试连接")
    
    # 2. 测试API连接
    connection_ok = test_api_connection()
    
    if connection_ok:
        print("\n✅ API连接测试成功！")
        
        # 询问是否进行连续监控
        response = input("\n是否进行连续监控测试？(yes/no): ").lower().strip()
        if response in ['yes', 'y', '是']:
            continuous_monitoring_test()
    else:
        print("\n❌ API连接测试失败！")
        print("\n可能的原因:")
        print("1. Token已过期")
        print("2. 签名算法不正确")
        print("3. 网络连接问题")
        print("4. API地址或参数错误")
        
        print("\n建议:")
        print("1. 检查token是否最新")
        print("2. 确认签名算法")
        print("3. 检查网络连接")

if __name__ == "__main__":
    main()
