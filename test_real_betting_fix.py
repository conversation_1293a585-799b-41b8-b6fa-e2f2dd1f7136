#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实投注路径的格式化错误修复
"""

import os
import sys
from datetime import datetime
from api_framework import GameAPIClient, GameState
from lcg_betting_system_main import LCGBettingSystemMain

def test_real_betting_fix():
    """测试真实投注路径的格式化错误修复"""
    
    print("🧪 测试真实投注路径的格式化错误修复")
    print("=" * 60)
    
    # 创建模拟API客户端
    class MockGameState:
        def __init__(self):
            self.issue = 130557
            self.state = 1  # 投注状态
            self.countdown = 25  # 合适的投注时机
            self.room_stats = {}
    
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return MockGameState()
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
                    self.message = "投注成功"
            return MockResult()
    
    # 创建系统实例
    print("🎯 创建LCG投注系统...")
    
    try:
        # 创建主系统
        main_system = LCGBettingSystemMain()

        # 初始化系统 (使用模拟模式，但后面会替换API客户端)
        main_system.initialize_system(real_betting=False)

        # 替换API客户端为模拟客户端
        main_system.api_client = MockAPIClient()

        # 启用真实投注模式
        main_system.real_betting_mode = True

        # 确保智能投注处理器存在
        if not main_system.smart_betting_handler:
            from smart_betting_handler import SmartBettingHandler
            main_system.smart_betting_handler = SmartBettingHandler(main_system.api_client)

        print("✅ 系统初始化成功")
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    
    # 设置测试状态
    print("📊 设置测试状态...")
    
    # 设置系统状态以触发各种奖励
    main_system.system.consecutive_wins = 3
    main_system.system.consecutive_losses = 0
    main_system.system.total_bets = 15
    main_system.system.total_wins = 14  # 93.3%胜率
    
    print(f"   连续获胜: {main_system.system.consecutive_wins}次")
    print(f"   连续失败: {main_system.system.consecutive_losses}次")
    print(f"   总投注: {main_system.system.total_bets}次")
    print(f"   总获胜: {main_system.system.total_wins}次")
    print(f"   胜率: {main_system.system.total_wins/main_system.system.total_bets*100:.1f}%")
    
    print("\n" + "=" * 60)
    
    # 测试动态金额计算
    print("💰 测试动态金额计算...")
    
    try:
        amount, calculation = main_system.system.calculate_enhanced_dynamic_amount()
        print(f"✅ 动态金额计算成功")
        print(f"   最终金额: {amount}元")
        print(f"   计算详情: {len(calculation)}个字段")
        
        # 显示计算详情
        for key, value in calculation.items():
            print(f"   {key}: {value}")
            
    except Exception as e:
        print(f"❌ 动态金额计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    
    # 测试真实投注执行
    print("🎲 测试真实投注执行...")
    
    current_issue = 130557
    
    print(f"\n📍 执行第{current_issue}期真实投注...")
    
    try:
        # 执行真实投注
        main_system.execute_real_betting(current_issue)
        
        print(f"✅ 真实投注执行成功，无格式化错误")
        
    except Exception as e:
        print(f"❌ 真实投注执行失败: {e}")
        print(f"   错误类型: {type(e).__name__}")
        
        # 检查是否是格式化错误
        if "format" in str(e).lower() or "tuple" in str(e).lower():
            print(f"⚠️ 这是格式化相关的错误，需要进一步修复")
        
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    
    # 检查生成的日志文件
    print("📂 检查生成的日志文件...")
    
    # 查找最新的投注日志文件
    log_files = []
    for filename in os.listdir('.'):
        if filename.startswith('betting_log_') and filename.endswith('.md'):
            log_files.append(filename)
    
    if log_files:
        latest_log = sorted(log_files)[-1]
        print(f"✅ 找到日志文件: {latest_log}")
        
        # 读取并检查内容
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 日志文件内容长度: {len(content)}字符")
            
            # 检查关键内容
            checks = [
                ("动态计算", "动态金额计算"),
                ("基础金额", "基础金额"),
                ("连胜奖励", "连胜奖励"),
                ("算法奖励", "算法奖励"),
                ("风险调整", "风险调整"),
                ("计算公式", "计算公式"),
                (f"期号{current_issue}", "当前期号")
            ]
            
            for check_name, check_text in checks:
                if check_text in content:
                    print(f"✅ 包含{check_name}")
                else:
                    print(f"⚠️ 未包含{check_name}")
            
            # 显示最新的投注记录
            lines = content.split('\n')
            in_current_issue = False
            current_issue_lines = []
            
            for line in lines:
                if f"期号{current_issue}" in line:
                    in_current_issue = True
                    current_issue_lines.append(line)
                elif in_current_issue:
                    current_issue_lines.append(line)
                    if line.strip() == '' and len(current_issue_lines) > 10:
                        break
            
            if current_issue_lines:
                print(f"\n📋 最新投注记录:")
                for line in current_issue_lines[:15]:  # 显示前15行
                    print(f"   {line}")
            
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
            return False
    else:
        print("❌ 未找到日志文件")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试连败场景
    print("🔄 测试连败场景...")
    
    # 设置连败状态
    main_system.system.consecutive_wins = 0
    main_system.system.consecutive_losses = 2
    
    current_issue = 130558
    
    print(f"\n📍 执行第{current_issue}期投注 (连败2次)...")
    
    try:
        # 执行真实投注
        main_system.execute_real_betting(current_issue)
        
        print(f"✅ 连败场景投注执行成功")
        
    except Exception as e:
        print(f"❌ 连败场景投注执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("🎯 测试完成!")
    print("✅ 真实投注路径格式化错误修复测试通过")
    print("✅ 动态金额计算详情正确传递")
    print("✅ 日志记录功能正常工作")
    print("✅ 支持连胜和连败场景")
    
    return True

if __name__ == "__main__":
    success = test_real_betting_fix()
    if success:
        print("\n🎉 所有测试通过！真实投注路径的格式化错误已修复。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
