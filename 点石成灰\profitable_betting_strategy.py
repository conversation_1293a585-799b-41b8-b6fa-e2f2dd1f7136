#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于历史数据分析的新盈利投注策略
解决高胜率但仍亏损的问题
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from collections import Counter
import statistics

class ProfitableBettingStrategy:
    """盈利投注策略"""
    
    def __init__(self):
        """初始化策略"""
        self.load_historical_analysis()
        
        # 策略参数
        self.base_bet_amount = 1.0  # 基础投注金额
        self.max_bet_amount = 10.0  # 最大投注金额
        self.profit_target = 50.0   # 目标盈利
        self.stop_loss = -20.0      # 止损线
        
        # 风险控制
        self.consecutive_losses = 0
        self.max_consecutive_losses = 3
        self.daily_profit = 0.0
        
    def load_historical_analysis(self):
        """加载历史数据分析"""
        
        # 基于您的历史数据的关键发现
        self.historical_insights = {
            'overall_win_rate': 0.878,  # 87.8%胜率
            'profit_per_win': 0.10,     # 每次获胜收益
            'loss_per_fail': 1.00,      # 每次失败损失
            'break_even_rate': 0.909,   # 盈亏平衡需要90.9%胜率
            
            # 失败模式分析
            'high_risk_patterns': [
                'consecutive_high_confidence',  # 连续高置信度
                'frequency_lowest_trap',        # 频率最低陷阱
                'late_night_instability'       # 深夜不稳定
            ],
            
            # 成功模式
            'success_patterns': [
                'medium_confidence_stable',     # 中等置信度稳定
                'frequency_balanced',           # 频率平衡选择
                'prime_time_betting'           # 黄金时段投注
            ]
        }
    
    def calculate_optimal_bet_amount(self, confidence: float, recent_performance: Dict) -> float:
        """计算最优投注金额"""
        
        # 基础金额
        base_amount = self.base_bet_amount
        
        # 置信度调整 (反向调整 - 高置信度降低投注)
        if confidence >= 0.95:
            confidence_multiplier = 0.5  # 高置信度风险高，降低投注
        elif confidence >= 0.8:
            confidence_multiplier = 0.8  # 中高置信度适中
        elif confidence >= 0.7:
            confidence_multiplier = 1.2  # 中等置信度增加投注
        else:
            confidence_multiplier = 0.6  # 低置信度降低投注
        
        # 连败调整
        if self.consecutive_losses >= 2:
            loss_multiplier = 0.3  # 连败时大幅降低投注
        elif self.consecutive_losses == 1:
            loss_multiplier = 0.7  # 单次失败适度降低
        else:
            loss_multiplier = 1.0  # 正常投注
        
        # 时间段调整
        current_hour = datetime.now().hour
        if 9 <= current_hour <= 21:  # 白天黄金时段
            time_multiplier = 1.2
        elif 22 <= current_hour <= 23 or 6 <= current_hour <= 8:  # 次优时段
            time_multiplier = 1.0
        else:  # 深夜高风险时段
            time_multiplier = 0.4
        
        # 计算最终金额
        optimal_amount = base_amount * confidence_multiplier * loss_multiplier * time_multiplier
        
        # 限制在合理范围内
        optimal_amount = max(0.1, min(optimal_amount, self.max_bet_amount))
        
        return round(optimal_amount, 2)
    
    def should_skip_betting(self, game_state: Dict, prediction: Dict) -> Tuple[bool, str]:
        """判断是否应该跳过投注"""
        
        # 连败保护
        if self.consecutive_losses >= self.max_consecutive_losses:
            return True, f"连败{self.consecutive_losses}次，暂停投注"
        
        # 止损保护
        if self.daily_profit <= self.stop_loss:
            return True, f"触发止损线{self.stop_loss}元"
        
        # 目标达成保护
        if self.daily_profit >= self.profit_target:
            return True, f"达成目标盈利{self.profit_target}元"
        
        # 高风险时段保护
        current_hour = datetime.now().hour
        if 0 <= current_hour <= 5:  # 凌晨高风险
            if prediction['confidence'] < 0.9:
                return True, "凌晨时段，置信度不足，跳过投注"
        
        # 高置信度连续出现保护
        if prediction['confidence'] >= 0.95:
            # 检查最近是否有高置信度失败
            return True, "高置信度风险保护，跳过投注"
        
        return False, ""
    
    def select_optimal_rooms(self, available_rooms: List[int], prediction: Dict, 
                           historical_data: List[int]) -> List[Tuple[int, float]]:
        """选择最优投注房间组合"""
        
        # 分析最近20期的房间频率
        recent_data = historical_data[-20:] if len(historical_data) >= 20 else historical_data
        room_frequency = Counter(recent_data)
        
        # 计算每个房间的风险评分
        room_scores = {}
        for room in available_rooms:
            frequency = room_frequency.get(room, 0)
            
            # 频率评分 (中等频率最优)
            if frequency == 0:
                freq_score = 0.3  # 从未出现风险高
            elif 1 <= frequency <= 2:
                freq_score = 0.9  # 低频率较好
            elif 3 <= frequency <= 4:
                freq_score = 1.0  # 中等频率最优
            elif 5 <= frequency <= 6:
                freq_score = 0.7  # 高频率一般
            else:
                freq_score = 0.4  # 超高频率风险高
            
            # 位置评分 (中间房间相对稳定)
            if room in [4, 5]:
                position_score = 1.0  # 中间房间
            elif room in [3, 6]:
                position_score = 0.9  # 次中间
            elif room in [2, 7]:
                position_score = 0.8  # 偏外
            else:  # 1, 8
                position_score = 0.7  # 边缘房间
            
            # 综合评分
            room_scores[room] = freq_score * position_score
        
        # 选择评分最高的3个房间
        sorted_rooms = sorted(room_scores.items(), key=lambda x: x[1], reverse=True)
        
        return sorted_rooms[:3]
    
    def calculate_multi_room_allocation(self, total_amount: float, 
                                      room_scores: List[Tuple[int, float]]) -> Dict[int, float]:
        """计算多房间投注分配"""
        
        total_score = sum(score for _, score in room_scores)
        allocation = {}
        
        for room, score in room_scores:
            # 按评分比例分配金额
            room_amount = (score / total_score) * total_amount
            allocation[room] = round(room_amount, 2)
        
        return allocation
    
    def execute_profitable_strategy(self, game_state: Dict, prediction: Dict, 
                                  historical_data: List[int]) -> Dict:
        """执行盈利策略"""
        
        # 检查是否应该跳过
        should_skip, skip_reason = self.should_skip_betting(game_state, prediction)
        if should_skip:
            return {
                'action': 'skip',
                'reason': skip_reason,
                'recommendation': '等待更好的投注时机'
            }
        
        # 计算最优投注金额
        recent_performance = {'consecutive_losses': self.consecutive_losses}
        optimal_amount = self.calculate_optimal_bet_amount(
            prediction['confidence'], recent_performance
        )
        
        # 选择最优房间
        predicted_room = prediction['room']
        available_rooms = [i for i in range(1, 9) if i != predicted_room]
        
        room_scores = self.select_optimal_rooms(
            available_rooms, prediction, historical_data
        )
        
        # API限制：每期只能投注一个房间
        # 选择评分最高的房间进行单房间投注
        best_room = room_scores[0][0]
        allocation = {best_room: optimal_amount}
        strategy_type = "单房间最优投注"
        
        return {
            'action': 'bet',
            'strategy_type': strategy_type,
            'predicted_room': predicted_room,
            'allocation': allocation,
            'total_amount': optimal_amount,
            'confidence': prediction['confidence'],
            'risk_level': self.assess_risk_level(prediction, game_state),
            'expected_profit': self.calculate_expected_profit(allocation, prediction['confidence'])
        }
    
    def assess_risk_level(self, prediction: Dict, game_state: Dict) -> str:
        """评估风险等级"""
        
        confidence = prediction['confidence']
        current_hour = datetime.now().hour
        
        # 基础风险评估
        if confidence >= 0.95:
            base_risk = "高"  # 高置信度反而风险高
        elif confidence >= 0.8:
            base_risk = "中"
        elif confidence >= 0.7:
            base_risk = "低"
        else:
            base_risk = "中"
        
        # 时间调整
        if 0 <= current_hour <= 5:
            time_risk = "高"
        elif 22 <= current_hour <= 23:
            time_risk = "中"
        else:
            time_risk = "低"
        
        # 连败调整
        if self.consecutive_losses >= 2:
            loss_risk = "高"
        elif self.consecutive_losses == 1:
            loss_risk = "中"
        else:
            loss_risk = "低"
        
        # 综合评估
        risk_factors = [base_risk, time_risk, loss_risk]
        if "高" in risk_factors:
            return "高风险"
        elif risk_factors.count("中") >= 2:
            return "中风险"
        else:
            return "低风险"
    
    def calculate_expected_profit(self, allocation: Dict[int, float], confidence: float) -> float:
        """计算期望收益"""
        
        total_bet = sum(allocation.values())
        
        # 基于历史数据的实际胜率 (考虑置信度偏差)
        if confidence >= 0.95:
            actual_win_rate = 0.85  # 高置信度实际胜率较低
        elif confidence >= 0.8:
            actual_win_rate = 0.90  # 中高置信度
        elif confidence >= 0.7:
            actual_win_rate = 0.88  # 中等置信度最稳定
        else:
            actual_win_rate = 0.82  # 低置信度
        
        # 期望收益计算
        expected_win = total_bet * 0.1 * actual_win_rate  # 获胜收益
        expected_loss = total_bet * (1 - actual_win_rate)  # 失败损失
        
        return round(expected_win - expected_loss, 3)
    
    def update_performance(self, bet_result: Dict):
        """更新投注表现"""
        
        if bet_result['result'] == '获胜':
            self.consecutive_losses = 0
            self.daily_profit += bet_result['profit']
        else:
            self.consecutive_losses += 1
            self.daily_profit += bet_result['loss']
        
        print(f"📊 策略更新: 连败{self.consecutive_losses}次, 日盈亏{self.daily_profit:+.2f}元")

def main():
    """测试新策略"""
    
    strategy = ProfitableBettingStrategy()
    
    # 模拟测试
    test_prediction = {
        'room': 8,
        'confidence': 0.85,
        'rule_type': 'dynamic'
    }
    
    test_game_state = {
        'issue': 129999,
        'countdown': 25
    }
    
    test_history = [1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 2, 3, 4, 5]
    
    result = strategy.execute_profitable_strategy(
        test_game_state, test_prediction, test_history
    )
    
    print("🎯 新盈利策略测试结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
