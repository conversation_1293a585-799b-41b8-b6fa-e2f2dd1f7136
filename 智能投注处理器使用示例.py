#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能投注处理器使用示例
演示如何将自定义金额转换为API支持的固定面额投注
"""

from smart_betting_handler import SmartBettingHandler, BetRequest

def demo_smart_betting():
    """演示智能投注处理器的使用"""
    
    print("🎯 智能投注处理器使用示例")
    print("=" * 60)
    
    # 模拟API客户端
    class DemoAPIClient:
        def __init__(self):
            self.call_count = 0
            
        def place_bet(self, room, amount):
            self.call_count += 1
            
            class DemoResult:
                def __init__(self, room, amount, call_count):
                    self.success = True
                    self.data = {
                        'room': room,
                        'amount': amount,
                        'call_sequence': call_count,
                        'api_response': f'投注成功: 房间{room}, 金额{amount}元'
                    }
                    
            return DemoResult(room, amount, self.call_count)
    
    # 创建智能投注处理器
    api_client = DemoAPIClient()
    handler = SmartBettingHandler(api_client)
    
    print("\n📋 测试场景:")
    
    # 场景1: 动态算法产生的典型金额
    test_scenarios = [
        {"room": 4, "amount": 1.2, "description": "动态算法: 基础1.0 × 1.2置信度调整"},
        {"room": 7, "amount": 0.96, "description": "动态算法: 基础1.0 × 0.8置信度 × 1.2时间调整"},
        {"room": 3, "amount": 2.4, "description": "动态算法: 基础1.0 × 2.0高金额 × 1.2时间调整"},
        {"room": 5, "amount": 0.36, "description": "动态算法: 基础1.0 × 1.2置信度 × 0.3连败保护"},
        {"room": 2, "amount": 15.75, "description": "动态算法: 复杂计算结果"},
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- 场景{i}: {scenario['description']} ---")
        print(f"目标投注: 房间{scenario['room']}, 金额{scenario['amount']:.2f}元")
        
        # 执行智能投注
        result = handler.execute_smart_bet(scenario['room'], scenario['amount'])
        
        if result.success:
            print(f"🎉 投注成功!")
            print(f"   API调用次数: {len(result.successful_bets)}次")
            print(f"   实际投注金额: {result.total_amount:.2f}元")
            print(f"   金额差异: {result.total_amount - scenario['amount']:+.2f}元")
            
            # 显示详细的API调用序列
            print(f"   API调用序列:")
            for bet in result.successful_bets:
                print(f"     第{bet['sequence']}次: 房间{bet['room']}, 金额{bet['amount']}元")
        else:
            print(f"❌ 投注失败: {result.message}")
    
    print(f"\n📊 总计API调用次数: {api_client.call_count}次")
    
    # 演示批量投注
    print(f"\n🎯 批量投注演示:")
    
    batch_requests = [
        BetRequest(room=1, amount=1.5),
        BetRequest(room=6, amount=3.2),
        BetRequest(room=8, amount=0.7),
    ]
    
    batch_results = handler.batch_smart_bet(batch_requests)
    
    print(f"\n📋 批量投注结果汇总:")
    for room, result in batch_results.items():
        if result.success:
            print(f"   房间{room}: ✅ 成功 ({result.total_amount:.2f}元)")
        else:
            print(f"   房间{room}: ❌ 失败 ({result.message})")

def demo_integration_with_new_system():
    """演示与新盈利系统的集成"""
    
    print(f"\n🔗 与新盈利系统集成演示")
    print("=" * 60)
    
    # 模拟新盈利系统的动态投注金额计算
    def simulate_dynamic_amount_calculation():
        """模拟动态投注金额算法"""
        
        # 模拟不同的系统状态
        scenarios = [
            {
                "confidence": 0.95,
                "consecutive_losses": 0,
                "time_hour": 14,
                "base_amount": 1.0,
                "description": "高置信度保护"
            },
            {
                "confidence": 0.75,
                "consecutive_losses": 2,
                "time_hour": 14,
                "base_amount": 1.0,
                "description": "连败保护"
            },
            {
                "confidence": 0.8,
                "consecutive_losses": 0,
                "time_hour": 2,
                "base_amount": 1.0,
                "description": "深夜保护"
            },
            {
                "confidence": 0.7,
                "consecutive_losses": 0,
                "time_hour": 14,
                "base_amount": 1.0,
                "description": "中等置信度增强"
            }
        ]
        
        results = []
        
        for scenario in scenarios:
            # 模拟动态投注金额算法
            base_amount = scenario["base_amount"]
            
            # 置信度调整
            if scenario["confidence"] >= 0.95:
                confidence_multiplier = 0.5
            elif scenario["confidence"] >= 0.8:
                confidence_multiplier = 0.8
            elif scenario["confidence"] >= 0.7:
                confidence_multiplier = 1.2
            else:
                confidence_multiplier = 0.6
            
            # 连败调整
            if scenario["consecutive_losses"] >= 2:
                loss_multiplier = 0.3
            elif scenario["consecutive_losses"] == 1:
                loss_multiplier = 0.7
            else:
                loss_multiplier = 1.0
            
            # 时间调整
            if 9 <= scenario["time_hour"] <= 21:
                time_multiplier = 1.2
            elif 22 <= scenario["time_hour"] <= 23 or 6 <= scenario["time_hour"] <= 8:
                time_multiplier = 1.0
            else:
                time_multiplier = 0.4
            
            # 计算最终金额
            final_amount = base_amount * confidence_multiplier * loss_multiplier * time_multiplier
            final_amount = max(0.1, min(final_amount, 10.0))
            
            results.append({
                "scenario": scenario["description"],
                "calculated_amount": final_amount,
                "details": f"基础{base_amount} × 置信度{confidence_multiplier} × 连败{loss_multiplier} × 时间{time_multiplier}"
            })
        
        return results
    
    # 获取动态计算结果
    dynamic_results = simulate_dynamic_amount_calculation()
    
    # 模拟API客户端
    class IntegrationAPIClient:
        def place_bet(self, room, amount):
            class Result:
                def __init__(self):
                    self.success = True
                    self.data = {'room': room, 'amount': amount}
            return Result()
    
    # 创建智能投注处理器
    handler = SmartBettingHandler(IntegrationAPIClient())
    
    print(f"\n📊 动态金额处理结果:")
    
    for i, result in enumerate(dynamic_results, 1):
        print(f"\n场景{i}: {result['scenario']}")
        print(f"动态计算: {result['calculated_amount']:.3f}元")
        print(f"计算过程: {result['details']}")
        
        # 获取最优调整建议
        optimal_amount, adjustment = handler.get_optimal_amount_adjustment(result['calculated_amount'])
        print(f"智能调整: {optimal_amount:.2f}元 ({adjustment})")
        
        # 显示分解过程
        breakdown = handler.calculate_bet_breakdown(result['calculated_amount'])
        if breakdown:
            print(f"API调用: {len(breakdown)}次 {breakdown}")
        else:
            print(f"API调用: 跳过投注 (金额过小)")

if __name__ == "__main__":
    # 运行演示
    demo_smart_betting()
    demo_integration_with_new_system()
    
    print(f"\n🎊 智能投注处理器演示完成!")
    print(f"💡 关键优势:")
    print(f"   1. 自动将任意金额分解为API支持的固定面额")
    print(f"   2. 智能处理小数部分 (四舍五入逻辑)")
    print(f"   3. 支持批量投注和重试机制")
    print(f"   4. 完美集成到新盈利系统的动态金额算法")
    print(f"   5. 详细的投注过程记录和结果分析")
