#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于机器学习的模式识别
使用神经网络尝试学习随机数生成模式
"""

import numpy as np
from typing import List, Tuple, Dict
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, accuracy_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class MLPatternRecognizer:
    def __init__(self, sequence: List[int]):
        """初始化ML模式识别器"""
        self.sequence = sequence
        self.n = len(sequence)
        
    def create_features(self, window_size: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """创建特征和标签"""
        features = []
        labels = []
        
        for i in range(window_size, self.n):
            # 使用前window_size个数字作为特征
            feature = self.sequence[i-window_size:i]
            
            # 添加统计特征
            feature_extended = feature + [
                np.mean(feature),           # 均值
                np.std(feature),            # 标准差
                np.max(feature),            # 最大值
                np.min(feature),            # 最小值
                len(set(feature)),          # 唯一值数量
                sum(1 for j in range(1, len(feature)) 
                    if feature[j] > feature[j-1]),  # 上升趋势计数
            ]
            
            features.append(feature_extended)
            labels.append(self.sequence[i])
        
        return np.array(features), np.array(labels)
    
    def test_neural_network(self, window_size: int = 10) -> Dict:
        """测试神经网络预测"""
        print(f"=== 神经网络预测测试 (窗口大小: {window_size}) ===")
        
        X, y = self.create_features(window_size)
        
        # 分割训练和测试集
        split_point = int(len(X) * 0.8)
        X_train, X_test = X[:split_point], X[split_point:]
        y_train, y_test = y[:split_point], y[split_point:]
        
        # 标准化特征
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 训练神经网络
        models = {
            'MLP_Small': MLPRegressor(hidden_layer_sizes=(50,), max_iter=1000, random_state=42),
            'MLP_Medium': MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42),
            'MLP_Large': MLPRegressor(hidden_layer_sizes=(200, 100, 50), max_iter=1000, random_state=42),
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"\n训练 {name}...")
            
            try:
                model.fit(X_train_scaled, y_train)
                
                # 预测
                y_pred = model.predict(X_test_scaled)
                y_pred_rounded = np.round(np.clip(y_pred, 1, 8)).astype(int)
                
                # 计算准确率
                accuracy = accuracy_score(y_test, y_pred_rounded)
                mse = mean_squared_error(y_test, y_pred)
                
                print(f"  准确率: {accuracy:.4f}")
                print(f"  MSE: {mse:.4f}")
                
                # 预测下一个数字
                last_window = self.sequence[-window_size:]
                last_feature = last_window + [
                    np.mean(last_window),
                    np.std(last_window),
                    np.max(last_window),
                    np.min(last_window),
                    len(set(last_window)),
                    sum(1 for j in range(1, len(last_window)) 
                        if last_window[j] > last_window[j-1]),
                ]
                
                last_feature_scaled = scaler.transform([last_feature])
                next_pred = model.predict(last_feature_scaled)[0]
                next_pred_rounded = int(np.round(np.clip(next_pred, 1, 8)))
                
                print(f"  预测下一个数字: {next_pred_rounded} (原始: {next_pred:.3f})")
                
                results[name] = {
                    'accuracy': accuracy,
                    'mse': mse,
                    'next_prediction': next_pred_rounded,
                    'model': model
                }
                
            except Exception as e:
                print(f"  训练失败: {e}")
                results[name] = {'error': str(e)}
        
        return results
    
    def test_ensemble_methods(self, window_size: int = 10) -> Dict:
        """测试集成方法"""
        print(f"\n=== 集成方法测试 (窗口大小: {window_size}) ===")
        
        X, y = self.create_features(window_size)
        
        # 分割数据
        split_point = int(len(X) * 0.8)
        X_train, X_test = X[:split_point], X[split_point:]
        y_train, y_test = y[:split_point], y[split_point:]
        
        models = {
            'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42),
            'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"\n训练 {name}...")
            
            try:
                model.fit(X_train, y_train)
                
                # 预测
                y_pred = model.predict(X_test)
                y_pred_rounded = np.round(np.clip(y_pred, 1, 8)).astype(int)
                
                # 计算准确率
                accuracy = accuracy_score(y_test, y_pred_rounded)
                mse = mean_squared_error(y_test, y_pred)
                
                print(f"  准确率: {accuracy:.4f}")
                print(f"  MSE: {mse:.4f}")
                
                # 特征重要性
                if hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_
                    top_features = np.argsort(importances)[-5:][::-1]
                    print(f"  重要特征索引: {top_features}")
                    print(f"  重要性分数: {importances[top_features]}")
                
                # 预测下一个数字
                last_window = self.sequence[-window_size:]
                last_feature = last_window + [
                    np.mean(last_window),
                    np.std(last_window),
                    np.max(last_window),
                    np.min(last_window),
                    len(set(last_window)),
                    sum(1 for j in range(1, len(last_window)) 
                        if last_window[j] > last_window[j-1]),
                ]
                
                next_pred = model.predict([last_feature])[0]
                next_pred_rounded = int(np.round(np.clip(next_pred, 1, 8)))
                
                print(f"  预测下一个数字: {next_pred_rounded} (原始: {next_pred:.3f})")
                
                results[name] = {
                    'accuracy': accuracy,
                    'mse': mse,
                    'next_prediction': next_pred_rounded,
                    'feature_importances': importances if hasattr(model, 'feature_importances_') else None
                }
                
            except Exception as e:
                print(f"  训练失败: {e}")
                results[name] = {'error': str(e)}
        
        return results
    
    def sequence_prediction_test(self) -> Dict:
        """序列预测测试"""
        print(f"\n=== 序列预测测试 ===")
        
        results = {}
        
        # 测试不同的窗口大小
        for window_size in [5, 10, 15, 20]:
            print(f"\n测试窗口大小: {window_size}")
            
            if window_size >= self.n:
                print("  窗口大小过大，跳过")
                continue
            
            # 使用简单的线性回归作为基准
            X, y = self.create_features(window_size)
            
            if len(X) < 100:
                print("  数据量不足，跳过")
                continue
            
            # 分割数据
            split_point = int(len(X) * 0.9)
            X_train, X_test = X[:split_point], X[split_point:]
            y_train, y_test = y[:split_point], y[split_point:]
            
            # 使用随机森林（相对稳定）
            model = RandomForestRegressor(n_estimators=50, random_state=42)
            model.fit(X_train, y_train)
            
            y_pred = model.predict(X_test)
            y_pred_rounded = np.round(np.clip(y_pred, 1, 8)).astype(int)
            
            accuracy = accuracy_score(y_test, y_pred_rounded)
            
            print(f"  准确率: {accuracy:.4f}")
            
            results[f'window_{window_size}'] = {
                'accuracy': accuracy,
                'predictions': y_pred_rounded.tolist()[:10],  # 前10个预测
                'actual': y_test.tolist()[:10]  # 前10个实际值
            }
        
        return results
    
    def run_ml_analysis(self) -> Dict:
        """运行ML分析"""
        print("开始机器学习模式识别...")
        print(f"序列长度: {self.n}")
        print(f"序列前20个: {self.sequence[:20]}")
        
        # 各项ML测试
        nn_results = self.test_neural_network()
        ensemble_results = self.test_ensemble_methods()
        sequence_results = self.sequence_prediction_test()
        
        return {
            'neural_networks': nn_results,
            'ensemble_methods': ensemble_results,
            'sequence_prediction': sequence_results
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    recognizer = MLPatternRecognizer(sequence)
    results = recognizer.run_ml_analysis()
    
    print(f"\n=== ML分析总结 ===")
    
    # 找出最佳模型
    best_accuracy = 0
    best_model = None
    
    for category, models in results.items():
        if isinstance(models, dict):
            for model_name, model_results in models.items():
                if isinstance(model_results, dict) and 'accuracy' in model_results:
                    if model_results['accuracy'] > best_accuracy:
                        best_accuracy = model_results['accuracy']
                        best_model = f"{category}_{model_name}"
    
    print(f"最佳模型: {best_model}")
    print(f"最佳准确率: {best_accuracy:.4f}")
    
    if best_accuracy > 0.15:  # 如果准确率超过15%（随机猜测是12.5%）
        print("发现了一定的可预测性！这可能表明存在隐藏的模式。")
    else:
        print("ML方法也无法找到明显的模式，进一步证实了随机数的高质量。")
