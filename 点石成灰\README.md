# LCG随机投注系统 - 完整版

## 🎯 系统简介

这是一个基于线性同余生成器(LCG)算法的智能投注系统，具有以下特点：

- 🧮 **最优随机算法**: 使用LCG算法，历史验证避开率88.21%（超过理论87.5%）
- 💰 **智能动态金额**: 根据连胜连败、风险等级自动调整投注金额
- 📊 **完整记录功能**: 实时记录所有投注数据，生成详细报告
- 📝 **美观报告生成**: 自动生成Markdown格式的分析报告
- ⚠️ **多层风险控制**: 连败保护、余额保护、时间段控制等
- 🎯 **真实投注支持**: 可连接真实API进行投注（需配置）

## 📁 文件结构

```
点石成灰/
├── lcg_betting_system_main.py          # 主程序入口 ⭐
├── optimized_random_betting_system.py  # 核心投注系统
├── api_framework.py                    # API框架
├── enhanced_markdown_reporter.py       # 报告生成器
├── prediction_strategy_adapter.py      # 预测规则适配器
├── profitable_betting_strategy.py      # 盈利策略引擎
├── smart_betting_handler.py           # 智能投注处理器
├── real_time_logger.py                # 实时日志记录
├── game_history.json                  # 历史数据
├── api_config.json                    # API配置
├── reports/                           # 报告输出目录
└── README.md                          # 本说明文件
```

## 🚀 快速开始

### 1. 运行系统

```bash
python lcg_betting_system_main.py
```

### 2. 选择投注模式

- **模拟投注**: 安全的演示模式，不使用真实资金
- **真实投注**: 连接真实API进行投注（⚠️ 需要真实资金）

### 3. 选择运行模式

1. **交互模式**: 手动控制每次投注
2. **演示模式**: 自动运行20期展示功能
3. **快速测试**: 运行测试并生成报告
4. **长期运行模式**: 持续执行投注直到停止条件
5. **持续监控模式**: 等待开奖信号自动投注
6. **真实投注监控模式**: 连接真实API自动投注

## 💡 推荐使用方式

### 新手用户
1. 选择 **模拟投注模式**
2. 选择 **演示模式** 或 **交互模式**
3. 观察系统运行效果和报告生成

### 高级用户
1. 先用模拟模式测试策略
2. 配置真实API参数
3. 使用小额资金测试真实投注
4. 确认无误后使用长期运行模式

## 📊 系统特性详解

### LCG算法优势
- **历史验证**: 基于6108期真实数据验证
- **避开率**: 88.21%（超过理论87.5%）
- **稳定性**: 算法参数经过优化，性能稳定

### 动态金额管理
- **基础金额**: 2元起投
- **马丁格尔策略**: 连败时递增投注
- **连胜奖励**: 连胜3次以上增加投注
- **算法奖励**: 胜率超过88%时额外奖励
- **风险调整**: 根据风险等级调整金额
- **余额保护**: 余额不足时自动减少投注

### 风险控制机制
- **连败保护**: 连续失败5次自动停止
- **日损失限制**: 达到日最大损失自动停止
- **余额保护**: 余额低于安全线时减少投注
- **时间段控制**: 高风险时段智能跳过

## 📈 测试结果示例

最近一次演示运行结果：
- **投注次数**: 20次
- **胜率**: 95.0%（预期88.21%）
- **总盈亏**: +2.20元
- **最长连胜**: 13次
- **风险事件**: 0次

## ⚠️ 重要提醒

1. **投资有风险**: 任何投注都存在风险，请理性投资
2. **小额测试**: 建议先用小额资金测试系统
3. **风险控制**: 严格遵守系统的风险控制机制
4. **定期检查**: 定期查看生成的报告，了解系统表现
5. **及时止损**: 如遇连续失败，及时停止投注

## 🔧 配置说明

### 默认配置
- 基础投注金额: 2元
- 最大投注金额: 20元
- 最大连续失败: 5次
- 日最大损失: 50元
- 初始余额: 200元

### 自定义配置
可以在 `get_default_config()` 方法中修改这些参数。

## 📝 报告说明

系统会自动生成两种格式的报告：
- **Markdown报告**: 包含详细分析和图表
- **JSON数据**: 原始数据，便于进一步分析

报告保存在 `reports/` 目录中，文件名包含时间戳。

## 🆘 常见问题

### Q: 系统提示"真实投注组件未找到"
A: 这是正常的，系统会自动切换到模拟投注模式。

### Q: 如何查看详细的投注记录？
A: 查看 `reports/` 目录中生成的Markdown报告文件。

### Q: 系统什么时候会自动停止？
A: 当触发风险控制条件时，如连续失败5次、日损失达到上限等。

### Q: 可以修改投注参数吗？
A: 可以，修改 `get_default_config()` 方法中的参数。

## 📞 技术支持

如有问题，请查看：
1. 生成的报告文件
2. 控制台输出信息
3. 系统状态文件（system_state_*.json）

---

**祝您使用愉快！记住：理性投资，风险自控！** 🎲
