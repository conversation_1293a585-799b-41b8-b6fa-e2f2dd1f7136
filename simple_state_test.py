#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的状态持久化测试
"""

import os
import json
from datetime import datetime

def test_state_file():
    """测试状态文件创建"""
    
    print("🧪 测试状态文件功能")
    
    # 创建测试状态文件
    state_file = f"system_state_{datetime.now().strftime('%Y%m%d')}.json"
    
    # 测试数据
    test_state = {
        'consecutive_losses': 2,
        'consecutive_wins': 0,
        'daily_loss': 5.0,
        'total_profit': -5.0,
        'current_balance': 145.0,
        'total_bets': 2,
        'total_wins': 0,
        'last_update': datetime.now().isoformat()
    }
    
    # 保存状态
    try:
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(test_state, f, indent=2, ensure_ascii=False)
        print(f"✅ 状态文件创建成功: {state_file}")
    except Exception as e:
        print(f"❌ 状态文件创建失败: {e}")
        return
    
    # 读取状态
    try:
        with open(state_file, 'r', encoding='utf-8') as f:
            loaded_state = json.load(f)
        print(f"✅ 状态文件读取成功")
        print(f"   连败: {loaded_state['consecutive_losses']}次")
        print(f"   连胜: {loaded_state['consecutive_wins']}次")
        print(f"   余额: {loaded_state['current_balance']:.2f}元")
    except Exception as e:
        print(f"❌ 状态文件读取失败: {e}")
        return
    
    # 清理
    try:
        os.remove(state_file)
        print(f"🗑️ 清理测试文件: {state_file}")
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

if __name__ == "__main__":
    test_state_file()
    print("🎉 简单测试完成!")
