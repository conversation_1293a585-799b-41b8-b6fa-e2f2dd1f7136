#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度统计分析
寻找隐藏的统计模式和弱点
"""

import numpy as np
from typing import List, Tuple, Dict
from collections import Counter, defaultdict
import itertools

class DeepStatisticalAnalyzer:
    def __init__(self, sequence: List[int]):
        """初始化深度统计分析器"""
        self.sequence = sequence
        self.n = len(sequence)
        
    def higher_order_differences(self) -> Dict:
        """高阶差分分析"""
        print("=== 高阶差分分析 ===")
        
        results = {}
        current_seq = self.sequence.copy()
        
        for order in range(1, 6):
            # 计算差分
            differences = []
            for i in range(len(current_seq) - 1):
                diff = (current_seq[i+1] - current_seq[i]) % 8
                differences.append(diff)
            
            # 分析差分的统计特性
            diff_counter = Counter(differences)
            entropy = -sum((count / len(differences)) * np.log2(count / len(differences)) 
                          for count in diff_counter.values() if count > 0)
            
            print(f"{order}阶差分:")
            print(f"  长度: {len(differences)}")
            print(f"  熵: {entropy:.3f}")
            print(f"  分布: {dict(diff_counter)}")
            
            # 检查是否有周期性
            if len(differences) > 10:
                for period in range(2, min(20, len(differences) // 3)):
                    matches = sum(1 for i in range(len(differences) - period) 
                                if differences[i] == differences[i + period])
                    match_rate = matches / (len(differences) - period)
                    if match_rate > 0.3:
                        print(f"  发现周期性: 周期={period}, 匹配率={match_rate:.3f}")
            
            results[f'order_{order}'] = {
                'differences': differences,
                'entropy': entropy,
                'distribution': dict(diff_counter)
            }
            
            current_seq = differences
            if len(current_seq) < 10:
                break
        
        return results
    
    def spectral_analysis_detailed(self) -> Dict:
        """详细频谱分析"""
        print(f"\n=== 详细频谱分析 ===")
        
        # 将序列转换为复数
        seq_complex = np.array(self.sequence, dtype=complex)
        
        # FFT分析
        fft_result = np.fft.fft(seq_complex)
        power_spectrum = np.abs(fft_result) ** 2
        phase_spectrum = np.angle(fft_result)
        
        # 找出显著的频率成分
        freqs = np.fft.fftfreq(len(seq_complex))
        
        # 排除DC分量，找出功率最大的频率
        significant_freqs = []
        for i in range(1, len(power_spectrum) // 2):
            if power_spectrum[i] > np.mean(power_spectrum) * 3:  # 超过平均值3倍
                significant_freqs.append((freqs[i], power_spectrum[i], phase_spectrum[i]))
        
        significant_freqs.sort(key=lambda x: x[1], reverse=True)
        
        print(f"发现 {len(significant_freqs)} 个显著频率成分:")
        for freq, power, phase in significant_freqs[:5]:
            period = 1/freq if freq != 0 else float('inf')
            print(f"  频率: {freq:.6f}, 周期: {period:.2f}, 功率: {power:.2e}, 相位: {phase:.3f}")
        
        return {
            'significant_frequencies': significant_freqs,
            'power_spectrum': power_spectrum,
            'phase_spectrum': phase_spectrum
        }
    
    def conditional_probability_analysis(self) -> Dict:
        """条件概率分析"""
        print(f"\n=== 条件概率分析 ===")
        
        results = {}
        
        # 分析不同条件下的概率分布
        for condition_length in range(1, 5):
            print(f"\n{condition_length}元条件概率:")
            
            conditional_probs = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                conditional_probs[condition][next_val] += 1
                condition_counts[condition] += 1
            
            # 转换为概率并寻找异常
            anomalies = []
            for condition, next_counts in conditional_probs.items():
                total = condition_counts[condition]
                if total >= 3:  # 至少出现3次才考虑
                    probs = {next_val: count/total for next_val, count in next_counts.items()}
                    
                    # 检查是否有明显偏向
                    max_prob = max(probs.values())
                    if max_prob > 0.6:  # 超过60%的概率
                        anomalies.append((condition, probs, total))
            
            if anomalies:
                print(f"  发现 {len(anomalies)} 个条件概率异常:")
                for condition, probs, total in sorted(anomalies, key=lambda x: max(x[1].values()), reverse=True)[:5]:
                    max_next = max(probs.keys(), key=lambda k: probs[k])
                    print(f"    条件 {condition} -> {max_next}: {probs[max_next]:.3f} (样本数: {total})")
            
            results[f'length_{condition_length}'] = {
                'anomalies': anomalies,
                'total_conditions': len(conditional_probs)
            }
        
        return results
    
    def gap_analysis(self) -> Dict:
        """间隔分析"""
        print(f"\n=== 间隔分析 ===")
        
        results = {}
        
        for target_value in range(1, 9):
            # 找出目标值的所有位置
            positions = [i for i, val in enumerate(self.sequence) if val == target_value]
            
            if len(positions) < 2:
                continue
            
            # 计算间隔
            gaps = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
            
            # 分析间隔的统计特性
            gap_counter = Counter(gaps)
            mean_gap = np.mean(gaps)
            std_gap = np.std(gaps)
            
            print(f"数字 {target_value}:")
            print(f"  出现次数: {len(positions)}")
            print(f"  平均间隔: {mean_gap:.2f}")
            print(f"  间隔标准差: {std_gap:.2f}")
            
            # 检查间隔是否有规律
            if len(set(gaps)) < len(gaps) * 0.7:  # 如果重复间隔较多
                common_gaps = gap_counter.most_common(3)
                print(f"  常见间隔: {common_gaps}")
            
            results[f'value_{target_value}'] = {
                'positions': positions,
                'gaps': gaps,
                'mean_gap': mean_gap,
                'std_gap': std_gap,
                'gap_distribution': dict(gap_counter)
            }
        
        return results
    
    def run_deep_analysis(self) -> Dict:
        """运行深度分析"""
        print("开始深度统计分析...")
        print(f"序列长度: {self.n}")
        print(f"序列前20个: {self.sequence[:20]}")
        print()
        
        # 各项深度分析
        diff_results = self.higher_order_differences()
        spectral_results = self.spectral_analysis_detailed()
        conditional_results = self.conditional_probability_analysis()
        gap_results = self.gap_analysis()
        
        return {
            'differences': diff_results,
            'spectral': spectral_results,
            'conditional': conditional_results,
            'gaps': gap_results
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    # 分析大数据集
    sequence = load_sequence("随机2000个数字.txt")
    analyzer = DeepStatisticalAnalyzer(sequence)
    results = analyzer.run_deep_analysis()
    
    print(f"\n=== 深度分析总结 ===")
    print("如果发现了任何统计异常或模式，可能提供逆向的线索。")
    print("否则，这进一步证实了随机数的高质量。")
