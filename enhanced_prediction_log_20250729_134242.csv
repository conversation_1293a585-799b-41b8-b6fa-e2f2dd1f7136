时间,期号,预测房间,预测置信度,规则类型,可选房间,投注房间,选择策略,投注金额,开奖房间,投注结果,盈亏,累计盈亏,胜率
20:44:42,126909,7,0.720,static,"[1, 2, 3, 4, 5, 6, 8]",3,频率相同，选择中位数,1.00,2,获胜,+0.10,+0.10,100.0%
20:50:33,126913,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",1,频率最低,1.00,3,获胜,+0.10,+0.20,100.0%
21:10:00,126926,8,1.000,static,"[1, 2, 3, 4, 5, 6, 7]",7,频率最低,1.00,4,获胜,+0.10,+0.30,100.0%
21:14:28,126929,4,0.667,dynamic,"[1, 2, 3, 5, 6, 7, 8]",7,频率最低,1.00,6,获胜,+0.10,+0.40,100.0%
21:47:08,126951,4,0.750,static,"[1, 2, 3, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,7,获胜,+0.10,+0.50,100.0%
22:06:26,126964,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",5,频率相同，选择中位数,1.00,6,获胜,+0.10,+0.60,100.0%
22:12:22,126968,4,0.600,dynamic,"[1, 2, 3, 5, 6, 7, 8]",5,频率最低,1.00,3,获胜,+0.10,+0.70,100.0%
22:13:50,126969,5,0.780,static,"[1, 2, 3, 4, 6, 7, 8]",4,频率最低,1.00,7,获胜,+0.10,+0.80,100.0%
22:18:24,126972,4,0.750,static,"[1, 2, 3, 5, 6, 7, 8]",5,频率最低,1.00,7,获胜,+0.10,+0.90,100.0%
22:22:58,126975,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,+1.00,100.0%
22:25:54,126977,7,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 8]",5,频率相同，选择中位数,1.00,6,获胜,+0.10,+1.10,100.0%
22:49:36,126993,3,0.600,dynamic,"[1, 2, 4, 5, 6, 7, 8]",5,频率最低,1.00,4,获胜,+0.10,+1.20,100.0%
23:03:01,127002,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,4,获胜,+0.10,+1.30,100.0%
23:25:29,127017,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",1,频率最低,1.00,8,获胜,+0.10,+1.40,100.0%
23:26:57,127018,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",1,频率最低,1.00,7,获胜,+0.10,+1.50,100.0%
23:28:30,127019,8,0.750,dynamic,"[1, 2, 3, 4, 5, 6, 7]",1,频率最低,1.00,7,获胜,+0.10,+1.60,100.0%
23:37:27,127025,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",1,频率最低,1.00,5,获胜,+0.10,+1.70,100.0%
23:55:14,127037,5,0.600,dynamic,"[1, 2, 3, 4, 6, 7, 8]",1,频率最低,1.00,5,获胜,+0.10,+1.80,100.0%
00:04:17,127043,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",2,频率最低,1.00,3,获胜,+0.10,+1.90,100.0%
00:10:22,127047,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",2,频率最低,1.00,5,获胜,+0.10,+2.00,100.0%
00:19:27,127053,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,4,获胜,+0.10,+2.10,100.0%
00:23:53,127056,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,1,获胜,+0.10,+2.20,100.0%
00:37:40,127065,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",2,频率最低,1.00,3,获胜,+0.10,+2.30,100.0%
01:03:20,127082,1,0.750,dynamic,"[2, 3, 4, 5, 6, 7, 8]",8,频率最低,1.00,1,获胜,+0.10,+2.40,100.0%
01:07:48,127085,5,0.750,dynamic,"[1, 2, 3, 4, 6, 7, 8]",4,频率最低,1.00,7,获胜,+0.10,+2.50,100.0%
01:27:30,127098,3,0.600,dynamic,"[1, 2, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,4,失败,-1.00,+1.50,96.2%
01:38:06,127105,1,0.800,dynamic,"[2, 3, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,4,获胜,+0.10,+1.60,96.3%
02:38:17,127144,2,0.600,dynamic,"[1, 3, 4, 5, 6, 7, 8]",3,频率相同，选择中位数,1.00,2,获胜,+0.10,+1.70,96.4%
03:00:45,127158,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率相同，选择中位数,1.00,6,获胜,+0.10,+1.80,96.6%
03:14:49,127166,4,0.750,static,"[1, 2, 3, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,1,获胜,+0.10,+1.90,96.7%
03:27:56,127174,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",4,频率最低,1.00,8,获胜,+0.10,+2.00,96.8%
03:34:50,127178,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,5,获胜,+0.10,+2.10,96.9%
03:54:56,127189,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",4,频率最低,1.00,5,获胜,+0.10,+2.20,97.0%
04:41:39,127212,8,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 7]",3,频率相同，选择中位数,1.00,4,获胜,+0.10,+2.30,97.1%
05:16:26,127227,8,1.000,static,"[1, 2, 3, 4, 5, 6, 7]",6,频率相同，选择中位数,1.00,2,获胜,+0.10,+2.40,97.1%
05:18:56,127228,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",6,频率最低,1.00,1,获胜,+0.10,+2.50,97.2%
05:59:57,127248,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",6,频率最低,1.00,4,获胜,+0.10,+2.60,97.3%
06:10:34,127254,7,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率相同，选择中位数,1.00,1,获胜,+0.10,+2.70,97.4%
06:36:31,127269,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",4,频率最低,1.00,3,获胜,+0.10,+2.80,97.4%
07:05:40,127286,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",2,频率最低,1.00,5,获胜,+0.10,+2.90,97.5%
08:07:52,127325,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,3,失败,-1.00,+1.90,95.1%
08:12:41,127328,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,4,获胜,+0.10,+2.00,95.2%
08:31:28,127340,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,3,获胜,+0.10,+2.10,95.3%
08:42:02,127347,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,2,获胜,+0.10,+2.20,95.5%
08:51:10,127353,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",8,频率相同，选择中位数,1.00,2,获胜,+0.10,+2.30,95.6%
09:11:09,127366,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,1,获胜,+0.10,+2.40,95.7%
09:24:55,127375,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",5,频率相同，选择中位数,1.00,6,获胜,+0.10,+2.50,95.7%
09:56:37,127396,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,2,获胜,+0.10,+2.60,95.8%
10:21:13,127412,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,6,失败,-1.00,+1.60,93.9%
10:24:15,127414,5,0.600,dynamic,"[1, 2, 3, 4, 6, 7, 8]",4,频率相同，选择中位数,1.00,3,获胜,+0.10,+1.70,94.0%
10:27:22,127416,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",7,频率相同，选择中位数,1.00,1,获胜,+0.10,+1.80,94.1%
10:44:08,127427,3,0.600,dynamic,"[1, 2, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,6,获胜,+0.10,+1.90,94.2%
11:00:40,127438,3,0.710,static,"[1, 2, 4, 5, 6, 7, 8]",5,频率最低,1.00,1,获胜,+0.10,+2.00,94.3%
11:06:48,127442,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",3,频率最低,1.00,1,获胜,+0.10,+2.10,94.4%
11:08:21,127443,1,1.000,static,"[2, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,7,获胜,+0.10,+2.20,94.5%
11:23:29,127453,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",6,频率最低,1.00,2,获胜,+0.10,+2.30,94.6%
11:34:02,127460,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",6,频率最低,1.00,1,获胜,+0.10,+2.40,94.7%
12:01:04,127478,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,7,获胜,+0.10,+2.50,94.8%
12:13:07,127486,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,5,获胜,+0.10,+2.60,94.9%
12:22:05,127492,8,0.850,static,"[1, 2, 3, 4, 5, 6, 7]",3,频率最低,1.00,4,获胜,+0.10,+2.70,95.0%
12:23:38,127493,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",3,频率最低,1.00,2,获胜,+0.10,+2.80,95.1%
12:25:06,127494,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",3,频率最低,1.00,3,失败,-1.00,+1.80,93.5%
12:35:36,127501,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,6,获胜,+0.10,+1.90,93.7%
12:46:17,127508,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",8,频率最低,1.00,8,失败,-1.00,+0.90,92.2%
13:15:57,127528,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",4,频率最低,1.00,4,失败,-1.00,-0.10,90.8%
13:20:36,127531,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",7,频率最低,1.00,4,获胜,+0.10,+0.00,90.9%
13:56:50,127555,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",4,频率最低,1.00,1,获胜,+0.10,+0.10,91.0%
14:05:58,127561,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",8,频率相同，选择中位数,1.00,6,获胜,+0.10,+0.20,91.2%
14:17:51,127569,8,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 7]",3,频率最低,1.00,1,获胜,+0.10,+0.30,91.3%
14:26:54,127575,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,5,获胜,+0.10,+0.40,91.4%
14:38:54,127583,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,6,获胜,+0.10,+0.50,91.5%
14:53:59,127593,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率最低,1.00,6,获胜,+0.10,+0.60,91.7%
14:55:32,127594,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",8,频率最低,1.00,5,获胜,+0.10,+0.70,91.8%
15:01:30,127598,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率最低,1.00,6,获胜,+0.10,+0.80,91.9%
15:12:05,127605,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",4,频率相同，选择中位数,1.00,1,获胜,+0.10,+0.90,92.0%
15:37:36,127622,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,6,获胜,+0.10,+1.00,92.1%
15:46:39,127628,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",7,频率最低,1.00,3,获胜,+0.10,+1.10,92.2%
16:06:18,127641,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,4,获胜,+0.10,+1.20,92.3%
16:56:57,127674,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,7,获胜,+0.10,+1.30,92.4%
17:04:33,127679,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,2,获胜,+0.10,+1.40,92.5%
17:07:35,127681,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,3,获胜,+0.10,+1.50,92.6%
17:13:47,127685,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,6,获胜,+0.10,+1.60,92.7%
17:35:29,127699,4,0.667,dynamic,"[1, 2, 3, 5, 6, 7, 8]",1,频率最低,1.00,5,获胜,+0.10,+1.70,92.8%
17:49:03,127708,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",1,频率最低,1.00,8,获胜,+0.10,+1.80,92.9%
18:14:49,127725,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",7,频率相同，选择中位数,1.00,2,获胜,+0.10,+1.90,92.9%
18:28:33,127734,6,0.950,static,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,6,获胜,+0.10,+2.00,93.0%
18:48:20,127747,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率最低,1.00,6,获胜,+0.10,+2.10,93.1%
18:55:56,127752,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",8,频率最低,1.00,8,失败,-1.00,+1.10,92.0%
19:00:29,127755,1,0.910,static,"[2, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,8,失败,-1.00,+0.10,91.0%
19:12:38,127763,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,4,失败,-1.00,-0.90,90.0%
19:15:44,127765,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,3,获胜,+0.10,-0.80,90.1%
19:17:17,127766,5,0.750,dynamic,"[1, 2, 3, 4, 6, 7, 8]",7,频率最低,1.00,1,获胜,+0.10,-0.70,90.2%
19:37:14,127779,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",4,频率相同，选择中位数,1.00,6,获胜,+0.10,-0.60,90.3%
19:46:17,127785,1,1.000,static,"[2, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,5,失败,-1.00,-1.60,89.4%
20:12:05,127802,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,2,获胜,+0.10,-1.50,89.5%
20:20:53,127808,3,0.710,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,6,获胜,+0.10,-1.40,89.6%
20:30:00,127814,8,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 7]",4,频率相同，选择中位数,1.00,2,获胜,+0.10,-1.30,89.7%
20:49:47,127827,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,-1.20,89.8%
21:14:10,127843,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",6,频率最低,1.00,2,获胜,+0.10,-1.10,89.9%
21:41:18,127861,6,1.000,static,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,5,获胜,+0.10,-1.00,90.0%
21:51:45,127868,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",2,频率最低,1.00,5,获胜,+0.10,-0.90,90.1%
22:08:09,127879,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",2,频率最低,1.00,2,失败,-1.00,-1.90,89.2%
22:20:14,127887,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",3,频率相同，选择中位数,1.00,8,获胜,+0.10,-1.80,89.3%
22:30:42,127894,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",2,频率相同，选择中位数,1.00,4,获胜,+0.10,-1.70,89.4%
22:44:19,127903,6,0.625,dynamic,"[1, 2, 3, 4, 5, 7, 8]",3,频率最低,1.00,1,获胜,+0.10,-1.60,89.5%
23:11:18,127921,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,3,获胜,+0.10,-1.50,89.6%
23:17:15,127925,5,0.900,static,"[1, 2, 3, 4, 6, 7, 8]",8,频率相同，选择中位数,1.00,7,获胜,+0.10,-1.40,89.7%
23:52:10,127948,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,3,获胜,+0.10,-1.30,89.8%
00:27:19,127971,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,3,获胜,+0.10,-1.20,89.9%
00:28:49,127972,7,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 8]",5,频率相同，选择中位数,1.00,1,获胜,+0.10,-1.10,90.0%
00:39:28,127979,8,0.740,static,"[1, 2, 3, 4, 5, 6, 7]",7,频率最低,1.00,6,获胜,+0.10,-1.00,90.1%
00:40:56,127980,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",7,频率最低,1.00,5,获胜,+0.10,-0.90,90.2%
01:17:39,128004,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",4,频率相同，选择中位数,1.00,8,获胜,+0.10,-0.80,90.3%
01:23:56,128008,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,8,获胜,+0.10,-0.70,90.4%
01:36:46,128016,5,0.780,static,"[1, 2, 3, 4, 6, 7, 8]",7,频率最低,1.00,8,获胜,+0.10,-0.60,90.4%
01:49:39,128024,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.50,90.5%
02:40:22,128055,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,8,获胜,+0.10,-0.40,90.6%
02:59:14,128066,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",4,频率相同，选择中位数,1.00,8,获胜,+0.10,-0.30,90.7%
03:23:53,128079,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",6,频率最低,1.00,8,获胜,+0.10,-0.20,90.8%
03:57:05,128096,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",1,频率最低,1.00,6,获胜,+0.10,-0.10,90.8%
05:56:01,128129,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,6,获胜,+0.10,+0.00,90.9%
06:28:04,128141,4,0.600,dynamic,"[1, 2, 3, 5, 6, 7, 8]",8,频率最低,1.00,1,获胜,+0.10,+0.10,91.0%
07:03:21,128159,2,0.870,static,"[1, 3, 4, 5, 6, 7, 8]",8,频率最低,1.00,7,获胜,+0.10,+0.20,91.1%
07:39:14,128180,1,0.910,static,"[2, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,7,获胜,+0.10,+0.30,91.1%
07:49:26,128186,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",8,频率最低,1.00,1,获胜,+0.10,+0.40,91.2%
08:14:48,128202,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,4,获胜,+0.10,+0.50,91.3%
08:42:49,128220,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",3,频率相同，选择中位数,1.00,8,获胜,+0.10,+0.60,91.3%
09:30:35,128251,1,0.625,dynamic,"[2, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,3,获胜,+0.10,+0.70,91.4%
09:49:05,128263,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,5,失败,-1.00,-0.30,90.7%
10:07:35,128275,8,0.850,static,"[1, 2, 3, 4, 5, 6, 7]",4,频率相同，选择中位数,1.00,1,获胜,+0.10,-0.20,90.8%
10:12:20,128278,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",3,频率最低,1.00,7,获胜,+0.10,-0.10,90.8%
10:21:29,128284,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,8,获胜,+0.10,+0.00,90.9%
11:25:15,128326,8,0.850,static,"[1, 2, 3, 4, 5, 6, 7]",4,频率相同，选择中位数,1.00,8,获胜,+0.10,+0.10,91.0%
11:51:03,128343,2,0.600,dynamic,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,5,获胜,+0.10,+0.20,91.0%
11:54:00,128345,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",4,频率相同，选择中位数,1.00,4,失败,-1.00,-0.80,90.4%
12:40:11,128375,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",6,频率相同，选择中位数,1.00,3,获胜,+0.10,-0.70,90.4%
12:41:44,128376,5,0.600,dynamic,"[1, 2, 3, 4, 6, 7, 8]",6,频率相同，选择中位数,1.00,6,失败,-1.00,-1.70,89.8%
13:13:27,128397,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,5,获胜,+0.10,-1.60,89.9%
13:34:45,128411,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",1,频率最低,1.00,1,失败,-1.00,-2.60,89.2%
13:57:29,128426,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,2,获胜,+0.10,-2.50,89.3%
14:00:36,128428,5,0.780,static,"[1, 2, 3, 4, 6, 7, 8]",6,频率最低,1.00,8,获胜,+0.10,-2.40,89.4%
14:29:13,128447,5,0.900,static,"[1, 2, 3, 4, 6, 7, 8]",4,频率相同，选择中位数,1.00,4,失败,-1.00,-3.40,88.7%
14:45:46,128458,6,0.600,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,8,获胜,+0.10,-3.30,88.8%
15:21:03,128481,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,8,获胜,+0.10,-3.20,88.9%
16:11:27,128514,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",6,频率最低,1.00,6,失败,-1.00,-4.20,88.3%
16:18:58,128519,8,0.850,static,"[1, 2, 3, 4, 5, 6, 7]",3,频率相同，选择中位数,1.00,6,获胜,+0.10,-4.10,88.4%
16:23:27,128522,8,0.850,static,"[1, 2, 3, 4, 5, 6, 7]",4,频率最低,1.00,1,获胜,+0.10,-4.00,88.4%
16:26:30,128524,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",4,频率最低,1.00,5,获胜,+0.10,-3.90,88.5%
16:41:35,128534,5,0.625,dynamic,"[1, 2, 3, 4, 6, 7, 8]",7,频率相同，选择中位数,1.00,1,获胜,+0.10,-3.80,88.6%
16:53:57,128542,2,0.870,static,"[1, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,7,获胜,+0.10,-3.70,88.7%
17:09:11,128552,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,3,获胜,+0.10,-3.60,88.7%
18:19:31,128599,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,6,获胜,+0.10,-3.50,88.8%
18:30:03,128606,8,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 7]",2,频率相同，选择中位数,1.00,4,获胜,+0.10,-3.40,88.9%
18:51:10,128620,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,1,获胜,+0.10,-3.30,89.0%
19:28:48,128645,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",3,频率最低,1.00,4,获胜,+0.10,-3.20,89.0%
19:31:54,128647,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率最低,1.00,1,获胜,+0.10,-3.10,89.1%
19:34:55,128649,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",6,频率相同，选择中位数,1.00,8,获胜,+0.10,-3.00,89.2%
19:57:25,128664,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",6,频率相同，选择中位数,1.00,1,获胜,+0.10,-2.90,89.2%
20:18:27,128678,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",6,频率最低,1.00,6,失败,-1.00,-3.90,88.7%
20:54:15,128702,7,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率相同，选择中位数,1.00,6,获胜,+0.10,-3.80,88.8%
21:19:59,128719,5,0.780,static,"[1, 2, 3, 4, 6, 7, 8]",8,频率最低,1.00,5,获胜,+0.10,-3.70,88.8%
21:35:08,128729,1,0.910,static,"[2, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,6,获胜,+0.10,-3.60,88.9%
21:55:41,128743,2,0.870,static,"[1, 3, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,1,获胜,+0.10,-3.50,89.0%
22:09:05,128752,4,0.600,dynamic,"[1, 2, 3, 5, 6, 7, 8]",3,频率最低,1.00,4,获胜,+0.10,-3.40,89.0%
22:13:34,128755,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率最低,1.00,8,获胜,+0.10,-3.30,89.1%
22:22:18,128761,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,5,获胜,+0.10,-3.20,89.2%
22:23:51,128762,6,0.750,dynamic,"[1, 2, 3, 4, 5, 7, 8]",3,频率最低,1.00,1,获胜,+0.10,-3.10,89.2%
22:34:08,128769,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,1,获胜,+0.10,-3.00,89.3%
22:41:35,128774,7,0.625,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率最低,1.00,4,获胜,+0.10,-2.90,89.3%
22:47:35,128778,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",3,频率最低,1.00,8,获胜,+0.10,-2.80,89.4%
23:29:17,128806,6,0.950,static,"[1, 2, 3, 4, 5, 7, 8]",5,频率最低,1.00,6,获胜,+0.10,-2.70,89.5%
23:35:11,128810,2,0.870,static,"[1, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,1,获胜,+0.10,-2.60,89.5%
23:39:52,128813,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",4,频率相同，选择中位数,1.00,5,获胜,+0.10,-2.50,89.6%
23:57:46,128825,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,1,获胜,+0.10,-2.40,89.7%
23:59:19,128826,4,0.750,static,"[1, 2, 3, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,3,获胜,+0.10,-2.30,89.7%
00:00:47,128827,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",5,频率最低,1.00,1,获胜,+0.10,-2.20,89.8%
00:03:54,128829,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",5,频率最低,1.00,7,获胜,+0.10,-2.10,89.8%
00:35:24,128850,5,0.900,static,"[1, 2, 3, 4, 6, 7, 8]",4,频率最低,1.00,6,获胜,+0.10,-2.00,89.9%
01:25:51,128883,8,0.850,static,"[1, 2, 3, 4, 5, 6, 7]",7,频率最低,1.00,2,获胜,+0.10,-1.90,89.9%
01:39:32,128892,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",7,频率最低,1.00,1,获胜,+0.10,-1.80,90.0%
01:42:39,128894,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,7,失败,-1.00,-2.80,89.5%
01:45:47,128896,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,2,获胜,+0.10,-2.70,89.6%
01:47:15,128897,4,0.667,dynamic,"[1, 2, 3, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,3,获胜,+0.10,-2.60,89.6%
02:21:21,128919,7,0.720,static,"[1, 2, 3, 4, 5, 6, 8]",5,频率相同，选择中位数,1.00,4,获胜,+0.10,-2.50,89.7%
02:49:49,128937,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",2,频率最低,1.00,3,获胜,+0.10,-2.40,89.7%
02:59:34,128943,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",2,频率相同，选择中位数,1.00,4,获胜,+0.10,-2.30,89.8%
03:02:45,128945,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",1,频率最低,1.00,1,失败,-1.00,-3.30,89.3%
03:09:24,128949,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,8,失败,-1.00,-4.30,88.8%
03:14:19,128952,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,4,获胜,+0.10,-4.20,88.9%
03:16:03,128953,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",2,频率最低,1.00,3,获胜,+0.10,-4.10,88.9%
03:22:42,128957,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",2,频率最低,1.00,2,失败,-1.00,-5.10,88.5%
03:37:47,128966,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,7,获胜,+0.10,-5.00,88.5%
03:39:26,128967,3,0.710,static,"[1, 2, 4, 5, 6, 7, 8]",1,频率最低,1.00,3,获胜,+0.10,-4.90,88.6%
03:50:08,128973,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",1,频率最低,1.00,1,失败,-1.00,-5.90,88.1%
04:11:01,128984,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,2,获胜,+0.10,-5.80,88.2%
04:59:53,129008,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,1,获胜,+0.10,-5.70,88.3%
05:24:26,129019,5,0.900,static,"[1, 2, 3, 4, 6, 7, 8]",7,频率相同，选择中位数,1.00,8,获胜,+0.10,-5.60,88.3%
07:14:28,129076,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",2,频率最低,1.00,5,获胜,+0.10,-5.50,88.4%
07:17:49,129078,3,0.600,dynamic,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,8,获胜,+0.10,-5.40,88.4%
07:27:59,129084,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",4,频率相同，选择中位数,1.00,1,获胜,+0.10,-5.30,88.5%
07:49:24,129097,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,5,获胜,+0.10,-5.20,88.6%
08:00:25,129104,7,0.720,static,"[1, 2, 3, 4, 5, 6, 8]",8,频率相同，选择中位数,1.00,1,获胜,+0.10,-5.10,88.6%
08:05:09,129107,5,0.780,static,"[1, 2, 3, 4, 6, 7, 8]",7,频率相同，选择中位数,1.00,8,获胜,+0.10,-5.00,88.7%
08:20:51,129117,7,0.720,static,"[1, 2, 3, 4, 5, 6, 8]",6,频率最低,1.00,6,失败,-1.00,-6.00,88.2%
08:48:31,129135,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",5,频率最低,1.00,8,获胜,+0.10,-5.90,88.3%
08:54:33,129139,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",5,频率最低,1.00,8,获胜,+0.10,-5.80,88.3%
08:59:09,129142,1,0.910,static,"[2, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,8,获胜,+0.10,-5.70,88.4%
09:05:22,129146,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,4,获胜,+0.10,-5.60,88.5%
09:22:10,129157,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",7,频率最低,1.00,3,获胜,+0.10,-5.50,88.5%
09:36:04,129166,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",6,频率最低,1.00,4,获胜,+0.10,-5.40,88.6%
09:55:50,129179,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,3,获胜,+0.10,-5.30,88.6%
10:01:48,129183,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",1,频率最低,1.00,8,获胜,+0.10,-5.20,88.7%
10:15:22,129192,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",6,频率最低,1.00,7,获胜,+0.10,-5.10,88.7%
10:46:51,129213,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,4,获胜,+0.10,-5.00,88.8%
10:57:14,129220,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",7,频率相同，选择中位数,1.00,7,失败,-1.00,-6.00,88.4%
11:31:19,129243,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",3,频率最低,1.00,6,获胜,+0.10,-5.90,88.4%
11:34:10,129245,1,0.750,dynamic,"[2, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,3,失败,-1.00,-6.90,88.0%
11:43:04,129251,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,8,获胜,+0.10,-6.80,88.1%
12:10:05,129269,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",8,频率相同，选择中位数,1.00,2,获胜,+0.10,-6.70,88.1%
12:20:38,129276,4,0.750,static,"[1, 2, 3, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,8,失败,-1.00,-7.70,87.7%
12:34:10,129285,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率相同，选择中位数,1.00,4,获胜,+0.10,-7.60,87.8%
12:52:10,129297,3,0.920,static,"[1, 2, 4, 5, 6, 7, 8]",8,频率最低,1.00,5,获胜,+0.10,-7.50,87.8%
