# 🎯 完整自动化投注系统

基于23607个样本的随机数算法逆向工程，实现高精度预测和自动化投注的完整解决方案。

## 📊 系统性能指标

- **样本数量**: 23607个历史开奖数据
- **预测准确率**: 75.61%
- **期望收益率**: +102.68%
- **高置信度规则**: 60个 (置信度≥70%)
- **完美规则**: 15个 (置信度=100%)

## 🏗️ 系统架构

### 核心模块

1. **api_framework.py** - API调用框架
   - 游戏状态监控
   - 自动投注执行
   - 数据获取和解析

2. **prediction_strategy_adapter.py** - 预测策略适配器
   - 60个高精度预测规则
   - 动态置信度评估
   - 投注建议生成

3. **real_time_betting_system.py** - 实时投注系统
   - 自动化投注流程
   - 实时监控和响应
   - 投注记录管理

4. **risk_control_monitor.py** - 风险控制监控
   - 多层风险控制
   - 实时预警系统
   - 自动止损保护

5. **complete_betting_system.py** - 完整系统集成
   - 所有模块整合
   - 一键启动运行
   - 系统状态管理

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install requests pandas matplotlib

# 确保所有文件在同一目录
ls -la *.py
```

### 2. 配置API认证

编辑 `complete_betting_system.py` 中的API配置：

```python
'api': {
    'base_url': 'https://fks-api.lucklyworld.com',  # 实际API地址
    'headers': {
        'User-Agent': 'com.caike.union/5.2.2-official...',
        'userId': '8607652',  # 您的用户ID
        'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',  # 您的token
        # ... 其他认证信息
    }
}
```

### 3. 调整风险参数

```python
'risk_control': {
    'max_daily_loss': 10.0,        # 日最大损失
    'max_consecutive_losses': 3,   # 最大连续失败次数
    'max_single_bet': 1.0,         # 单次最大投注
    'initial_balance': 100.0       # 初始余额
}
```

### 4. 启动系统

```python
python complete_betting_system.py
```

## 🎯 预测策略详解

### 高置信度规则示例

```python
# 100%置信度规则
(6,6,5,1) -> 8  # 当最近4期开出6,6,5,1时，下期100%开出8
(5,7,8,4) -> 2  # 当最近4期开出5,7,8,4时，下期100%开出2
(3,1,3,3) -> 6  # 当最近4期开出3,1,3,3时，下期100%开出6

# 90%+置信度规则
(2,7,6) -> 6    # 95%置信度
(5,1,3) -> 3    # 92%置信度
(8,4,2) -> 1    # 91%置信度
```

### 投注策略

1. **高置信度预测** (≥90%): 投注所有其他7个房间
2. **中高置信度预测** (80-89%): 投注5个房间
3. **中等置信度预测** (70-79%): 投注3个房间

## ⚠️ 风险控制机制

### 多层风险保护

1. **连续失败保护**: 连续失败3次自动停止
2. **日损失限制**: 日损失达到10元自动停止
3. **最大回撤保护**: 回撤超过20%自动停止
4. **余额保护**: 余额低于初始值10%自动停止

### 预警系统

- 🟡 **黄色预警**: 连续失败2次、日损失10%
- 🟠 **橙色预警**: 连续失败3次、日损失15%
- 🔴 **红色预警**: 最大回撤20%、余额不足

## 📈 使用建议

### 初次使用

1. **小额测试**: 建议初始余额10-50元
2. **观察验证**: 运行1-2天观察预测准确率
3. **逐步增加**: 验证有效后逐步增加投注金额

### 最佳实践

1. **定期检查**: 每日查看系统报告
2. **参数调整**: 根据实际表现调整风险参数
3. **及时止损**: 遇到异常情况及时手动停止

### 风险提醒

⚠️ **重要警告**:
- 投注有风险，可能导致资金损失
- 历史表现不代表未来结果
- 建议只投入可承受损失的资金
- 请遵守当地法律法规

## 📊 系统监控

### 实时监控指标

- 当前余额和盈亏
- 预测准确率
- 连续失败次数
- 风险等级评估

### 报告文件

- `betting_log_YYYYMMDD.log` - 详细运行日志
- `session_YYYYMMDD_HHMMSS.json` - 投注会话记录
- `risk_report_YYYYMMDD_HHMMSS.json` - 风险分析报告
- `system_report_YYYYMMDD_HHMMSS.json` - 系统运行报告

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证API地址和认证信息
   - 确认token未过期

2. **预测规则不匹配**
   - 检查历史数据是否充足
   - 验证数据格式正确性
   - 调整最小置信度阈值

3. **系统自动停止**
   - 查看风险控制日志
   - 检查是否触发止损条件
   - 分析失败原因并调整策略

### 技术支持

如遇到技术问题，请检查：
1. 所有Python文件是否在同一目录
2. 依赖包是否正确安装
3. API配置是否完整准确
4. 系统日志中的错误信息

## 📝 更新日志

### v1.0.0 (2025-07-25)
- ✅ 完成23607样本分析
- ✅ 实现60个高精度预测规则
- ✅ 构建完整自动化投注系统
- ✅ 集成多层风险控制机制
- ✅ 实现实时监控和报告功能

## 📄 许可证

本项目仅供学习和研究使用。使用者需自行承担投注风险，开发者不对任何损失负责。

---

**🎯 祝您投注顺利，收益满满！**

*记住：理性投注，风险自控！*
