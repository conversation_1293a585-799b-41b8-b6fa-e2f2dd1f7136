#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试投注系统
检查为什么系统不进行投注
"""

from prediction_strategy_adapter import PredictionRuleAdapter
from risk_control_monitor import RiskControlMonitor

def debug_prediction_system():
    """调试预测系统"""
    
    print("🔍 调试预测系统")
    print("=" * 50)
    
    # 1. 测试预测规则加载
    print("1. 测试预测规则加载...")
    adapter = PredictionRuleAdapter()
    rules = adapter.load_prediction_rules_from_analysis()
    adapter.categorize_rules(rules)
    
    print(f"   高置信度规则: {len(adapter.high_confidence_rules)}")
    print(f"   中等置信度规则: {len(adapter.medium_confidence_rules)}")
    print(f"   备用规则: {len(adapter.backup_rules)}")
    
    # 2. 测试历史数据（从控制台观察到的开奖结果）
    print("\n2. 测试历史数据预测...")
    # 从您的控制台日志中提取的实际开奖数据
    observed_history = [8, 5, 4, 5, 5, 6, 3, 2, 7, 1]  # 最近10期的开奖结果
    
    print(f"   历史数据: {observed_history}")
    print(f"   数据长度: {len(observed_history)}")
    
    # 3. 测试不同置信度阈值的预测
    confidence_thresholds = [0.7, 0.8, 0.9]
    
    for min_conf in confidence_thresholds:
        print(f"\n   测试置信度阈值 {min_conf}:")
        prediction = adapter.predict_next_room(observed_history, min_conf)
        
        if prediction:
            print(f"     ✅ 找到预测: 房间{prediction['predicted_room']}, 置信度{prediction['confidence']:.3f}")
            
            # 测试投注建议生成
            recommendations = adapter.generate_betting_recommendations(observed_history, 0.1)
            print(f"     投注建议数量: {len(recommendations)}")
            
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"       {i}. 房间{rec['room_number']}: {rec['bet_amount']:.2f}")
        else:
            print(f"     ❌ 未找到匹配的预测规则")
    
    # 4. 测试所有可能的历史组合
    print(f"\n3. 测试历史数据的所有子序列...")
    
    found_any = False
    for length in [3, 4, 5]:
        for start in range(len(observed_history) - length + 1):
            sub_history = observed_history[start:start+length]
            prediction = adapter.predict_next_room(sub_history, 0.7)
            
            if prediction:
                print(f"   ✅ 序列 {sub_history} -> 预测房间{prediction['predicted_room']} (置信度{prediction['confidence']:.3f})")
                found_any = True
    
    if not found_any:
        print("   ❌ 没有找到任何匹配的预测规则")
        
        # 显示实际的规则条件，看看为什么不匹配
        print(f"\n4. 显示前10个预测规则条件:")
        all_rules = adapter.high_confidence_rules + adapter.medium_confidence_rules
        for i, rule in enumerate(all_rules[:10], 1):
            print(f"   {i}. {rule['condition']} -> {rule['predicted_value']} (置信度: {rule['confidence']:.3f})")

def debug_risk_control():
    """调试风险控制"""
    
    print(f"\n🛡️ 调试风险控制系统")
    print("=" * 30)
    
    # 创建风险控制监控器
    config = {
        'max_daily_loss': 5.0,
        'max_consecutive_losses': 2,
        'max_single_bet': 1.0,
        'stop_loss_percentage': 0.15,
        'take_profit_percentage': 0.3,
        'initial_balance': 20.0
    }
    
    risk_monitor = RiskControlMonitor(config)
    
    # 测试风险指标计算
    metrics = risk_monitor.calculate_risk_metrics()
    print(f"当前风险指标:")
    print(f"  余额: {risk_monitor.current_balance}")
    print(f"  风险等级: {metrics.risk_level}")
    print(f"  胜率: {metrics.win_rate}")
    print(f"  连续失败: {metrics.consecutive_losses}")
    
    # 测试是否应该停止投注
    should_stop = risk_monitor.should_stop_betting()
    print(f"  是否应该停止投注: {should_stop}")
    
    # 测试推荐投注金额
    recommended_amount = risk_monitor.calculate_recommended_bet_size(0.8, 0.1)
    print(f"  推荐投注金额: {recommended_amount}")

def debug_betting_logic():
    """调试投注逻辑"""
    
    print(f"\n💰 调试投注逻辑")
    print("=" * 30)
    
    # 模拟系统状态
    print("模拟系统状态:")
    print("  betting_enabled: True")
    print("  history长度: >= 5")
    print("  风险控制: 通过")
    
    # 检查预测规则匹配
    adapter = PredictionRuleAdapter()
    rules = adapter.load_prediction_rules_from_analysis()
    adapter.categorize_rules(rules)
    
    # 使用观察到的历史数据
    history = [8, 5, 4, 5, 5, 6, 3, 2, 7, 1]
    
    print(f"\n检查预测流程:")
    print(f"1. 历史数据: {history}")
    print(f"2. 数据长度: {len(history)} (需要>=5)")
    
    if len(history) >= 5:
        print("   ✅ 历史数据充足")
        
        prediction = adapter.predict_next_room(history, 0.8)  # 使用系统默认的0.8置信度
        
        if prediction:
            print(f"3. ✅ 预测成功: 房间{prediction['predicted_room']}, 置信度{prediction['confidence']:.3f}")
            
            recommendations = adapter.generate_betting_recommendations(history, 0.1)
            
            if recommendations:
                print(f"4. ✅ 生成投注建议: {len(recommendations)}个")
                for rec in recommendations:
                    print(f"   - 房间{rec['room_number']}: {rec['bet_amount']:.2f}")
                
                print("5. 🎯 应该执行投注!")
            else:
                print("4. ❌ 未生成投注建议")
        else:
            print("3. ❌ 预测失败 - 这就是问题所在!")
            print("   原因: 没有找到置信度>=0.8的匹配规则")
            
            # 尝试降低置信度
            print("\n   尝试降低置信度:")
            for conf in [0.7, 0.6, 0.5]:
                pred = adapter.predict_next_room(history, conf)
                if pred:
                    print(f"   ✅ 置信度{conf}: 房间{pred['predicted_room']}, 置信度{pred['confidence']:.3f}")
                    break
            else:
                print("   ❌ 即使降低置信度也没有匹配的规则")
    else:
        print("   ❌ 历史数据不足")

def main():
    """主函数"""
    
    print("🔧 投注系统调试工具")
    print("=" * 50)
    print("分析为什么系统不进行投注")
    print()
    
    # 1. 调试预测系统
    debug_prediction_system()
    
    # 2. 调试风险控制
    debug_risk_control()
    
    # 3. 调试投注逻辑
    debug_betting_logic()
    
    print(f"\n📋 调试总结:")
    print("如果预测系统无法找到匹配规则，可能的原因:")
    print("1. 预测规则的条件与实际历史数据不匹配")
    print("2. 置信度阈值设置过高 (当前0.8)")
    print("3. 规则数据与实际游戏数据存在差异")
    
    print(f"\n💡 建议解决方案:")
    print("1. 降低置信度阈值到0.7或0.6")
    print("2. 检查预测规则是否基于正确的历史数据")
    print("3. 增加更多的预测规则")
    print("4. 实时收集当前游戏的开奖数据来训练新规则")

if __name__ == "__main__":
    main()
