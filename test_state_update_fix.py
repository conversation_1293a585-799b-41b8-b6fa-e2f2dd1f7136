#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试状态更新修复 - 验证连续获胜/失败计数是否正确更新
"""

import os
import time
from datetime import datetime
from api_framework import GameAPIClient, GameState
from optimized_random_betting_system import OptimizedRandomBettingSystem

def test_state_update_fix():
    """测试状态更新修复"""
    
    print("🧪 测试状态更新修复")
    print("=" * 60)
    
    # 创建模拟API客户端
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return None
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 创建系统实例
    api_client = MockAPIClient()
    config = {
        'base_bet_amount': 2,
        'max_bet_amount': 20,
        'min_bet_amount': 1,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50,
        'initial_balance': 200,
        'auto_report_interval': 10,
        'risk_monitoring': True,
        'real_time_logging': True
    }
    
    print("🎯 创建系统实例...")
    system = OptimizedRandomBettingSystem(api_client, config)
    
    # 清空历史记录，从干净状态开始
    system.bet_history = []
    system.result_history = []
    system.consecutive_wins = 0
    system.consecutive_losses = 0
    system.total_bets = 0
    system.total_wins = 0
    
    print(f"✅ 系统初始化完成")
    print(f"   初始状态: 连胜{system.consecutive_wins}次, 连败{system.consecutive_losses}次")
    
    print("\n" + "=" * 60)
    
    # 模拟一系列投注和结果
    test_scenarios = [
        {'issue': 130601, 'bet_room': 1, 'winning_room': 2, 'expected_result': '获胜'},
        {'issue': 130602, 'bet_room': 3, 'winning_room': 4, 'expected_result': '获胜'},
        {'issue': 130603, 'bet_room': 5, 'winning_room': 6, 'expected_result': '获胜'},  # 连胜3次
        {'issue': 130604, 'bet_room': 7, 'winning_room': 7, 'expected_result': '失败'},  # 第1次失败
        {'issue': 130605, 'bet_room': 2, 'winning_room': 2, 'expected_result': '失败'},  # 连败2次
        {'issue': 130606, 'bet_room': 4, 'winning_room': 8, 'expected_result': '获胜'},  # 重新获胜
    ]
    
    print("🎲 执行测试场景...")
    
    for i, scenario in enumerate(test_scenarios, 1):
        issue = scenario['issue']
        bet_room = scenario['bet_room']
        winning_room = scenario['winning_room']
        expected_result = scenario['expected_result']
        
        print(f"\n--- 场景{i}: 期号{issue} ---")
        
        # 1. 计算当前投注金额 (应该反映当前的连胜/连败状态)
        amount, calculation = system.calculate_enhanced_dynamic_amount()
        
        print(f"📊 当前状态:")
        print(f"   连续获胜: {system.consecutive_wins}次")
        print(f"   连续失败: {system.consecutive_losses}次")
        print(f"   总投注: {system.total_bets}次")
        print(f"   总获胜: {system.total_wins}次")
        
        print(f"💰 计算投注金额: {amount}元")
        
        # 显示计算详情中的关键信息
        print(f"   马丁格尔调整: {calculation.get('martingale_adjustment', 0)}元")
        print(f"   连胜奖励: {calculation.get('win_streak_bonus', 0)}元")
        print(f"   算法奖励: {calculation.get('algorithm_bonus', 0)}元")
        print(f"   风险调整: {calculation.get('risk_adjustment', 0)}元")
        
        # 2. 手动添加投注记录 (模拟真实投注)
        bet_info = {
            'issue': issue,
            'room': bet_room,
            'amount': amount,
            'timestamp': time.time(),
            'strategy': 'LCG算法'
        }
        system.bet_history.append(bet_info)
        system.total_bets += 1
        
        print(f"🎯 投注: 房间{bet_room}, 金额{amount}元")
        
        # 3. 处理开奖结果
        print(f"🎰 开奖: 房间{winning_room}")
        system.process_result(issue, winning_room)
        
        # 4. 验证状态更新
        print(f"📊 结果验证:")
        print(f"   预期结果: {expected_result}")
        print(f"   实际结果: {'获胜' if bet_room != winning_room else '失败'}")
        print(f"   更新后连胜: {system.consecutive_wins}次")
        print(f"   更新后连败: {system.consecutive_losses}次")
        
        # 验证状态是否正确
        actual_result = '获胜' if bet_room != winning_room else '失败'
        if actual_result == expected_result:
            print(f"   ✅ 结果正确")
        else:
            print(f"   ❌ 结果错误")
        
        # 验证连胜/连败计数
        if i == 1:  # 第1次获胜
            expected_wins, expected_losses = 1, 0
        elif i == 2:  # 第2次获胜
            expected_wins, expected_losses = 2, 0
        elif i == 3:  # 第3次获胜
            expected_wins, expected_losses = 3, 0
        elif i == 4:  # 第1次失败
            expected_wins, expected_losses = 0, 1
        elif i == 5:  # 第2次失败
            expected_wins, expected_losses = 0, 2
        elif i == 6:  # 重新获胜
            expected_wins, expected_losses = 1, 0
        
        if system.consecutive_wins == expected_wins and system.consecutive_losses == expected_losses:
            print(f"   ✅ 连胜/连败计数正确")
        else:
            print(f"   ❌ 连胜/连败计数错误")
            print(f"      预期: 连胜{expected_wins}次, 连败{expected_losses}次")
            print(f"      实际: 连胜{system.consecutive_wins}次, 连败{system.consecutive_losses}次")
    
    print("\n" + "=" * 60)
    
    # 测试下一次投注的金额计算
    print("🔮 测试下一次投注金额计算...")
    
    next_amount, next_calculation = system.calculate_enhanced_dynamic_amount()
    
    print(f"📊 当前最终状态:")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   总投注: {system.total_bets}次")
    print(f"   总获胜: {system.total_wins}次")
    print(f"   胜率: {system.total_wins/system.total_bets*100:.1f}%")
    
    print(f"\n💰 下次投注金额: {next_amount}元")
    print(f"   基础金额: {next_calculation.get('base_amount', 0)}元")
    print(f"   马丁格尔调整: {next_calculation.get('martingale_adjustment', 0)}元")
    print(f"   连胜奖励: {next_calculation.get('win_streak_bonus', 0)}元")
    print(f"   算法奖励: {next_calculation.get('algorithm_bonus', 0)}元")
    print(f"   风险调整: {next_calculation.get('risk_adjustment', 0)}元")
    
    # 验证最终状态
    if system.consecutive_wins == 1 and system.consecutive_losses == 0:
        print(f"\n✅ 最终状态正确: 连胜1次, 连败0次")
        
        # 验证下次投注应该有连胜奖励
        if next_calculation.get('win_streak_bonus', 0) == 0:
            print(f"✅ 连胜奖励正确: 连胜1次不足3次，无奖励")
        else:
            print(f"⚠️ 连胜奖励异常: 连胜1次不应有奖励")
            
    else:
        print(f"\n❌ 最终状态错误")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试连败场景的马丁格尔调整
    print("🔄 测试连败场景的马丁格尔调整...")
    
    # 手动设置连败状态
    system.consecutive_wins = 0
    system.consecutive_losses = 2
    
    martingale_amount, martingale_calc = system.calculate_enhanced_dynamic_amount()
    
    print(f"📊 连败2次状态:")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   连续失败: {system.consecutive_losses}次")
    
    print(f"\n💰 马丁格尔投注金额: {martingale_amount}元")
    print(f"   基础金额: {martingale_calc.get('base_amount', 0)}元")
    print(f"   马丁格尔调整: {martingale_calc.get('martingale_adjustment', 0)}元")
    print(f"   连胜奖励: {martingale_calc.get('win_streak_bonus', 0)}元")
    print(f"   算法奖励: {martingale_calc.get('algorithm_bonus', 0)}元")
    print(f"   风险调整: {martingale_calc.get('risk_adjustment', 0)}元")
    
    # 验证马丁格尔调整
    expected_martingale = 2  # 连败2次应该增加2元
    actual_martingale = martingale_calc.get('martingale_adjustment', 0)
    
    if actual_martingale == expected_martingale:
        print(f"✅ 马丁格尔调整正确: +{actual_martingale}元")
    else:
        print(f"❌ 马丁格尔调整错误: 预期+{expected_martingale}元, 实际+{actual_martingale}元")
        return False
    
    print("\n" + "=" * 60)
    print("🎯 测试完成!")
    print("✅ 状态更新修复测试通过")
    print("✅ 连续获胜/失败计数正确更新")
    print("✅ 马丁格尔调整正确计算")
    print("✅ 投注金额动态变化正常")
    
    return True

if __name__ == "__main__":
    success = test_state_update_fix()
    if success:
        print("\n🎉 所有测试通过！状态更新问题已修复。")
        print("💡 现在真实投注模式下，连续获胜/失败计数将正确更新")
        print("💡 投注金额将根据连胜/连败情况动态调整")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
