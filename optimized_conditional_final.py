#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化的条件概率系统
基于原始成功方案的精细化优化
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter

class OptimizedConditionalFinal:
    def __init__(self, sequence: List[int]):
        """初始化最终优化条件概率系统"""
        self.sequence = sequence
        self.n = len(sequence)
        
    def extract_optimal_rules(self) -> Dict:
        """提取最优规则 - 回到原始成功的方法"""
        print("=== 提取最优条件规则 ===")
        
        all_rules = []
        
        # 重点关注3-5元条件，这些在原始方法中表现最好
        for condition_length in [3, 4, 5]:
            print(f"\n分析 {condition_length} 元条件:")
            
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            rules = []
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= 3:  # 最小支持度
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    # 使用原始的高置信度阈值
                    if confidence >= 0.6:
                        rules.append({
                            'condition': condition,
                            'next_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length,
                            'priority': condition_length * 1000 + confidence * 100
                        })
            
            if rules:
                rules.sort(key=lambda x: x['confidence'], reverse=True)
                print(f"  发现 {len(rules)} 个高置信度规则 (前10个):")
                for rule in rules[:10]:
                    print(f"    {rule['condition']} -> {rule['next_value']} "
                          f"(置信度: {rule['confidence']:.3f}, 支持度: {rule['support']})")
                
                all_rules.extend(rules)
        
        # 按优先级排序（长条件优先，然后是高置信度）
        all_rules.sort(key=lambda x: x['priority'], reverse=True)
        
        print(f"\n总共提取了 {len(all_rules)} 个优质规则")
        return {'rules': all_rules}
    
    def validate_rules_performance(self, rules: Dict, validation_size: int = 300) -> Dict:
        """验证规则性能"""
        print(f"=== 验证规则性能 (最后 {validation_size} 个数据点) ===")
        
        if self.n <= validation_size:
            validation_size = self.n // 3
        
        # 使用前面的数据作为训练，后面的作为验证
        train_end = self.n - validation_size
        
        correct_predictions = 0
        total_predictions = 0
        rule_usage = defaultdict(int)
        rule_success = defaultdict(int)
        
        for i in range(train_end + 5, self.n):  # 从验证集开始预测
            predicted = None
            used_rule = None
            
            # 按优先级尝试规则
            for rule in rules['rules']:
                condition_length = rule['length']
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        used_rule = rule
                        break
            
            if predicted is not None:
                actual = self.sequence[i]
                rule_usage[str(used_rule['condition'])] += 1
                
                if predicted == actual:
                    correct_predictions += 1
                    rule_success[str(used_rule['condition'])] += 1
                
                total_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        print(f"验证集准确率: {accuracy:.4f} ({correct_predictions}/{total_predictions})")
        
        # 分析规则表现
        rule_performance = {}
        for rule_str, usage_count in rule_usage.items():
            success_count = rule_success[rule_str]
            rule_accuracy = success_count / usage_count if usage_count > 0 else 0
            rule_performance[rule_str] = {
                'usage': usage_count,
                'success': success_count,
                'accuracy': rule_accuracy
            }
        
        # 显示表现最好的规则
        best_performing = sorted(rule_performance.items(), 
                               key=lambda x: (x[1]['accuracy'], x[1]['usage']), reverse=True)
        
        print(f"\n表现最佳的规则 (前10个):")
        for rule_str, perf in best_performing[:10]:
            if perf['usage'] >= 2:
                print(f"  {rule_str}: {perf['accuracy']:.3f} ({perf['success']}/{perf['usage']})")
        
        return {
            'accuracy': accuracy,
            'rule_performance': rule_performance,
            'total_predictions': total_predictions
        }
    
    def filter_best_rules(self, rules: Dict, performance: Dict) -> Dict:
        """筛选最佳规则"""
        print(f"\n=== 筛选最佳规则 ===")
        
        # 只保留在验证中表现良好的规则
        filtered_rules = []
        
        for rule in rules['rules']:
            rule_str = str(rule['condition'])
            if rule_str in performance['rule_performance']:
                perf = performance['rule_performance'][rule_str]
                # 保留使用次数>=2且准确率>=0.5的规则
                if perf['usage'] >= 2 and perf['accuracy'] >= 0.5:
                    rule['validated_accuracy'] = perf['accuracy']
                    rule['validated_usage'] = perf['usage']
                    filtered_rules.append(rule)
        
        # 按验证准确率重新排序
        filtered_rules.sort(key=lambda x: (x['validated_accuracy'], x['confidence']), reverse=True)
        
        print(f"筛选后保留 {len(filtered_rules)} 个高质量规则")
        
        return {'rules': filtered_rules}
    
    def predict_with_best_rules(self, best_rules: Dict, count: int = 30) -> List[Dict]:
        """使用最佳规则进行预测"""
        print(f"=== 使用最佳规则预测未来 {count} 个值 ===")
        
        predictions = []
        current_sequence = self.sequence.copy()
        
        for step in range(count):
            predicted = None
            used_rule = None
            confidence = 0
            
            # 尝试应用最佳规则
            for rule in best_rules['rules']:
                condition_length = rule['length']
                if len(current_sequence) >= condition_length:
                    current_condition = tuple(current_sequence[-condition_length:])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        used_rule = rule
                        confidence = rule['validated_accuracy']
                        break
            
            if predicted is None:
                # 回退策略：使用最频繁的数字
                counter = Counter(current_sequence[-50:])  # 最近50个数字
                predicted = counter.most_common(1)[0][0]
                used_rule = {'condition': 'fallback'}
                confidence = 0.1
            
            prediction_info = {
                'step': step + 1,
                'predicted_value': predicted,
                'confidence': confidence,
                'rule_condition': used_rule['condition'],
                'rule_type': 'validated' if confidence > 0.1 else 'fallback'
            }
            
            predictions.append(prediction_info)
            current_sequence.append(predicted)
            
            print(f"  步骤 {step+1}: 预测 {predicted} "
                  f"(置信度: {confidence:.3f}, 规则: {used_rule['condition']})")
        
        return predictions
    
    def final_accuracy_test(self, best_rules: Dict, test_size: int = 100) -> Dict:
        """最终准确率测试"""
        print(f"\n=== 最终准确率测试 (最后 {test_size} 个数据点) ===")
        
        if self.n <= test_size:
            test_size = self.n // 4
        
        test_start = self.n - test_size
        correct = 0
        total = 0
        high_confidence_correct = 0
        high_confidence_total = 0
        
        for i in range(test_start + 5, self.n):
            predicted = None
            confidence = 0
            
            # 使用最佳规则预测
            for rule in best_rules['rules']:
                condition_length = rule['length']
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        confidence = rule['validated_accuracy']
                        break
            
            if predicted is not None:
                actual = self.sequence[i]
                if predicted == actual:
                    correct += 1
                    if confidence >= 0.7:
                        high_confidence_correct += 1
                
                total += 1
                if confidence >= 0.7:
                    high_confidence_total += 1
        
        overall_accuracy = correct / total if total > 0 else 0
        high_conf_accuracy = high_confidence_correct / high_confidence_total if high_confidence_total > 0 else 0
        
        print(f"整体准确率: {overall_accuracy:.4f} ({correct}/{total})")
        print(f"高置信度准确率: {high_conf_accuracy:.4f} ({high_confidence_correct}/{high_confidence_total})")
        
        return {
            'overall_accuracy': overall_accuracy,
            'high_confidence_accuracy': high_conf_accuracy,
            'total_predictions': total,
            'high_confidence_predictions': high_confidence_total
        }
    
    def run_optimized_system(self) -> Dict:
        """运行优化系统"""
        print("开始最终优化的条件概率系统...")
        print(f"序列长度: {self.n}")
        print()
        
        # 1. 提取最优规则
        optimal_rules = self.extract_optimal_rules()
        
        # 2. 验证规则性能
        performance = self.validate_rules_performance(optimal_rules)
        
        # 3. 筛选最佳规则
        best_rules = self.filter_best_rules(optimal_rules, performance)
        
        # 4. 最终准确率测试
        final_test = self.final_accuracy_test(best_rules)
        
        # 5. 预测未来值
        future_predictions = self.predict_with_best_rules(best_rules, 30)
        
        return {
            'optimal_rules': optimal_rules,
            'validation_accuracy': performance['accuracy'],
            'best_rules': best_rules,
            'final_test': final_test,
            'future_predictions': future_predictions
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    system = OptimizedConditionalFinal(sequence)
    results = system.run_optimized_system()
    
    print(f"\n=== 最终优化系统总结 ===")
    print(f"验证准确率: {results['validation_accuracy']:.4f}")
    print(f"最终测试准确率: {results['final_test']['overall_accuracy']:.4f}")
    print(f"高置信度准确率: {results['final_test']['high_confidence_accuracy']:.4f}")
    
    # 提取预测值
    predicted_values = [p['predicted_value'] for p in results['future_predictions']]
    print(f"预测的未来30个值: {predicted_values}")
    
    # 统计高置信度预测
    high_conf_predictions = [p for p in results['future_predictions'] if p['confidence'] >= 0.7]
    print(f"高置信度预测 (≥0.7): {len(high_conf_predictions)} 个")
    
    for pred in high_conf_predictions:
        print(f"  步骤 {pred['step']}: {pred['predicted_value']} (置信度: {pred['confidence']:.3f})")
    
    if results['final_test']['overall_accuracy'] > 0.7:
        print(f"\n🎯 系统达到优秀水平！准确率超过70%")
    elif results['final_test']['overall_accuracy'] > 0.6:
        print(f"\n✅ 系统表现良好！准确率超过60%")
    elif results['final_test']['overall_accuracy'] > 0.5:
        print(f"\n📈 系统表现尚可，准确率超过50%")
    else:
        print(f"\n📊 当前准确率: {results['final_test']['overall_accuracy']:.1%}，需要进一步优化")
