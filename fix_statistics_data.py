#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复统计数据不一致问题
"""

import json
import os
from datetime import datetime

def fix_statistics_data():
    """修复统计数据不一致问题"""
    
    print("🔧 修复统计数据不一致问题")
    print("=" * 60)
    
    # 查找最新的JSON记录文件
    json_files = [f for f in os.listdir('.') if f.startswith('betting_log_') and f.endswith('.json')]
    if not json_files:
        print("❌ 未找到JSON记录文件")
        return False
    
    latest_json = sorted(json_files)[-1]
    print(f"📂 处理文件: {latest_json}")
    
    # 读取JSON数据
    with open(latest_json, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 修复前统计:")
    session_info = data['session_info']
    print(f"   total_bets: {session_info['total_bets']}")
    print(f"   total_wins: {session_info['total_wins']}")
    print(f"   total_profit: {session_info['total_profit']:.2f}")
    
    # 重新计算正确的统计
    records = data['betting_records']
    completed_records = [r for r in records if r.get('status') == 'completed']
    
    correct_total_bets = len(completed_records)
    correct_total_wins = sum(1 for r in completed_records if r.get('result') == '获胜')
    correct_total_profit = sum(r.get('profit', 0) for r in completed_records)
    
    print(f"\n🧮 重新计算统计:")
    print(f"   总记录数: {len(records)}")
    print(f"   完成记录数: {correct_total_bets}")
    print(f"   获胜次数: {correct_total_wins}")
    print(f"   总盈亏: {correct_total_profit:.2f}")
    print(f"   胜率: {(correct_total_wins/correct_total_bets*100):.1f}%")
    
    # 显示每条完成的记录
    print(f"\n📋 完成的投注记录:")
    for i, record in enumerate(completed_records):
        issue = record.get('issue', 'N/A')
        result = record.get('result', 'N/A')
        profit = record.get('profit', 0)
        print(f"   {i+1}. 期号{issue}: {result}, 盈亏{profit:+.2f}元")
    
    # 显示未完成的记录
    pending_records = [r for r in records if r.get('status') != 'completed']
    if pending_records:
        print(f"\n⏳ 未完成的投注记录:")
        for i, record in enumerate(pending_records):
            issue = record.get('issue', 'N/A')
            status = record.get('status', 'N/A')
            print(f"   {i+1}. 期号{issue}: 状态={status}")
    
    # 更新统计数据
    print(f"\n🔧 更新统计数据:")
    data['session_info']['total_bets'] = correct_total_bets
    data['session_info']['total_wins'] = correct_total_wins
    data['session_info']['total_profit'] = correct_total_profit
    
    # 备份原文件
    backup_file = f"{latest_json}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"💾 备份原文件: {backup_file}")
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    # 保存修复后的文件
    print(f"💾 保存修复后的文件: {latest_json}")
    with open(latest_json, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 修复后统计:")
    print(f"   total_bets: {data['session_info']['total_bets']}")
    print(f"   total_wins: {data['session_info']['total_wins']}")
    print(f"   total_profit: {data['session_info']['total_profit']:.2f}")
    print(f"   胜率: {(data['session_info']['total_wins']/data['session_info']['total_bets']*100):.1f}%")
    
    # 触发Markdown文件重新生成
    print(f"\n📝 重新生成Markdown文件...")
    
    # 导入实时日志记录器并重新写入文件
    try:
        from real_time_logger import RealTimeLogger
        
        # 创建记录器实例
        session_id = data['session_info']['session_id']
        logger = RealTimeLogger(f"betting_log_{session_id}")
        
        # 更新记录器的数据
        logger.session_data = data
        
        # 重新写入所有文件
        logger.write_all_files()
        
        print(f"✅ Markdown文件已重新生成")
        
    except Exception as e:
        print(f"⚠️ 重新生成Markdown文件失败: {e}")
    
    print(f"\n✅ 统计数据修复完成!")
    print(f"💡 现在实时投注记录将显示正确的统计数据")
    print(f"💡 只有完成的投注才会被计入统计")
    
    return True

if __name__ == "__main__":
    success = fix_statistics_data()
    if success:
        print(f"\n🎉 修复成功！")
        print(f"💡 请检查更新后的实时投注记录文件")
    else:
        print(f"\n❌ 修复失败")
