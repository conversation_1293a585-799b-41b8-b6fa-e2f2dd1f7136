#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复日损失计算问题
"""

import json
import os
from datetime import datetime

def fix_daily_loss_calculation():
    """修复日损失计算问题"""
    
    print("🔧 修复日损失计算问题")
    print("=" * 60)
    
    # 查找状态文件
    state_file = "system_state_20250802.json"
    
    if not os.path.exists(state_file):
        print(f"❌ 状态文件不存在: {state_file}")
        return False
    
    # 读取当前状态
    print(f"📂 读取状态文件: {state_file}")
    with open(state_file, 'r', encoding='utf-8') as f:
        state_data = json.load(f)
    
    print(f"📊 当前状态:")
    for key, value in state_data.items():
        print(f"   {key}: {value}")
    
    # 计算正确的日损失
    initial_balance = 200.0  # 默认初始余额
    current_balance = state_data.get('current_balance', initial_balance)
    actual_daily_loss = max(0, initial_balance - current_balance)  # 确保不为负数
    
    print(f"\n🧮 计算正确的日损失:")
    print(f"   初始余额: {initial_balance:.2f}元")
    print(f"   当前余额: {current_balance:.2f}元")
    print(f"   实际日损失: {actual_daily_loss:.2f}元")
    print(f"   原daily_loss: {state_data.get('daily_loss', 0):.2f}元")
    
    # 更新状态
    state_data['daily_loss'] = actual_daily_loss
    state_data['last_update'] = datetime.now().isoformat()
    
    # 备份原文件
    backup_file = f"{state_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"\n💾 备份原文件: {backup_file}")
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(state_data, f, indent=2, ensure_ascii=False)
    
    # 保存修复后的状态
    print(f"💾 保存修复后的状态: {state_file}")
    with open(state_file, 'w', encoding='utf-8') as f:
        json.dump(state_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 修复后状态:")
    for key, value in state_data.items():
        print(f"   {key}: {value}")
    
    # 验证风险控制
    max_daily_loss = 50.0  # 默认日损失限制
    print(f"\n🛡️ 风险控制验证:")
    print(f"   日损失限制: {max_daily_loss:.2f}元")
    print(f"   实际日损失: {actual_daily_loss:.2f}元")
    print(f"   损失比例: {actual_daily_loss/max_daily_loss*100:.1f}%")
    
    if actual_daily_loss >= max_daily_loss:
        print(f"   🛑 超过日损失限制，系统将阻止投注")
    elif actual_daily_loss >= max_daily_loss * 0.8:
        print(f"   ⚠️ 接近日损失限制，风险等级: critical")
    elif actual_daily_loss >= max_daily_loss * 0.6:
        print(f"   ⚠️ 日损失较高，风险等级: high")
    elif actual_daily_loss >= max_daily_loss * 0.4:
        print(f"   ⚠️ 日损失中等，风险等级: medium")
    else:
        print(f"   ✅ 日损失在安全范围内，风险等级: low")
    
    print(f"\n✅ 日损失计算修复完成!")
    print(f"💡 现在系统将使用实际余额损失进行风险控制")
    print(f"💡 而不是只累加失败投注金额")
    
    return True

if __name__ == "__main__":
    success = fix_daily_loss_calculation()
    if success:
        print(f"\n🎉 修复成功！")
        print(f"💡 重新启动投注系统，风险控制将使用正确的日损失计算")
    else:
        print(f"\n❌ 修复失败")
