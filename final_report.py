#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机数算法逆向分析 - 最终报告
基于对69个随机数序列的深入分析，提供最佳算法实现和预测
"""

import numpy as np
from typing import List, Tuple, Dict

class RandomNumberPredictor:
    """随机数预测器 - 基于逆向分析的结果"""
    
    def __init__(self):
        """初始化预测器"""
        # 基于分析得出的最佳LCG参数
        self.best_algorithms = [
            {
                'name': 'LCG_MSVC',
                'type': 'LCG',
                'a': 1664525,
                'c': 1013904223,
                'm': 2**32,
                'seed': 51,
                'mapping': lambda x: (x % 8) + 1,
                'match_rate': 0.261,
                'description': 'Microsoft Visual C++ LCG参数'
            },
            {
                'name': 'LCG_Small',
                'type': 'LCG',
                'a': 17,
                'c': 5,
                'm': 256,
                'seed': 3,
                'mapping': lambda x: (x % 8) + 1,
                'match_rate': 0.261,
                'description': '小参数LCG，适合简单游戏'
            }
        ]
        
        self.target_sequence = [4, 6, 1, 2, 8, 3, 2, 4, 3, 6, 7, 3, 5, 2, 2, 2, 2, 1, 2, 5, 3, 2, 4, 4, 1, 6, 2, 8, 7, 1, 2, 7, 1, 4, 4, 5, 7, 2, 1, 3, 4, 5, 5, 1, 4, 2, 5, 4, 1, 3, 6, 7, 5, 5, 7, 4, 5, 8, 1, 6, 6, 6, 5, 8, 4, 2, 2, 7, 3]
    
    def generate_lcg_sequence(self, seed: int, a: int, c: int, m: int, count: int, mapping_func) -> List[int]:
        """生成LCG序列"""
        sequence = []
        x = seed
        for _ in range(count):
            x = (a * x + c) % m
            mapped = mapping_func(x)
            sequence.append(mapped)
        return sequence
    
    def predict_next_values(self, algorithm_index: int = 0, count: int = 20) -> List[int]:
        """预测后续的随机数值"""
        algo = self.best_algorithms[algorithm_index]
        
        if algo['type'] == 'LCG':
            # 计算当前LCG状态
            x = algo['seed']
            for _ in range(len(self.target_sequence)):
                x = (algo['a'] * x + algo['c']) % algo['m']
            
            # 生成后续值
            predictions = []
            for _ in range(count):
                x = (algo['a'] * x + algo['c']) % algo['m']
                mapped = algo['mapping'](x)
                predictions.append(mapped)
            
            return predictions
        
        return []
    
    def validate_algorithm(self, algorithm_index: int = 0) -> Dict:
        """验证算法的准确性"""
        algo = self.best_algorithms[algorithm_index]
        
        if algo['type'] == 'LCG':
            generated = self.generate_lcg_sequence(
                algo['seed'], algo['a'], algo['c'], algo['m'], 
                len(self.target_sequence), algo['mapping']
            )
            
            matches = sum(1 for i in range(len(self.target_sequence)) 
                         if generated[i] == self.target_sequence[i])
            match_rate = matches / len(self.target_sequence)
            
            return {
                'algorithm': algo['name'],
                'parameters': f"LCG({algo['a']}, {algo['c']}, {algo['m']})",
                'seed': algo['seed'],
                'match_rate': match_rate,
                'matches': matches,
                'total': len(self.target_sequence),
                'generated_sequence': generated
            }
        
        return {}
    
    def get_algorithm_implementation(self, algorithm_index: int = 0) -> str:
        """获取算法的实现代码"""
        algo = self.best_algorithms[algorithm_index]
        
        if algo['type'] == 'LCG':
            code = f"""
// {algo['description']}
// 匹配率: {algo['match_rate']:.3f}

class RandomGenerator {{
private:
    unsigned long long state;
    static const unsigned long long a = {algo['a']}ULL;
    static const unsigned long long c = {algo['c']}ULL;
    static const unsigned long long m = {algo['m']}ULL;

public:
    RandomGenerator(unsigned long long seed = {algo['seed']}ULL) : state(seed) {{}}
    
    int next() {{
        state = (a * state + c) % m;
        return (state % 8) + 1;  // 映射到1-8范围
    }}
    
    // 重置种子
    void setSeed(unsigned long long seed) {{
        state = seed;
    }}
}};

// Python实现
class RandomGenerator:
    def __init__(self, seed={algo['seed']}):
        self.state = seed
        self.a = {algo['a']}
        self.c = {algo['c']}
        self.m = {algo['m']}
    
    def next(self):
        self.state = (self.a * self.state + self.c) % self.m
        return (self.state % 8) + 1
    
    def set_seed(self, seed):
        self.state = seed
"""
            return code
        
        return "未找到有效的算法实现"
    
    def generate_analysis_report(self) -> str:
        """生成完整的分析报告"""
        report = """
=== 随机数算法逆向分析报告 ===

1. 数据概况:
   - 序列长度: 69个数字
   - 数值范围: 1-8
   - 数据来源: 游戏随机生成

2. 分析方法:
   - 基础统计分析（频率分布、均值、方差）
   - 周期性检测
   - 自相关分析
   - 状态转换矩阵分析
   - 常见PRNG算法匹配（LCG、Xorshift等）
   - 暴力搜索种子空间

3. 主要发现:
   - 数字2出现频率最高(20.3%)，数字8出现频率最低(5.8%)
   - 未发现明显的周期性模式
   - 自相关性较弱，符合伪随机数特征
   - 存在一些重复的短模式，如(2,2)出现4次

4. 最佳算法候选:
"""
        
        for i, algo in enumerate(self.best_algorithms):
            validation = self.validate_algorithm(i)
            report += f"""
   算法 {i+1}: {algo['name']}
   - 类型: 线性同余生成器 (LCG)
   - 参数: a={algo['a']}, c={algo['c']}, m={algo['m']}
   - 种子: {algo['seed']}
   - 匹配率: {validation['match_rate']:.3f} ({validation['matches']}/{validation['total']})
   - 描述: {algo['description']}
"""
        
        # 预测示例
        predictions = self.predict_next_values(0, 10)
        report += f"""
5. 预测示例:
   基于最佳算法预测的后续10个数字: {predictions}

6. 使用建议:
   - 如果需要预测游戏的随机数，建议使用算法1 (LCG_MSVC)
   - 匹配率约26%，虽然不是完美匹配，但在实际应用中可能有一定参考价值
   - 建议收集更多数据样本以提高分析准确性

7. 技术限制:
   - 当前分析基于69个样本，样本量相对较小
   - 游戏可能使用了更复杂的算法或多重随机源
   - 可能存在我们未考虑到的映射函数或状态转换

8. 代码实现:
   详见 get_algorithm_implementation() 方法的输出
"""
        
        return report

def main():
    """主函数 - 演示预测器的使用"""
    predictor = RandomNumberPredictor()
    
    print("=== 随机数算法逆向分析结果 ===\n")
    
    # 显示分析报告
    print(predictor.generate_analysis_report())
    
    # 验证最佳算法
    print("\n=== 算法验证 ===")
    for i in range(len(predictor.best_algorithms)):
        validation = predictor.validate_algorithm(i)
        print(f"\n算法 {i+1} 验证结果:")
        print(f"  名称: {validation['algorithm']}")
        print(f"  参数: {validation['parameters']}")
        print(f"  种子: {validation['seed']}")
        print(f"  匹配率: {validation['match_rate']:.3f}")
        print(f"  匹配数: {validation['matches']}/{validation['total']}")
    
    # 显示实现代码
    print("\n=== 最佳算法实现代码 ===")
    print(predictor.get_algorithm_implementation(0))
    
    # 预测后续值
    print("\n=== 预测后续随机数 ===")
    predictions = predictor.predict_next_values(0, 20)
    print(f"预测的后续20个数字: {predictions}")
    
    # 提供使用示例
    print("\n=== 使用示例 ===")
    print("# Python使用示例:")
    print("rng = RandomGenerator(seed=51)")
    print("for i in range(10):")
    print("    print(f'随机数 {i+1}: {rng.next()}')")

if __name__ == "__main__":
    main()
