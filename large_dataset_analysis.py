#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大数据集随机数分析工具
针对2000个随机数样本进行深入分析
"""

import numpy as np
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
from typing import List, Dict, Tuple
import itertools

class LargeDatasetAnalyzer:
    def __init__(self, data_file: str):
        """初始化大数据集分析器"""
        self.data_file = data_file
        self.sequence = self.load_data()
        self.n = len(self.sequence)
        
    def load_data(self) -> List[int]:
        """加载数据"""
        with open(self.data_file, 'r', encoding='utf-8') as f:
            return [int(line.strip()) for line in f if line.strip()]
    
    def comprehensive_statistics(self) -> Dict:
        """全面的统计分析"""
        print("=== 大数据集统计分析 ===")
        print(f"数据点数量: {self.n}")
        
        seq = np.array(self.sequence)
        
        # 基础统计
        stats = {
            'count': self.n,
            'min': int(np.min(seq)),
            'max': int(np.max(seq)),
            'mean': float(np.mean(seq)),
            'std': float(np.std(seq)),
            'variance': float(np.var(seq)),
            'median': float(np.median(seq))
        }
        
        print("基础统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 频率分布
        counter = Counter(self.sequence)
        freq_dist = {}
        expected_freq = self.n / 8  # 理论期望频率
        
        print(f"\n频率分布 (期望每个数字出现 {expected_freq:.1f} 次):")
        chi_square = 0
        for i in range(1, 9):
            count = counter.get(i, 0)
            freq = count / self.n
            deviation = abs(count - expected_freq)
            chi_square += (count - expected_freq) ** 2 / expected_freq
            
            freq_dist[i] = {
                'count': count,
                'frequency': freq,
                'expected': expected_freq,
                'deviation': deviation
            }
            
            print(f"  数字 {i}: {count:4d} 次 ({freq:.4f}, 偏差: {deviation:.1f})")
        
        print(f"\n卡方统计量: {chi_square:.3f}")
        print(f"自由度: 7, 临界值(α=0.05): 14.067")
        print(f"均匀性检验: {'通过' if chi_square < 14.067 else '不通过'}")
        
        return {
            'basic_stats': stats,
            'frequency_dist': freq_dist,
            'chi_square': chi_square,
            'uniformity_test': chi_square < 14.067
        }
    
    def advanced_pattern_analysis(self) -> Dict:
        """高级模式分析"""
        print(f"\n=== 高级模式分析 ===")
        
        results = {}
        
        # 分析不同长度的模式
        for length in range(2, 6):
            patterns = Counter()
            for i in range(self.n - length + 1):
                pattern = tuple(self.sequence[i:i + length])
                patterns[pattern] += 1
            
            # 计算期望频率
            expected_freq = (self.n - length + 1) / (8 ** length)
            
            # 找出显著偏离期望的模式
            significant_patterns = []
            for pattern, count in patterns.items():
                if count > expected_freq * 2:  # 出现频率超过期望2倍
                    significant_patterns.append((pattern, count, count / expected_freq))
            
            if significant_patterns:
                print(f"\n{length}位显著模式 (期望频率: {expected_freq:.3f}):")
                for pattern, count, ratio in sorted(significant_patterns, key=lambda x: x[2], reverse=True)[:10]:
                    print(f"  {pattern}: {count} 次 (期望的 {ratio:.2f} 倍)")
            
            results[f'patterns_{length}'] = {
                'total_patterns': len(patterns),
                'expected_freq': expected_freq,
                'significant_patterns': significant_patterns
            }
        
        return results
    
    def runs_test(self) -> Dict:
        """游程检验 - 检测随机性"""
        print(f"\n=== 游程检验 ===")
        
        # 将数据转换为二进制序列 (>中位数为1，<=中位数为0)
        median = np.median(self.sequence)
        binary_seq = [1 if x > median else 0 for x in self.sequence]
        
        # 计算游程
        runs = []
        current_run = 1
        for i in range(1, len(binary_seq)):
            if binary_seq[i] == binary_seq[i-1]:
                current_run += 1
            else:
                runs.append(current_run)
                current_run = 1
        runs.append(current_run)
        
        n_runs = len(runs)
        n1 = sum(binary_seq)  # 1的个数
        n0 = len(binary_seq) - n1  # 0的个数
        
        # 期望游程数和方差
        expected_runs = (2 * n1 * n0) / (n1 + n0) + 1
        variance_runs = (2 * n1 * n0 * (2 * n1 * n0 - n1 - n0)) / ((n1 + n0) ** 2 * (n1 + n0 - 1))
        
        # Z统计量
        z_stat = (n_runs - expected_runs) / np.sqrt(variance_runs) if variance_runs > 0 else 0
        
        print(f"总游程数: {n_runs}")
        print(f"期望游程数: {expected_runs:.2f}")
        print(f"Z统计量: {z_stat:.3f}")
        print(f"随机性检验: {'通过' if abs(z_stat) < 1.96 else '不通过'} (α=0.05)")
        
        return {
            'runs_count': n_runs,
            'expected_runs': expected_runs,
            'z_statistic': z_stat,
            'randomness_test': abs(z_stat) < 1.96
        }
    
    def autocorrelation_detailed(self, max_lag: int = 50) -> Dict:
        """详细的自相关分析"""
        print(f"\n=== 详细自相关分析 ===")
        
        seq = np.array(self.sequence, dtype=float)
        seq_centered = seq - np.mean(seq)
        
        autocorr = {}
        significant_lags = []
        
        for lag in range(1, min(max_lag + 1, len(seq) // 4)):
            if len(seq_centered) > lag:
                corr = np.corrcoef(seq_centered[:-lag], seq_centered[lag:])[0, 1]
                if not np.isnan(corr):
                    autocorr[lag] = float(corr)
                    if abs(corr) > 0.1:  # 显著相关
                        significant_lags.append((lag, corr))
        
        if significant_lags:
            print("显著自相关 (|r| > 0.1):")
            for lag, corr in significant_lags[:10]:
                print(f"  滞后 {lag}: {corr:.4f}")
        else:
            print("未发现显著的自相关性")
        
        return {
            'autocorrelations': autocorr,
            'significant_lags': significant_lags
        }
    
    def spectral_analysis(self) -> Dict:
        """频谱分析"""
        print(f"\n=== 频谱分析 ===")
        
        # 计算功率谱密度
        seq = np.array(self.sequence, dtype=float)
        seq_centered = seq - np.mean(seq)
        
        # FFT
        fft_result = np.fft.fft(seq_centered)
        power_spectrum = np.abs(fft_result) ** 2
        
        # 找出主要频率成分
        freqs = np.fft.fftfreq(len(seq_centered))
        
        # 只考虑正频率
        positive_freqs = freqs[:len(freqs)//2]
        positive_power = power_spectrum[:len(power_spectrum)//2]
        
        # 找出功率最大的频率
        max_power_idx = np.argmax(positive_power[1:]) + 1  # 排除DC分量
        dominant_freq = positive_freqs[max_power_idx]
        dominant_power = positive_power[max_power_idx]
        
        print(f"主导频率: {dominant_freq:.6f}")
        print(f"对应周期: {1/dominant_freq:.2f}" if dominant_freq != 0 else "无明显周期")
        print(f"主导功率: {dominant_power:.2e}")
        
        return {
            'dominant_frequency': float(dominant_freq),
            'dominant_power': float(dominant_power),
            'period': float(1/dominant_freq) if dominant_freq != 0 else None
        }
    
    def run_comprehensive_analysis(self) -> Dict:
        """运行全面分析"""
        print("开始大数据集全面分析...")
        print(f"数据文件: {self.data_file}")
        print(f"序列前20个数字: {self.sequence[:20]}")
        print()
        
        # 各项分析
        stats_result = self.comprehensive_statistics()
        pattern_result = self.advanced_pattern_analysis()
        runs_result = self.runs_test()
        autocorr_result = self.autocorrelation_detailed()
        spectral_result = self.spectral_analysis()
        
        return {
            'statistics': stats_result,
            'patterns': pattern_result,
            'runs_test': runs_result,
            'autocorrelation': autocorr_result,
            'spectral': spectral_result
        }

def main():
    """主函数"""
    analyzer = LargeDatasetAnalyzer("随机2000个数字.txt")
    results = analyzer.run_comprehensive_analysis()
    
    print(f"\n=== 分析总结 ===")
    print(f"数据质量评估:")
    print(f"  均匀性检验: {'✓' if results['statistics']['uniformity_test'] else '✗'}")
    print(f"  随机性检验: {'✓' if results['runs_test']['randomness_test'] else '✗'}")
    print(f"  自相关检验: {'✓' if not results['autocorrelation']['significant_lags'] else '✗'}")
    
    if results['spectral']['period']:
        print(f"  可能存在周期性: {results['spectral']['period']:.2f}")
    else:
        print(f"  无明显周期性")

if __name__ == "__main__":
    main()
