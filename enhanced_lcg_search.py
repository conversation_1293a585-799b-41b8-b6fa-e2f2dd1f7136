#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强LCG参数搜索
利用2000个数据点进行高精度的LCG参数搜索
"""

import numpy as np
from typing import List, Tuple, Dict
import itertools
from math import gcd
import time

class EnhancedLCGSearcher:
    def __init__(self, target_sequence: List[int]):
        """初始化增强LCG搜索器"""
        self.target_sequence = target_sequence
        self.n = len(target_sequence)
        print(f"目标序列长度: {self.n}")
        
    def lcg_generator(self, seed: int, a: int, c: int, m: int, count: int) -> List[int]:
        """LCG生成器"""
        sequence = []
        x = seed
        for _ in range(count):
            x = (a * x + c) % m
            mapped = (x % 8) + 1
            sequence.append(mapped)
        return sequence
    
    def calculate_match_rate(self, generated: List[int]) -> float:
        """计算匹配率"""
        matches = sum(1 for i in range(min(len(generated), self.n)) 
                     if generated[i] == self.target_sequence[i])
        return matches / min(len(generated), self.n)
    
    def search_common_lcg_parameters(self) -> List[Tuple]:
        """搜索常见的LCG参数"""
        print("=== 搜索常见LCG参数 ===")
        
        # 扩展的常见LCG参数列表
        common_params = [
            # 标准库参数
            (1103515245, 12345, 2**31),
            (214013, 2531011, 2**32),
            (1664525, 1013904223, 2**32),
            (16807, 0, 2**31 - 1),
            (48271, 0, 2**31 - 1),
            (69621, 0, 2**32),
            (1103515245, 12345, 2**32),
            
            # 小参数LCG
            (17, 5, 256), (25, 7, 256), (37, 3, 256), (41, 7, 256),
            (13, 11, 64), (21, 5, 64), (29, 3, 128), (45, 21, 128),
            (7, 5, 32), (11, 7, 32), (15, 1, 32), (23, 9, 64),
            
            # 中等参数
            (1103515245, 12345, 65536),
            (214013, 2531011, 65536),
            (1664525, 1013904223, 65536),
            (75, 74, 65537),
            (16807, 0, 65536),
            
            # 更多变体
            (134775813, 1, 2**32),
            (1812433253, 0, 2**32),
            (279470273, 0, 2**32),
            (1103515245, 12345, 16777216),
        ]
        
        results = []
        
        for i, (a, c, m) in enumerate(common_params):
            print(f"  测试 {i+1}/{len(common_params)}: LCG({a}, {c}, {m})")
            
            best_match = 0
            best_seed = 0
            
            # 智能种子搜索
            seed_candidates = []
            
            # 小种子值
            seed_candidates.extend(range(1, min(1000, m // 1000)))
            
            # 基于模数的种子
            if m > 1000:
                step = max(1, m // 10000)
                seed_candidates.extend(range(1, min(10000, m), step))
            
            # 特殊种子值
            seed_candidates.extend([1, 2, 3, 7, 11, 13, 17, 19, 23, 31, 37, 41, 43, 47])
            
            # 去重
            seed_candidates = list(set(seed_candidates))
            
            for seed in seed_candidates:
                if seed >= m or seed <= 0:
                    continue
                    
                try:
                    generated = self.lcg_generator(seed, a, c, m, self.n)
                    match_rate = self.calculate_match_rate(generated)
                    
                    if match_rate > best_match:
                        best_match = match_rate
                        best_seed = seed
                        
                except (OverflowError, ZeroDivisionError):
                    continue
            
            if best_match > 0.3:  # 匹配率超过30%
                results.append((a, c, m, best_seed, best_match))
                print(f"    找到高匹配: seed={best_seed}, 匹配率={best_match:.4f}")
        
        return sorted(results, key=lambda x: x[4], reverse=True)
    
    def exhaustive_small_parameter_search(self) -> List[Tuple]:
        """穷举搜索小参数空间"""
        print(f"\n=== 穷举搜索小参数LCG ===")
        
        results = []
        
        # 搜索范围
        moduli = [8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096]
        
        for m in moduli:
            print(f"  搜索模数 m = {m}")
            
            # a必须与m互质
            valid_a = [a for a in range(1, min(m, 200)) if gcd(a, m) == 1]
            
            for a in valid_a:
                for c in range(0, min(m, 100)):
                    best_match = 0
                    best_seed = 0
                    
                    # 测试多个种子
                    for seed in range(1, min(m, 100)):
                        try:
                            generated = self.lcg_generator(seed, a, c, m, self.n)
                            match_rate = self.calculate_match_rate(generated)
                            
                            if match_rate > best_match:
                                best_match = match_rate
                                best_seed = seed
                                
                        except (OverflowError, ZeroDivisionError):
                            continue
                    
                    if best_match > 0.5:  # 高匹配率阈值
                        results.append((a, c, m, best_seed, best_match))
                        print(f"    找到高匹配: LCG({a}, {c}, {m}), seed={best_seed}, 匹配率={best_match:.4f}")
        
        return sorted(results, key=lambda x: x[4], reverse=True)
    
    def test_different_mappings(self, a: int, c: int, m: int, seed: int) -> List[Tuple]:
        """测试不同的映射函数"""
        print(f"\n  测试不同映射函数 for LCG({a}, {c}, {m}), seed={seed}")
        
        # 生成内部状态序列
        internal_states = []
        x = seed
        for _ in range(self.n):
            x = (a * x + c) % m
            internal_states.append(x)
        
        # 各种映射函数
        mappings = [
            ("(x % 8) + 1", lambda x: (x % 8) + 1),
            ("((x >> 3) % 8) + 1", lambda x: ((x >> 3) % 8) + 1),
            ("((x >> 8) % 8) + 1", lambda x: ((x >> 8) % 8) + 1),
            ("((x >> 16) % 8) + 1", lambda x: ((x >> 16) % 8) + 1),
            ("((x // 8) % 8) + 1", lambda x: ((x // 8) % 8) + 1),
            ("((x * 8) // m) + 1", lambda x: min(((x * 8) // m) + 1, 8)),
            ("(x % 7) + 1", lambda x: min((x % 7) + 1, 8)),
            ("((x ^ (x >> 8)) % 8) + 1", lambda x: ((x ^ (x >> 8)) % 8) + 1),
            ("((x + (x >> 4)) % 8) + 1", lambda x: ((x + (x >> 4)) % 8) + 1),
            ("((x * 3) % 8) + 1", lambda x: ((x * 3) % 8) + 1),
        ]
        
        results = []
        
        for name, mapping_func in mappings:
            try:
                mapped_sequence = [mapping_func(state) for state in internal_states]
                match_rate = self.calculate_match_rate(mapped_sequence)
                
                if match_rate > 0.3:
                    results.append((name, match_rate, mapped_sequence))
                    print(f"    {name}: 匹配率 {match_rate:.4f}")
                    
            except (ZeroDivisionError, OverflowError):
                continue
        
        return sorted(results, key=lambda x: x[1], reverse=True)
    
    def run_enhanced_search(self) -> Dict:
        """运行增强搜索"""
        print("开始增强LCG参数搜索...")
        print(f"目标序列前20个: {self.target_sequence[:20]}")
        print()
        
        start_time = time.time()
        
        # 搜索常见参数
        common_results = self.search_common_lcg_parameters()
        
        # 穷举搜索小参数
        exhaustive_results = self.exhaustive_small_parameter_search()
        
        # 合并结果
        all_results = common_results + exhaustive_results
        all_results = sorted(all_results, key=lambda x: x[4], reverse=True)
        
        # 对最佳结果测试不同映射
        best_with_mappings = []
        if all_results:
            print(f"\n=== 测试最佳候选的不同映射 ===")
            for i, (a, c, m, seed, match_rate) in enumerate(all_results[:5]):
                print(f"\n候选 {i+1}: LCG({a}, {c}, {m}), seed={seed}, 基础匹配率={match_rate:.4f}")
                
                mapping_results = self.test_different_mappings(a, c, m, seed)
                
                for map_name, map_match_rate, mapped_seq in mapping_results[:3]:
                    best_with_mappings.append({
                        'lcg_params': (a, c, m),
                        'seed': seed,
                        'mapping': map_name,
                        'match_rate': map_match_rate,
                        'sequence': mapped_seq
                    })
        
        # 按匹配率排序
        best_with_mappings.sort(key=lambda x: x['match_rate'], reverse=True)
        
        elapsed_time = time.time() - start_time
        print(f"\n搜索完成，耗时 {elapsed_time:.2f} 秒")
        
        return {
            'common_results': common_results,
            'exhaustive_results': exhaustive_results,
            'all_results': all_results,
            'best_with_mappings': best_with_mappings,
            'search_time': elapsed_time
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    searcher = EnhancedLCGSearcher(sequence)
    results = searcher.run_enhanced_search()
    
    print(f"\n=== 搜索结果总结 ===")
    
    if results['best_with_mappings']:
        print("最佳LCG候选 (含映射函数):")
        for i, result in enumerate(results['best_with_mappings'][:5], 1):
            a, c, m = result['lcg_params']
            print(f"\n{i}. LCG({a}, {c}, {m})")
            print(f"   种子: {result['seed']}")
            print(f"   映射: {result['mapping']}")
            print(f"   匹配率: {result['match_rate']:.4f}")
    else:
        print("未找到高匹配率的LCG参数")
    
    print(f"\n总搜索时间: {results['search_time']:.2f} 秒")
