#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
序列相关性分析工具
深入分析随机数序列的相关性和状态转换模式
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import itertools

class SequenceCorrelationAnalyzer:
    def __init__(self, sequence: List[int]):
        """初始化序列相关性分析器"""
        self.sequence = sequence
        self.n = len(sequence)
        
    def transition_matrix_analysis(self) -> Dict:
        """状态转换矩阵分析"""
        print("=== 状态转换矩阵分析 ===")
        
        # 构建转换矩阵
        transitions = defaultdict(lambda: defaultdict(int))
        for i in range(self.n - 1):
            current = self.sequence[i]
            next_val = self.sequence[i + 1]
            transitions[current][next_val] += 1
        
        # 计算转换概率
        transition_probs = {}
        for current in range(1, 9):
            total = sum(transitions[current].values())
            if total > 0:
                transition_probs[current] = {}
                for next_val in range(1, 9):
                    count = transitions[current][next_val]
                    transition_probs[current][next_val] = count / total
        
        # 显示转换矩阵
        print("状态转换计数矩阵:")
        print("从\\到", end="")
        for j in range(1, 9):
            print(f"{j:4d}", end="")
        print()
        
        for i in range(1, 9):
            print(f"{i:4d}", end="")
            for j in range(1, 9):
                count = transitions[i][j]
                print(f"{count:4d}", end="")
            print()
        
        print("\n状态转换概率矩阵:")
        print("从\\到", end="")
        for j in range(1, 9):
            print(f"{j:6.2f}", end="")
        print()
        
        for i in range(1, 9):
            print(f"{i:4d}", end="")
            for j in range(1, 9):
                prob = transition_probs.get(i, {}).get(j, 0)
                print(f"{prob:6.3f}", end="")
            print()
        
        return {
            'transitions': dict(transitions),
            'probabilities': transition_probs
        }
    
    def multi_step_correlation(self, max_steps: int = 5) -> Dict:
        """多步相关性分析"""
        print(f"\n=== 多步相关性分析 (最多{max_steps}步) ===")
        
        correlations = {}
        
        for step in range(1, max_steps + 1):
            if self.n <= step:
                break
                
            # 计算step步的相关性
            x = np.array(self.sequence[:-step], dtype=float)
            y = np.array(self.sequence[step:], dtype=float)
            
            if len(x) > 1 and len(y) > 1:
                correlation = np.corrcoef(x, y)[0, 1]
                if not np.isnan(correlation):
                    correlations[step] = correlation
                    print(f"  {step}步相关性: {correlation:.4f}")
        
        return correlations
    
    def pattern_frequency_analysis(self, pattern_length: int = 3) -> Dict:
        """模式频率分析"""
        print(f"\n=== {pattern_length}位模式频率分析 ===")
        
        patterns = Counter()
        for i in range(self.n - pattern_length + 1):
            pattern = tuple(self.sequence[i:i + pattern_length])
            patterns[pattern] += 1
        
        total_patterns = len(patterns)
        expected_freq = 1 / (8 ** pattern_length)
        
        print(f"总共发现 {total_patterns} 种不同的{pattern_length}位模式")
        print(f"理论期望频率: {expected_freq:.6f}")
        print("\n最常见的模式:")
        
        significant_patterns = []
        for pattern, count in patterns.most_common(10):
            freq = count / (self.n - pattern_length + 1)
            deviation = abs(freq - expected_freq)
            if count > 1:  # 只显示出现多次的模式
                print(f"  {pattern}: 出现{count}次, 频率{freq:.4f}, 偏差{deviation:.4f}")
                significant_patterns.append((pattern, count, freq, deviation))
        
        return {
            'patterns': dict(patterns),
            'significant_patterns': significant_patterns,
            'expected_frequency': expected_freq
        }
    
    def run_analysis(self) -> Dict:
        """运行完整的相关性分析"""
        print(f"序列长度: {self.n}")
        print(f"序列: {self.sequence}")
        print()
        
        # 状态转换分析
        transition_analysis = self.transition_matrix_analysis()
        
        # 多步相关性分析
        correlation_analysis = self.multi_step_correlation()
        
        # 模式频率分析
        pattern_analysis_2 = self.pattern_frequency_analysis(2)
        pattern_analysis_3 = self.pattern_frequency_analysis(3)
        
        return {
            'transitions': transition_analysis,
            'correlations': correlation_analysis,
            'patterns_2': pattern_analysis_2,
            'patterns_3': pattern_analysis_3
        }
    
    def detect_potential_algorithms(self) -> List[str]:
        """基于分析结果检测可能的算法"""
        print("\n=== 算法模式检测 ===")
        
        potential_algorithms = []
        
        # 检查是否有明显的状态转换偏好
        transitions = self.transition_matrix_analysis()['transitions']
        
        # 检查是否存在强烈的转换偏好
        strong_transitions = []
        for current in range(1, 9):
            if current in transitions:
                total = sum(transitions[current].values())
                if total > 0:
                    for next_val, count in transitions[current].items():
                        if count / total > 0.5:  # 超过50%的概率转换到某个状态
                            strong_transitions.append((current, next_val, count/total))
        
        if strong_transitions:
            print("发现强转换偏好:")
            for current, next_val, prob in strong_transitions:
                print(f"  {current} -> {next_val}: {prob:.3f}")
            potential_algorithms.append("状态机或查表算法")
        
        # 检查周期性
        for period in range(2, min(20, self.n // 3)):
            matches = 0
            total = 0
            for i in range(self.n - period):
                if self.sequence[i] == self.sequence[i + period]:
                    matches += 1
                total += 1
            
            if total > 0 and matches / total > 0.7:
                print(f"发现可能的周期性: 周期={period}, 匹配率={matches/total:.3f}")
                potential_algorithms.append(f"周期性算法(周期={period})")
        
        # 检查简单的数学关系
        # 检查是否是基于位置的函数
        position_based = True
        for i in range(min(10, self.n)):
            expected = ((i * 3 + 1) % 8) + 1  # 示例：简单的位置函数
            if self.sequence[i] != expected:
                position_based = False
                break
        
        if position_based:
            potential_algorithms.append("基于位置的数学函数")
        
        if not potential_algorithms:
            potential_algorithms.append("复杂的PRNG或真随机数")
        
        return potential_algorithms

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机生成1-8.txt")
    analyzer = SequenceCorrelationAnalyzer(sequence)
    
    # 运行分析
    results = analyzer.run_analysis()
    
    # 检测可能的算法
    potential_algorithms = analyzer.detect_potential_algorithms()
    
    print("\n=== 总结 ===")
    print("可能的算法类型:")
    for i, algo in enumerate(potential_algorithms, 1):
        print(f"  {i}. {algo}")
