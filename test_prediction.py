#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预测算法
验证我们逆向分析得出的随机数生成算法
"""

class RandomGenerator:
    """基于逆向分析的随机数生成器"""
    def __init__(self, seed=51):
        self.state = seed
        self.a = 1664525
        self.c = 1013904223
        self.m = 4294967296
    
    def next(self):
        self.state = (self.a * self.state + self.c) % self.m
        return (self.state % 8) + 1
    
    def set_seed(self, seed):
        self.state = seed

def load_original_sequence():
    """加载原始序列"""
    with open("随机生成1-8.txt", 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

def test_algorithm():
    """测试算法准确性"""
    print("=== 测试随机数生成算法 ===\n")
    
    # 加载原始序列
    original = load_original_sequence()
    print(f"原始序列长度: {len(original)}")
    print(f"原始序列: {original}\n")
    
    # 使用我们的算法生成序列
    rng = RandomGenerator(seed=51)
    generated = []
    for _ in range(len(original)):
        generated.append(rng.next())
    
    print(f"生成序列: {generated}\n")
    
    # 比较结果
    matches = 0
    print("详细比较 (前30个):")
    print("位置  原始  生成  匹配")
    print("-" * 25)
    
    for i in range(min(30, len(original))):
        orig = original[i]
        gen = generated[i]
        match = "✓" if orig == gen else "✗"
        if orig == gen:
            matches += 1
        print(f"{i+1:4d}  {orig:4d}  {gen:4d}   {match}")
    
    total_matches = sum(1 for i in range(len(original)) if original[i] == generated[i])
    match_rate = total_matches / len(original)
    
    print(f"\n总体匹配结果:")
    print(f"匹配数量: {total_matches}/{len(original)}")
    print(f"匹配率: {match_rate:.3f} ({match_rate*100:.1f}%)")
    
    # 预测后续数字
    print(f"\n=== 预测后续随机数 ===")
    predictions = []
    for i in range(20):
        predictions.append(rng.next())
    
    print(f"预测的后续20个数字: {predictions}")
    
    # 分析预测的统计特性
    from collections import Counter
    pred_counter = Counter(predictions)
    print(f"\n预测数字的分布:")
    for num in range(1, 9):
        count = pred_counter.get(num, 0)
        freq = count / len(predictions)
        print(f"  数字 {num}: {count} 次 ({freq:.3f})")
    
    return match_rate, predictions

def alternative_algorithm_test():
    """测试备选算法"""
    print(f"\n=== 测试备选算法 (小参数LCG) ===")
    
    class SmallLCG:
        def __init__(self, seed=3):
            self.state = seed
            self.a = 17
            self.c = 5
            self.m = 256
        
        def next(self):
            self.state = (self.a * self.state + self.c) % self.m
            return (self.state % 8) + 1
    
    original = load_original_sequence()
    rng = SmallLCG(seed=3)
    generated = []
    
    for _ in range(len(original)):
        generated.append(rng.next())
    
    matches = sum(1 for i in range(len(original)) if original[i] == generated[i])
    match_rate = matches / len(original)
    
    print(f"小参数LCG匹配率: {match_rate:.3f} ({matches}/{len(original)})")
    
    # 预测
    predictions = []
    for i in range(10):
        predictions.append(rng.next())
    print(f"小参数LCG预测: {predictions}")
    
    return match_rate

if __name__ == "__main__":
    # 测试主算法
    main_match_rate, main_predictions = test_algorithm()
    
    # 测试备选算法
    alt_match_rate = alternative_algorithm_test()
    
    print(f"\n=== 最终总结 ===")
    print(f"主算法 (MSVC LCG) 匹配率: {main_match_rate:.3f}")
    print(f"备选算法 (小参数LCG) 匹配率: {alt_match_rate:.3f}")
    
    if main_match_rate >= alt_match_rate:
        print(f"推荐使用主算法进行预测")
        print(f"预测的后续数字: {main_predictions}")
    else:
        print(f"推荐使用备选算法进行预测")
    
    print(f"\n注意: 匹配率约26%表明:")
    print(f"1. 游戏可能使用了更复杂的随机数生成机制")
    print(f"2. 可能存在多个随机源或额外的变换")
    print(f"3. 需要更多数据样本来提高分析准确性")
    print(f"4. 当前算法在实际应用中仅供参考")
