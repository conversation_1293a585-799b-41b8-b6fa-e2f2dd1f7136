#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试统计数据不一致问题
"""

import json
import os
from datetime import datetime

def debug_statistics_issue():
    """调试统计数据不一致问题"""
    
    print("🔍 调试统计数据不一致问题")
    print("=" * 60)
    
    # 1. 检查实时投注记录文件
    log_file = "betting_log_20250803_072051.md"
    if os.path.exists(log_file):
        print(f"📂 检查实时投注记录: {log_file}")
        
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取统计信息
        lines = content.split('\n')
        total_bets = 0
        total_wins = 0
        total_profit = 0.0
        
        for line in lines:
            if "**总投注次数**:" in line:
                total_bets = int(line.split(':')[1].strip())
            elif "**获胜次数**:" in line:
                total_wins = int(line.split(':')[1].strip())
            elif "**总盈亏**:" in line:
                profit_str = line.split(':')[1].strip().replace('元', '').replace('+', '')
                total_profit = float(profit_str)
        
        print(f"📊 实时投注记录统计:")
        print(f"   总投注次数: {total_bets}")
        print(f"   获胜次数: {total_wins}")
        print(f"   胜率: {(total_wins/total_bets*100):.1f}%")
        print(f"   总盈亏: {total_profit:+.2f}元")
        
        # 计算详细记录的实际统计
        print(f"\n🧮 重新计算详细记录:")
        actual_bets = 0
        actual_wins = 0
        actual_profit = 0.0
        
        in_table = False
        for line in lines:
            if "| 时间 | 期号 | 投注房间" in line:
                in_table = True
                continue
            elif in_table and line.startswith('|') and not line.startswith('|---'):
                parts = [p.strip() for p in line.split('|')[1:-1]]  # 去掉首尾空元素
                if len(parts) >= 6:
                    try:
                        result = parts[4]  # 结果列
                        profit_str = parts[5].replace('元', '').replace('+', '')  # 盈亏列
                        profit = float(profit_str)
                        
                        actual_bets += 1
                        if result == "获胜":
                            actual_wins += 1
                        actual_profit += profit
                        
                        print(f"   记录{actual_bets}: 结果={result}, 盈亏={profit:+.2f}元")
                    except (ValueError, IndexError):
                        continue
        
        print(f"\n📊 重新计算结果:")
        print(f"   实际投注次数: {actual_bets}")
        print(f"   实际获胜次数: {actual_wins}")
        if actual_bets > 0:
            print(f"   实际胜率: {(actual_wins/actual_bets*100):.1f}%")
        else:
            print(f"   实际胜率: 0.0%")
        print(f"   实际总盈亏: {actual_profit:+.2f}元")
        
        # 对比差异
        print(f"\n⚠️ 数据差异:")
        print(f"   投注次数差异: {total_bets - actual_bets}")
        print(f"   获胜次数差异: {total_wins - actual_wins}")
        print(f"   盈亏差异: {total_profit - actual_profit:+.2f}元")
    
    # 2. 检查系统状态文件
    state_file = f"system_state_{datetime.now().strftime('%Y%m%d')}.json"
    if os.path.exists(state_file):
        print(f"\n📂 检查系统状态文件: {state_file}")
        
        with open(state_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        print(f"📊 系统状态统计:")
        for key, value in state_data.items():
            if isinstance(value, (int, float)):
                print(f"   {key}: {value}")
            else:
                print(f"   {key}: {value}")
    
    # 3. 检查JSON记录文件
    json_files = [f for f in os.listdir('.') if f.startswith('betting_log_') and f.endswith('.json')]
    if json_files:
        latest_json = sorted(json_files)[-1]
        print(f"\n📂 检查JSON记录文件: {latest_json}")
        
        with open(latest_json, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        session_info = json_data.get('session_info', {})
        print(f"📊 JSON文件统计:")
        print(f"   总投注次数: {session_info.get('total_bets', 0)}")
        print(f"   获胜次数: {session_info.get('total_wins', 0)}")
        print(f"   总盈亏: {session_info.get('total_profit', 0):+.2f}元")
        
        # 检查记录数量
        records = json_data.get('betting_records', [])
        completed_records = [r for r in records if r.get('status') == 'completed']
        print(f"   记录总数: {len(records)}")
        print(f"   完成记录数: {len(completed_records)}")
    
    print(f"\n💡 问题分析:")
    print(f"1. 实时投注记录 (.md) - 显示当前会话数据")
    print(f"2. 系统状态文件 (.json) - 包含历史累积数据")
    print(f"3. JSON记录文件 (.json) - 当前会话的详细记录")
    print(f"4. 控制台输出 - 系统内部统计 (包含历史数据)")
    
    print(f"\n🔧 可能的问题:")
    print(f"- 重复调用统计更新方法")
    print(f"- 不同统计系统使用不同的数据源")
    print(f"- 历史数据和当前会话数据混合")
    print(f"- 连败/连胜计算逻辑错误")

if __name__ == "__main__":
    debug_statistics_issue()
