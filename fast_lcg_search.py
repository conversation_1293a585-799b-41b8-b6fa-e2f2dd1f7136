#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速LCG搜索
针对2000个数据点的高效搜索
"""

import numpy as np
from typing import List, Tuple, Dict
import time

class FastLCGSearcher:
    def __init__(self, target_sequence: List[int]):
        """初始化快速LCG搜索器"""
        self.target_sequence = target_sequence
        self.n = len(target_sequence)
        print(f"目标序列长度: {self.n}")
        
    def lcg_generator_fast(self, seed: int, a: int, c: int, m: int, count: int) -> List[int]:
        """快速LCG生成器 - 只生成需要的数量"""
        sequence = []
        x = seed
        for _ in range(min(count, 100)):  # 只生成前100个进行快速匹配
            x = (a * x + c) % m
            mapped = (x % 8) + 1
            sequence.append(mapped)
        return sequence
    
    def quick_match_test(self, generated: List[int]) -> float:
        """快速匹配测试 - 只测试前100个"""
        test_length = min(len(generated), len(self.target_sequence), 100)
        matches = sum(1 for i in range(test_length) 
                     if generated[i] == self.target_sequence[i])
        return matches / test_length
    
    def full_match_test(self, seed: int, a: int, c: int, m: int) -> float:
        """完整匹配测试"""
        matches = 0
        x = seed
        for i in range(self.n):
            x = (a * x + c) % m
            mapped = (x % 8) + 1
            if mapped == self.target_sequence[i]:
                matches += 1
        return matches / self.n
    
    def search_priority_parameters(self) -> List[Tuple]:
        """搜索优先级参数"""
        print("=== 搜索优先级LCG参数 ===")
        
        # 高优先级参数 - 基于之前的分析结果
        priority_params = [
            # 最常见的标准库参数
            (1664525, 1013904223, 2**32),
            (214013, 2531011, 2**32),
            (1103515245, 12345, 2**31),
            (16807, 0, 2**31 - 1),
            (48271, 0, 2**31 - 1),
            
            # 小参数但有效的LCG
            (17, 5, 256),
            (37, 3, 256),
            (13, 11, 64),
            (29, 3, 128),
            (25, 7, 256),
            
            # 中等参数
            (1103515245, 12345, 65536),
            (214013, 2531011, 65536),
            (75, 74, 65537),
            
            # 游戏常用的简单参数
            (7, 5, 32),
            (11, 7, 32),
            (15, 1, 32),
            (21, 5, 64),
            (23, 9, 64),
            (41, 7, 256),
            (45, 21, 128),
        ]
        
        results = []
        
        for i, (a, c, m) in enumerate(priority_params):
            print(f"  测试 {i+1}/{len(priority_params)}: LCG({a}, {c}, {m})")
            
            best_match = 0
            best_seed = 0
            
            # 智能种子选择
            seed_candidates = [1, 2, 3, 7, 11, 13, 17, 19, 23, 31, 37, 41, 43, 47, 51, 59, 61, 67, 71, 73]
            
            # 添加基于模数的种子
            if m > 100:
                seed_candidates.extend([m//4, m//3, m//2, m//8, m//16])
            
            # 添加一些随机种子
            seed_candidates.extend(range(1, min(200, m//10 if m > 1000 else m), max(1, min(50, m//100))))
            
            # 去重并过滤
            seed_candidates = list(set([s for s in seed_candidates if 0 < s < m]))
            
            for seed in seed_candidates:
                try:
                    # 快速测试
                    quick_gen = self.lcg_generator_fast(seed, a, c, m, 100)
                    quick_match = self.quick_match_test(quick_gen)
                    
                    if quick_match > 0.2:  # 快速测试通过才进行完整测试
                        full_match = self.full_match_test(seed, a, c, m)
                        
                        if full_match > best_match:
                            best_match = full_match
                            best_seed = seed
                            
                except (OverflowError, ZeroDivisionError):
                    continue
            
            if best_match > 0.15:  # 降低阈值以获得更多候选
                results.append((a, c, m, best_seed, best_match))
                print(f"    找到匹配: seed={best_seed}, 匹配率={best_match:.4f}")
        
        return sorted(results, key=lambda x: x[4], reverse=True)
    
    def test_advanced_mappings(self, a: int, c: int, m: int, seed: int) -> List[Tuple]:
        """测试高级映射函数"""
        print(f"  测试高级映射 for LCG({a}, {c}, {m}), seed={seed}")
        
        # 生成内部状态
        internal_states = []
        x = seed
        for _ in range(self.n):
            x = (a * x + c) % m
            internal_states.append(x)
        
        # 高级映射函数
        mappings = [
            ("(x % 8) + 1", lambda x: (x % 8) + 1),
            ("((x >> 3) % 8) + 1", lambda x: ((x >> 3) % 8) + 1),
            ("((x >> 8) % 8) + 1", lambda x: ((x >> 8) % 8) + 1),
            ("((x >> 16) % 8) + 1", lambda x: ((x >> 16) % 8) + 1),
            ("((x // (m//8)) % 8) + 1", lambda x: ((x // max(1, m//8)) % 8) + 1),
            ("((x * 8) // m) + 1", lambda x: min(((x * 8) // m) + 1, 8)),
            ("((x ^ (x >> 4)) % 8) + 1", lambda x: ((x ^ (x >> 4)) % 8) + 1),
            ("((x + (x >> 8)) % 8) + 1", lambda x: ((x + (x >> 8)) % 8) + 1),
            ("(((x >> 1) ^ x) % 8) + 1", lambda x: (((x >> 1) ^ x) % 8) + 1),
            ("((x % 7) + 1)", lambda x: (x % 7) + 1 if (x % 7) + 1 <= 8 else 8),
        ]
        
        results = []
        
        for name, mapping_func in mappings:
            try:
                mapped_sequence = [mapping_func(state) for state in internal_states]
                matches = sum(1 for i in range(self.n) 
                            if mapped_sequence[i] == self.target_sequence[i])
                match_rate = matches / self.n
                
                if match_rate > 0.2:
                    results.append((name, match_rate, mapped_sequence))
                    print(f"    {name}: 匹配率 {match_rate:.4f}")
                    
            except (ZeroDivisionError, OverflowError):
                continue
        
        return sorted(results, key=lambda x: x[1], reverse=True)
    
    def run_fast_search(self) -> Dict:
        """运行快速搜索"""
        print("开始快速LCG搜索...")
        print(f"目标序列前20个: {self.target_sequence[:20]}")
        print()
        
        start_time = time.time()
        
        # 搜索优先级参数
        priority_results = self.search_priority_parameters()
        
        # 对最佳结果测试高级映射
        best_with_mappings = []
        if priority_results:
            print(f"\n=== 测试最佳候选的高级映射 ===")
            for i, (a, c, m, seed, match_rate) in enumerate(priority_results[:5]):
                print(f"\n候选 {i+1}: LCG({a}, {c}, {m}), seed={seed}, 基础匹配率={match_rate:.4f}")
                
                mapping_results = self.test_advanced_mappings(a, c, m, seed)
                
                for map_name, map_match_rate, mapped_seq in mapping_results:
                    best_with_mappings.append({
                        'lcg_params': (a, c, m),
                        'seed': seed,
                        'mapping': map_name,
                        'match_rate': map_match_rate,
                        'sequence': mapped_seq
                    })
        
        # 按匹配率排序
        best_with_mappings.sort(key=lambda x: x['match_rate'], reverse=True)
        
        elapsed_time = time.time() - start_time
        print(f"\n快速搜索完成，耗时 {elapsed_time:.2f} 秒")
        
        return {
            'priority_results': priority_results,
            'best_with_mappings': best_with_mappings,
            'search_time': elapsed_time
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    searcher = FastLCGSearcher(sequence)
    results = searcher.run_fast_search()
    
    print(f"\n=== 快速搜索结果总结 ===")
    
    if results['best_with_mappings']:
        print("最佳LCG候选 (含高级映射):")
        for i, result in enumerate(results['best_with_mappings'][:5], 1):
            a, c, m = result['lcg_params']
            print(f"\n{i}. LCG({a}, {c}, {m})")
            print(f"   种子: {result['seed']}")
            print(f"   映射: {result['mapping']}")
            print(f"   匹配率: {result['match_rate']:.4f}")
            
            # 显示前20个匹配情况
            print("   前20个匹配情况:")
            for j in range(min(20, len(result['sequence']))):
                target = sequence[j]
                generated = result['sequence'][j]
                match = "✓" if target == generated else "✗"
                print(f"     {j+1:2d}: {target} vs {generated} {match}")
    else:
        print("未找到高匹配率的LCG参数")
    
    print(f"\n搜索时间: {results['search_time']:.2f} 秒")
