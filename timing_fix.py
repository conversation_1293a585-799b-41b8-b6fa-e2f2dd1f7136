#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
投注时机修复脚本
在现有系统基础上添加正确的投注时机
"""

import time
import json
from datetime import datetime
from collections import Counter
from api_framework import GameAPIClient

def get_betting_recommendation():
    """获取投注建议"""
    
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
    except:
        print("❌ 无法加载历史数据")
        return None
    
    if len(history) < 10:
        print("📊 历史数据不足")
        return None
    
    # 频率分析
    recent_data = history[-20:] if len(history) >= 20 else history
    counter = Counter(recent_data)
    
    # 计算投注价值
    betting_values = {}
    for room in range(1, 9):
        frequency = counter.get(room, 0)
        betting_values[room] = 1 - (frequency / len(recent_data))
    
    # 选择投注房间 (按投注价值排序)
    sorted_rooms = sorted(betting_values.items(), key=lambda x: x[1], reverse=True)
    target_rooms = [room for room, value in sorted_rooms[:3]]  # 提供前3个作为选择

    return {
        'target_rooms': target_rooms,
        'frequency_distribution': dict(counter),
        'total_samples': len(recent_data),
        'betting_values': betting_values,
        'best_room_info': {
            'room': target_rooms[0],
            'value': sorted_rooms[0][1],
            'frequency': counter.get(target_rooms[0], 0)
        }
    }

def execute_timed_betting():
    """执行定时投注"""
    
    print("🎯 投注时机修复系统")
    print("=" * 50)
    print("在正确的时机进行投注")
    print()
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    # 创建API客户端
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    
    last_bet_issue = 0
    
    print("🚀 开始监控投注时机...")
    print("按 Ctrl+C 停止")
    print()
    
    try:
        while True:
            # 获取游戏状态
            state = api_client.get_game_state()
            
            if state:
                current_time = datetime.now().strftime('%H:%M:%S')
                
                if state.state == 1:  # 等待开奖状态
                    print(f"📊 [{current_time}] 期号: {state.issue}, 倒计时: {state.countdown}秒")
                    
                    # 投注时机：倒计时在15-25秒之间，且未投注过此期
                    if 15 <= state.countdown <= 25 and state.issue > last_bet_issue:
                        print(f"🎯 投注时机到达！期号{state.issue}")
                        
                        # 获取投注建议
                        recommendation = get_betting_recommendation()
                        
                        if recommendation:
                            target_rooms = recommendation['target_rooms']
                            # 只选择最优的一个房间
                            best_room = target_rooms[0]
                            bet_amount = 0.1

                            print(f"💰 投注策略 (单房间):")
                            print(f"   最优房间: {best_room}")
                            print(f"   备选房间: {target_rooms[1:] if len(target_rooms) > 1 else '无'}")
                            print(f"   投注金额: {bet_amount}")
                            print(f"   频率分布: {recommendation['frequency_distribution']}")

                            # 只投注一个房间
                            print(f"🎯 投注房间{best_room}，金额{bet_amount:.2f}")

                            bet_result = api_client.place_bet(best_room, bet_amount)

                            if bet_result.success:
                                last_bet_issue = state.issue
                                print(f"✅ 房间{best_room}投注成功")
                                print(f"📝 成功投注1个房间")

                                # 显示投注响应详情
                                if hasattr(bet_result, 'data') and bet_result.data:
                                    print(f"📊 投注详情: {bet_result.data}")
                            else:
                                print(f"❌ 房间{best_room}投注失败: {bet_result.message}")
                        else:
                            print("📊 无法获取投注建议")
                    
                elif state.state == 2:  # 已开奖状态
                    print(f"🟢 [{current_time}] 期号: {state.issue}, 开出房间: {state.kill_number}")
                
                # 等待3秒
                time.sleep(3)
            else:
                print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] 无法获取游戏状态")
                time.sleep(5)
                
    except KeyboardInterrupt:
        print("\n🛑 停止投注时机监控")
    
    print("✅ 投注时机修复系统已停止")

def main():
    """主函数"""
    
    print("🔧 投注时机修复工具")
    print("=" * 50)
    print("解决'当前已经停止投入'的问题")
    print()
    
    # 检查历史数据
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
        
        print(f"✅ 历史数据: {len(history)}期")
        if len(history) >= 10:
            print(f"📊 最近10期: {history[-10:]}")
            
            # 显示当前投注建议
            recommendation = get_betting_recommendation()
            if recommendation:
                print(f"🎯 当前投注建议: {recommendation['target_rooms']}")
                print(f"📊 频率分布: {recommendation['frequency_distribution']}")
        else:
            print("⚠️  历史数据不足，无法生成投注建议")
            return
            
    except Exception as e:
        print(f"❌ 无法加载历史数据: {e}")
        return
    
    print(f"\n💡 修复策略:")
    print("• 监控游戏状态")
    print("• 在倒计时15-25秒时投注")
    print("• 避免重复投注同一期")
    print("• 基于频率分析选择房间")
    
    response = input("\n是否启动投注时机修复系统？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        execute_timed_betting()
    else:
        print("系统未启动")

if __name__ == "__main__":
    main()
