#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多种随机算法对比分析器
找出与历史开奖号码相同次数最少的随机算法
"""

import json
import random
import numpy as np
import hashlib
import time
from datetime import datetime
from typing import List, Dict, Tuple
from collections import Counter


class MultiRandomAlgorithmAnalyzer:
    """多种随机算法分析器"""
    
    def __init__(self, history_file: str = "game_history.json"):
        """初始化分析器"""
        self.history_data = self.load_history_data(history_file)
        self.history = self.history_data["history"]
        self.total_periods = len(self.history)
        
        print(f"📊 历史数据加载完成:")
        print(f"   总期数: {self.total_periods}")
        print(f"   数据范围: 房间1-8")
        print(f"   最后更新: {self.history_data.get('last_updated', 'Unknown')}")
        
        # 分析历史数据分布
        self.analyze_historical_distribution()
    
    def load_history_data(self, filename: str) -> Dict:
        """加载历史数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载历史数据失败: {e}")
            return {"history": [], "total_periods": 0}
    
    def analyze_historical_distribution(self):
        """分析历史数据分布"""
        counter = Counter(self.history)
        
        print(f"\n📈 历史数据分布:")
        for room in range(1, 9):
            count = counter.get(room, 0)
            percentage = (count / self.total_periods) * 100
            print(f"   房间{room}: {count}次 ({percentage:.2f}%)")
        
        # 计算理论期望
        theoretical_avg = self.total_periods / 8
        print(f"\n🎯 理论期望: 每房间{theoretical_avg:.1f}次 (12.5%)")
    
    def pure_random_algorithm(self, seed: int = None) -> List[int]:
        """纯随机算法 (Python内置random)"""
        if seed is not None:
            random.seed(seed)
        return [random.randint(1, 8) for _ in range(self.total_periods)]
    
    def numpy_random_algorithm(self, seed: int = None) -> List[int]:
        """NumPy随机算法"""
        if seed is not None:
            np.random.seed(seed)
        return np.random.randint(1, 9, self.total_periods).tolist()
    
    def linear_congruential_generator(self, seed: int = 12345) -> List[int]:
        """线性同余生成器 (LCG)"""
        # 使用标准LCG参数: a=1664525, c=1013904223, m=2^32
        a, c, m = 1664525, 1013904223, 2**32
        x = seed
        result = []
        
        for _ in range(self.total_periods):
            x = (a * x + c) % m
            result.append((x % 8) + 1)
        
        return result
    
    def xorshift_algorithm(self, seed: int = 123456789) -> List[int]:
        """Xorshift随机算法"""
        x = seed
        result = []
        
        for _ in range(self.total_periods):
            x ^= x << 13
            x ^= x >> 17
            x ^= x << 5
            x = x & 0xFFFFFFFF  # 保持32位
            result.append((x % 8) + 1)
        
        return result
    
    def mersenne_twister_algorithm(self, seed: int = None) -> List[int]:
        """Mersenne Twister算法 (通过numpy实现)"""
        rng = np.random.RandomState(seed)
        return rng.randint(1, 9, self.total_periods).tolist()
    
    def hash_based_algorithm(self, seed: str = "random_seed") -> List[int]:
        """基于哈希的随机算法"""
        result = []
        
        for i in range(self.total_periods):
            # 使用期数和种子生成哈希
            hash_input = f"{seed}_{i}".encode('utf-8')
            hash_value = hashlib.md5(hash_input).hexdigest()
            # 取哈希值的前8位转换为整数
            hash_int = int(hash_value[:8], 16)
            result.append((hash_int % 8) + 1)
        
        return result
    
    def fibonacci_based_algorithm(self, seed1: int = 1, seed2: int = 1) -> List[int]:
        """基于斐波那契数列的随机算法"""
        a, b = seed1, seed2
        result = []
        
        for _ in range(self.total_periods):
            a, b = b, (a + b) % 1000000  # 防止数字过大
            result.append((b % 8) + 1)
        
        return result
    
    def time_based_algorithm(self) -> List[int]:
        """基于时间的随机算法"""
        result = []
        base_time = int(time.time() * 1000000)  # 微秒时间戳
        
        for i in range(self.total_periods):
            # 使用时间戳和索引生成随机数
            time_value = (base_time + i * 1234567) % 1000000
            result.append((time_value % 8) + 1)
        
        return result
    
    def weighted_random_algorithm(self, weights: List[float] = None) -> List[int]:
        """加权随机算法 (反向权重，低频房间权重更高)"""
        if weights is None:
            # 基于历史频率计算反向权重
            counter = Counter(self.history)
            max_count = max(counter.values())
            weights = []
            for room in range(1, 9):
                count = counter.get(room, 0)
                # 反向权重：出现次数越少，权重越高
                weight = max_count - count + 1
                weights.append(weight)
        
        result = []
        for _ in range(self.total_periods):
            room = np.random.choice(range(1, 9), p=np.array(weights)/sum(weights))
            result.append(room)
        
        return result
    
    def calculate_match_rate(self, predicted: List[int]) -> Dict:
        """计算匹配率和统计信息"""
        matches = sum(1 for i in range(len(self.history)) if predicted[i] == self.history[i])
        match_rate = matches / len(self.history)
        
        # 计算预测分布
        pred_counter = Counter(predicted)
        hist_counter = Counter(self.history)
        
        # 计算各房间的匹配情况
        room_matches = {}
        for room in range(1, 9):
            room_matches[room] = sum(1 for i in range(len(self.history)) 
                                   if predicted[i] == room and self.history[i] == room)
        
        return {
            'total_matches': matches,
            'match_rate': match_rate,
            'avoid_rate': 1 - match_rate,  # 避开率 (我们想要的)
            'predicted_distribution': dict(pred_counter),
            'room_matches': room_matches,
            'theoretical_matches': len(self.history) / 8,  # 理论匹配次数
            'performance_ratio': matches / (len(self.history) / 8)  # 相对理论的表现
        }
    
    def run_comprehensive_analysis(self) -> Dict:
        """运行综合分析"""
        print(f"\n🔬 开始多种随机算法对比分析...")
        print(f"   测试数据: {self.total_periods}期历史数据")
        print(f"   目标: 找出匹配次数最少的算法")
        print("=" * 60)
        
        algorithms = {
            "纯随机 (Python)": lambda: self.pure_random_algorithm(42),
            "NumPy随机": lambda: self.numpy_random_algorithm(42),
            "线性同余生成器": lambda: self.linear_congruential_generator(12345),
            "Xorshift算法": lambda: self.xorshift_algorithm(123456789),
            "Mersenne Twister": lambda: self.mersenne_twister_algorithm(42),
            "哈希随机": lambda: self.hash_based_algorithm("optimal_seed"),
            "斐波那契随机": lambda: self.fibonacci_based_algorithm(1, 1),
            "时间随机": lambda: self.time_based_algorithm(),
            "反向权重随机": lambda: self.weighted_random_algorithm()
        }
        
        results = {}
        
        for name, algorithm in algorithms.items():
            print(f"\n🧪 测试算法: {name}")
            
            try:
                predicted = algorithm()
                stats = self.calculate_match_rate(predicted)
                
                results[name] = stats
                
                print(f"   匹配次数: {stats['total_matches']}/{self.total_periods}")
                print(f"   匹配率: {stats['match_rate']:.4f} ({stats['match_rate']*100:.2f}%)")
                print(f"   避开率: {stats['avoid_rate']:.4f} ({stats['avoid_rate']*100:.2f}%)")
                print(f"   理论匹配: {stats['theoretical_matches']:.1f}次")
                print(f"   性能比: {stats['performance_ratio']:.3f} (越小越好)")
                
            except Exception as e:
                print(f"   ❌ 算法执行失败: {e}")
                results[name] = None
        
        return results
    
    def find_optimal_algorithm(self, results: Dict) -> Tuple[str, Dict]:
        """找出最优算法 (匹配次数最少)"""
        valid_results = {name: stats for name, stats in results.items() if stats is not None}
        
        if not valid_results:
            return None, None
        
        # 按匹配次数排序 (越少越好)
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['total_matches'])
        
        best_name, best_stats = sorted_results[0]
        
        print(f"\n🏆 最优算法: {best_name}")
        print(f"   匹配次数: {best_stats['total_matches']}次")
        print(f"   避开率: {best_stats['avoid_rate']*100:.2f}%")
        print(f"   相比理论减少: {(1-best_stats['performance_ratio'])*100:.1f}%")
        
        return best_name, best_stats
    
    def generate_analysis_report(self, results: Dict) -> str:
        """生成分析报告"""
        report = f"""# 🎲 多种随机算法对比分析报告

## 📊 测试概况
- **历史数据**: {self.total_periods}期
- **测试算法**: {len(results)}种
- **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **目标**: 找出与开奖号码匹配次数最少的随机算法

## 🔬 算法性能排行榜

| 排名 | 算法名称 | 匹配次数 | 匹配率 | 避开率 | 性能比 |
|------|----------|----------|--------|--------|--------|"""
        
        # 按匹配次数排序
        valid_results = {name: stats for name, stats in results.items() if stats is not None}
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['total_matches'])
        
        for i, (name, stats) in enumerate(sorted_results, 1):
            report += f"""
| {i} | {name} | {stats['total_matches']} | {stats['match_rate']*100:.2f}% | {stats['avoid_rate']*100:.2f}% | {stats['performance_ratio']:.3f} |"""
        
        # 添加最优算法详细分析
        if sorted_results:
            best_name, best_stats = sorted_results[0]
            report += f"""

## 🏆 最优算法详细分析

### {best_name}
- **匹配次数**: {best_stats['total_matches']}次 (理论{best_stats['theoretical_matches']:.1f}次)
- **避开率**: {best_stats['avoid_rate']*100:.2f}% (理论87.5%)
- **性能优势**: 比理论减少{(1-best_stats['performance_ratio'])*100:.1f}%的匹配

### 各房间匹配分布
"""
            for room in range(1, 9):
                matches = best_stats['room_matches'][room]
                predicted_count = best_stats['predicted_distribution'].get(room, 0)
                report += f"- **房间{room}**: 匹配{matches}次, 预测{predicted_count}次\n"
        
        report += f"""

## 💡 结论与建议

1. **最优选择**: {sorted_results[0][0]} 算法表现最佳
2. **避开效果**: 相比纯随机，最优算法能减少{(1-sorted_results[0][1]['performance_ratio'])*100:.1f}%的匹配
3. **实用建议**: 在投注系统中使用最优算法可以提高避开率
4. **风险提醒**: 历史表现不代表未来结果，仍需谨慎使用

## 📈 投注策略建议

基于分析结果，建议在纯随机房间选择策略中使用 **{sorted_results[0][0]}** 算法，
预期可以将避开率从理论的87.5%提升到{sorted_results[0][1]['avoid_rate']*100:.2f}%。
"""
        
        return report


def main():
    """主函数"""
    print("🎲 多种随机算法对比分析系统")
    print("=" * 50)
    
    # 创建分析器
    analyzer = MultiRandomAlgorithmAnalyzer("game_history.json")
    
    # 运行综合分析
    results = analyzer.run_comprehensive_analysis()
    
    # 找出最优算法
    best_name, best_stats = analyzer.find_optimal_algorithm(results)
    
    # 生成报告
    report = analyzer.generate_analysis_report(results)
    
    # 保存报告
    report_filename = f"随机算法对比分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📝 分析报告已保存: {report_filename}")
    
    return best_name, best_stats, results


if __name__ == "__main__":
    main()
