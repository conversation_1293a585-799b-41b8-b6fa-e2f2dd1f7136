#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级时间序列分析
使用时间序列方法寻找隐藏的周期性和趋势
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import Counter
import scipy.signal as signal
from scipy import stats

class AdvancedTimeSeriesAnalyzer:
    def __init__(self, sequence: List[int]):
        """初始化高级时间序列分析器"""
        self.sequence = sequence
        self.n = len(sequence)
        
    def wavelet_analysis(self) -> Dict:
        """小波分析"""
        print("=== 小波分析 ===")
        
        # 简化的小波分析（使用连续小波变换的近似）
        seq_array = np.array(self.sequence, dtype=float)
        
        # 使用不同尺度的高斯小波
        scales = np.arange(1, 50)
        significant_scales = []
        
        for scale in scales:
            # 创建高斯小波
            wavelet_size = min(scale * 10, len(seq_array) // 4)
            if wavelet_size < 3:
                continue
                
            t = np.arange(-wavelet_size//2, wavelet_size//2 + 1)
            wavelet = np.exp(-t**2 / (2 * scale**2)) * np.cos(2 * np.pi * t / scale)
            
            # 卷积
            if len(wavelet) < len(seq_array):
                convolution = np.convolve(seq_array, wavelet, mode='valid')
                energy = np.sum(convolution**2)
                
                if energy > np.mean([np.sum(np.convolve(seq_array, 
                    np.exp(-t**2 / (2 * s**2)) * np.cos(2 * np.pi * t / s), mode='valid')**2) 
                    for s in [scale-1, scale+1] if s > 0]) * 1.5:
                    significant_scales.append((scale, energy))
        
        significant_scales.sort(key=lambda x: x[1], reverse=True)
        
        print(f"发现 {len(significant_scales)} 个显著尺度:")
        for scale, energy in significant_scales[:5]:
            print(f"  尺度 {scale}: 能量 {energy:.2e}")
        
        return {
            'significant_scales': significant_scales,
            'total_scales_tested': len(scales)
        }
    
    def detrended_fluctuation_analysis(self) -> Dict:
        """去趋势波动分析 (DFA)"""
        print(f"\n=== 去趋势波动分析 ===")
        
        # 计算累积和
        y = np.cumsum(np.array(self.sequence) - np.mean(self.sequence))
        
        # 不同的窗口大小
        scales = np.logspace(1, np.log10(len(y)//4), 20).astype(int)
        fluctuations = []
        
        for scale in scales:
            # 将序列分段
            n_segments = len(y) // scale
            if n_segments < 4:
                continue
                
            segments = y[:n_segments * scale].reshape(n_segments, scale)
            
            # 对每段进行线性去趋势
            detrended_vars = []
            for segment in segments:
                x = np.arange(len(segment))
                coeffs = np.polyfit(x, segment, 1)
                trend = np.polyval(coeffs, x)
                detrended = segment - trend
                detrended_vars.append(np.var(detrended))
            
            # 计算平均波动
            avg_fluctuation = np.sqrt(np.mean(detrended_vars))
            fluctuations.append(avg_fluctuation)
        
        # 计算Hurst指数
        if len(fluctuations) > 5:
            log_scales = np.log(scales[:len(fluctuations)])
            log_fluctuations = np.log(fluctuations)
            
            # 线性拟合
            slope, intercept, r_value, p_value, std_err = stats.linregress(log_scales, log_fluctuations)
            hurst_exponent = slope
            
            print(f"Hurst指数: {hurst_exponent:.4f}")
            print(f"相关系数: {r_value:.4f}")
            print(f"p值: {p_value:.4f}")
            
            # 解释Hurst指数
            if hurst_exponent > 0.5:
                interpretation = "持续性/趋势性"
            elif hurst_exponent < 0.5:
                interpretation = "反持续性/均值回归"
            else:
                interpretation = "随机游走"
            
            print(f"解释: {interpretation}")
        else:
            hurst_exponent = None
            r_value = None
            interpretation = "数据不足"
        
        return {
            'hurst_exponent': hurst_exponent,
            'correlation': r_value,
            'interpretation': interpretation,
            'scales': scales[:len(fluctuations)],
            'fluctuations': fluctuations
        }
    
    def recurrence_analysis(self) -> Dict:
        """递归分析"""
        print(f"\n=== 递归分析 ===")
        
        # 构建递归矩阵
        threshold = np.std(self.sequence) * 0.5  # 阈值设为标准差的一半
        
        recurrence_matrix = np.zeros((self.n, self.n))
        for i in range(self.n):
            for j in range(self.n):
                if abs(self.sequence[i] - self.sequence[j]) <= threshold:
                    recurrence_matrix[i, j] = 1
        
        # 计算递归率
        recurrence_rate = np.sum(recurrence_matrix) / (self.n * self.n)
        
        # 计算确定性
        determinism = 0
        diagonal_lines = []
        
        # 寻找对角线结构
        for offset in range(1, self.n):
            diagonal = np.diag(recurrence_matrix, offset)
            if len(diagonal) > 2:
                # 寻找连续的1
                line_lengths = []
                current_length = 0
                
                for val in diagonal:
                    if val == 1:
                        current_length += 1
                    else:
                        if current_length >= 2:  # 至少长度为2的线
                            line_lengths.append(current_length)
                        current_length = 0
                
                if current_length >= 2:
                    line_lengths.append(current_length)
                
                diagonal_lines.extend(line_lengths)
        
        if diagonal_lines:
            determinism = sum(diagonal_lines) / np.sum(recurrence_matrix)
            avg_diagonal_length = np.mean(diagonal_lines)
            max_diagonal_length = max(diagonal_lines)
        else:
            determinism = 0
            avg_diagonal_length = 0
            max_diagonal_length = 0
        
        print(f"递归率: {recurrence_rate:.4f}")
        print(f"确定性: {determinism:.4f}")
        print(f"平均对角线长度: {avg_diagonal_length:.2f}")
        print(f"最大对角线长度: {max_diagonal_length}")
        
        return {
            'recurrence_rate': recurrence_rate,
            'determinism': determinism,
            'avg_diagonal_length': avg_diagonal_length,
            'max_diagonal_length': max_diagonal_length,
            'diagonal_lines': diagonal_lines
        }
    
    def multifractal_analysis(self) -> Dict:
        """多重分形分析"""
        print(f"\n=== 多重分形分析 ===")
        
        # 简化的多重分形分析
        seq_array = np.array(self.sequence, dtype=float)
        
        # 不同的q值
        q_values = np.linspace(-5, 5, 21)
        q_values = q_values[q_values != 0]  # 排除q=0
        
        # 不同的尺度
        scales = np.logspace(1, np.log10(len(seq_array)//8), 10).astype(int)
        
        tau_q = []
        
        for q in q_values:
            log_scales = []
            log_fluctuations = []
            
            for scale in scales:
                if scale >= len(seq_array):
                    continue
                    
                # 分段
                n_segments = len(seq_array) // scale
                if n_segments < 2:
                    continue
                
                segments = seq_array[:n_segments * scale].reshape(n_segments, scale)
                
                # 计算每段的"质量"
                segment_sums = np.sum(np.abs(segments), axis=1)
                
                if np.any(segment_sums == 0):
                    continue
                
                # 计算q阶矩
                if q != 1:
                    moment = np.mean(segment_sums ** q)
                    if moment > 0:
                        log_fluctuations.append(np.log(moment) / (q - 1))
                        log_scales.append(np.log(scale))
                else:
                    # q=1的特殊情况
                    moment = np.mean(segment_sums * np.log(segment_sums + 1e-10))
                    log_fluctuations.append(moment)
                    log_scales.append(np.log(scale))
            
            # 线性拟合得到tau(q)
            if len(log_scales) > 3:
                slope, _, r_value, _, _ = stats.linregress(log_scales, log_fluctuations)
                if abs(r_value) > 0.5:  # 只保留拟合较好的结果
                    tau_q.append((q, slope))
        
        # 计算多重分形谱宽度
        if len(tau_q) > 5:
            tau_values = [tau for q, tau in tau_q]
            spectrum_width = max(tau_values) - min(tau_values)
            
            print(f"多重分形谱宽度: {spectrum_width:.4f}")
            
            if spectrum_width > 0.1:
                interpretation = "存在多重分形特征"
            else:
                interpretation = "单一分形或随机"
        else:
            spectrum_width = None
            interpretation = "分析不足"
        
        print(f"解释: {interpretation}")
        
        return {
            'tau_q': tau_q,
            'spectrum_width': spectrum_width,
            'interpretation': interpretation
        }
    
    def run_advanced_analysis(self) -> Dict:
        """运行高级时间序列分析"""
        print("开始高级时间序列分析...")
        print(f"序列长度: {self.n}")
        print(f"序列前20个: {self.sequence[:20]}")
        print()
        
        # 各项高级分析
        wavelet_results = self.wavelet_analysis()
        dfa_results = self.detrended_fluctuation_analysis()
        recurrence_results = self.recurrence_analysis()
        multifractal_results = self.multifractal_analysis()
        
        return {
            'wavelet': wavelet_results,
            'dfa': dfa_results,
            'recurrence': recurrence_results,
            'multifractal': multifractal_results
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    analyzer = AdvancedTimeSeriesAnalyzer(sequence)
    results = analyzer.run_advanced_analysis()
    
    print(f"\n=== 高级时间序列分析总结 ===")
    
    # 综合评估
    evidence_count = 0
    
    if results['wavelet']['significant_scales']:
        print("✓ 小波分析发现显著尺度")
        evidence_count += 1
    
    if results['dfa']['hurst_exponent'] and abs(results['dfa']['hurst_exponent'] - 0.5) > 0.05:
        print(f"✓ DFA分析显示非随机特征 (Hurst={results['dfa']['hurst_exponent']:.3f})")
        evidence_count += 1
    
    if results['recurrence']['determinism'] > 0.1:
        print(f"✓ 递归分析显示确定性结构 (确定性={results['recurrence']['determinism']:.3f})")
        evidence_count += 1
    
    if results['multifractal']['spectrum_width'] and results['multifractal']['spectrum_width'] > 0.1:
        print(f"✓ 多重分形分析显示复杂结构 (谱宽={results['multifractal']['spectrum_width']:.3f})")
        evidence_count += 1
    
    print(f"\n发现 {evidence_count} 个非随机性证据")
    
    if evidence_count >= 2:
        print("结论: 序列可能存在隐藏的确定性结构，值得进一步研究")
    else:
        print("结论: 序列表现出高度的随机性，难以找到确定性模式")
