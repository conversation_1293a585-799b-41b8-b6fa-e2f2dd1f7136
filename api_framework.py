#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调用框架
基于游戏API的自动化监控和投注系统
"""

import requests
import json
import time
import hashlib
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
import random

@dataclass
class GameState:
    """游戏状态数据类"""
    issue: int
    state: int  # 1=等待开奖, 2=已开奖
    kill_number: int  # 开出的房间号(1-8)
    my_room_number: int  # 我的房间号
    my_is_win: int  # 是否获胜 1=获胜, 0=失败
    my_win_stroke: float  # 当期获得的收益
    room_stats: Dict  # 房间统计信息 {room_number: {'total_medal': float, 'user_count': int}}
    timestamp: str
    countdown: int #当前游戏倒计时

@dataclass
class BetResult:
    """投注结果数据类"""
    success: bool
    issue: int
    room_number: int
    cost_medal: float
    message: str
    timestamp: str

class GameAPIClient:
    """游戏API客户端"""
    
    def __init__(self, base_url: str, headers: Dict[str, str]):
        """初始化API客户端"""
        self.base_url = base_url
        self.headers = headers
        self.session = requests.Session()
        self.session.headers.update(headers)
        
    def get_game_state(self) -> Optional[GameState]:
        """获取游戏状态"""
        try:
            # 更新时间戳和签名
            self.headers['ts'] = str(int(time.time() * 1000))
            self.session.headers.update(self.headers)

            url = f"{self.base_url}/v11/api/stroke/data"
            params = {
                'uid': self.headers.get('userId', ''),
                'version': self.headers.get('version', '5.2.2')
            }

            print(f"🔄 请求游戏状态: {url}")
            response = self.session.post(url, params=params, timeout=10)

            print(f"📡 响应状态: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"📊 响应数据: {str(data)[:100]}...")
                #print(f"📊 响应数据: {str(data)}")

                # 根据实际API响应格式解析数据
                # 处理killNumber为0的情况（等待开奖时）
                kill_number = data.get('killNumber', 0)
                if kill_number == 0 and data.get('state') == 2:
                    # 如果已开奖但killNumber为0，尝试从prevRoomNumber获取
                    kill_number = int(data.get('prevRoomNumber', 0))

                # 提取当期收益数据
                my_win_stroke = 0.0
                if 'myWinStroke' in data:
                    try:
                        my_win_stroke = float(data['myWinStroke'])
                    except (ValueError, TypeError):
                        my_win_stroke = 0.0

                # 提取房间统计数据
                room_stats = {}
                if 'room' in data and isinstance(data['room'], list):
                    for room_data in data['room']:
                        room_number = room_data.get('roomNumber', 0)
                        if room_number > 0:
                            # 提取投入金额 (totalMedal)
                            total_medal = 0.0
                            if 'totalMedal' in room_data:
                                try:
                                    total_medal = float(room_data['totalMedal'])
                                except (ValueError, TypeError):
                                    total_medal = 0.0

                            # 提取分享金额 (shareMedal)
                            share_medal = 0.0
                            if 'shareMedal' in room_data:
                                try:
                                    share_medal = float(room_data['shareMedal'])
                                except (ValueError, TypeError):
                                    share_medal = 0.0

                            # 提取购买笔数 (totalBuyStroke)
                            total_buy_stroke = 0
                            if 'totalBuyStroke' in room_data:
                                try:
                                    total_buy_stroke = int(room_data['totalBuyStroke'])
                                except (ValueError, TypeError):
                                    total_buy_stroke = 0

                            # 提取总笔数 (totalStroke)
                            total_stroke = 0.0
                            if 'totalStroke' in room_data:
                                try:
                                    total_stroke = float(room_data['totalStroke'])
                                except (ValueError, TypeError):
                                    total_stroke = 0.0

                            # 提取用户数量 (userCount)
                            user_count = room_data.get('userCount', 0)

                            room_stats[room_number] = {
                                'total_medal': total_medal,
                                'share_medal': share_medal,
                                'total_buy_stroke': total_buy_stroke,
                                'total_stroke': total_stroke,
                                'user_count': user_count
                            }

                return GameState(
                    issue=data.get('issue', 0),
                    state=data.get('state', 0),
                    kill_number=kill_number,
                    my_room_number=data.get('myRoomNumber', 0),
                    my_is_win=data.get('myIsWin', 0),
                    my_win_stroke=my_win_stroke,
                    room_stats=room_stats,
                    timestamp=datetime.now().isoformat(),
                    countdown=data.get('countdown', 0)
                )
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"错误内容: {response.text[:200]}...")
                return None

        except requests.exceptions.RequestException as e:
            print(f"🌐 网络请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"📄 JSON解析失败: {e}")
            print(f"原始响应: {response.text[:200]}...")
            return None
        except Exception as e:
            print(f"❌ 获取游戏状态失败: {e}")
            return None
    
    def place_bet(self, room_number: int, cost_medal: float) -> BetResult:
        """投注"""
        try:
            # 更新时间戳和签名
            self.headers['ts'] = str(int(time.time() * 1000))
            self.session.headers.update(self.headers)

            #url = f"{self.base_url}/v11/api/stroke/buy"
            url = f"{self.base_url}/v11/api/stroke/buy/stroke"
            params = {
                'uid': self.headers.get('userId', ''),
                'version': self.headers.get('version', '5.2.2')
            }
            #roomNumber=5&strokeId=1&costMedal=1

            data = {
                'roomNumber': room_number,
                'costMedal': cost_medal,
                #随机1-5
                'strokeId': random.randint(1, 5)
            }

            print(f"💰 执行投注: 房间{room_number}, 金额{cost_medal}")
            print(f"请求URL: {url}")
            print(f"请求参数: {params}")
            print(f"请求数据: {data}")
            response = self.session.post(url, params=params, data=data, timeout=10)

            print(f"📡 投注响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"📊 投注响应: {str(result)[:200]}...")

                # 根据实际响应格式判断成功
                success = result.get('success', True)  # 假设成功的默认值

                return BetResult(
                    success=success,
                    issue=result.get('issue', 0),
                    room_number=room_number,
                    cost_medal=cost_medal,
                    message=result.get('message', '投注已提交'),
                    timestamp=datetime.now().isoformat()
                )
            else:
                print(f"❌ 投注HTTP错误: {response.status_code}")
                print(f"错误内容: {response.text[:200]}...")
                return BetResult(
                    success=False,
                    issue=0,
                    room_number=room_number,
                    cost_medal=cost_medal,
                    message=f"HTTP错误: {response.status_code}",
                    timestamp=datetime.now().isoformat()
                )

        except Exception as e:
            print(f"❌ 投注异常: {e}")
            return BetResult(
                success=False,
                issue=0,
                room_number=room_number,
                cost_medal=cost_medal,
                message=f"投注异常: {e}",
                timestamp=datetime.now().isoformat()
            )

class GameMonitor:
    """游戏监控器"""
    
    def __init__(self, api_client: GameAPIClient):
        """初始化监控器"""
        self.api_client = api_client
        self.history = []  # 历史开奖记录
        self.current_issue = 0
        self.is_monitoring = False
        
    def start_monitoring(self, callback=None):
        """开始监控"""
        print("🔄 开始监控游戏状态...")
        self.is_monitoring = True

        # 初始化监控状态
        if not hasattr(self, 'last_monitored_issue'):
            self.last_monitored_issue = 0
        if not hasattr(self, 'processed_issues'):
            self.processed_issues = set()

        while self.is_monitoring:
            try:
                state = self.api_client.get_game_state()

                if state:
                    current_time = datetime.now().strftime("%H:%M:%S")

                    # 检查是否有新的开奖
                    if state.issue != self.last_monitored_issue and state.issue > 0:
                        print(f"🔍 检测到期号变化: {self.last_monitored_issue} -> {state.issue}, 状态: {state.state}")

                        # 更新监控的期号
                        self.last_monitored_issue = state.issue
                        self.current_issue = state.issue

                        # 如果是已开奖状态，处理开奖结果
                        if state.state == 2 and state.kill_number > 0:
                            if state.issue not in self.processed_issues:
                                print(f"🎯 [{current_time}] 新开奖: 期号{state.issue}, 开出房间{state.kill_number}")

                                # 标记为已处理
                                self.processed_issues.add(state.issue)

                                # 记录历史
                                self.history.append({
                                    'issue': state.issue,
                                    'kill_number': state.kill_number,
                                    'timestamp': state.timestamp
                                })

                                # 调用回调函数处理开奖结果
                                if callback:
                                    print(f"📞 调用回调函数处理开奖结果")
                                    try:
                                        callback(state)
                                    except Exception as e:
                                        print(f"❌ 回调函数执行失败: {e}")
                                else:
                                    print(f"⚠️  回调函数为空")

                    # 特殊处理：如果是已开奖状态且还未处理过
                    elif state.state == 2 and state.kill_number > 0 and state.issue not in self.processed_issues:
                        print(f"🎯 [{current_time}] 补充处理开奖: 期号{state.issue}, 开出房间{state.kill_number}")

                        # 标记为已处理
                        self.processed_issues.add(state.issue)

                        # 记录历史
                        self.history.append({
                            'issue': state.issue,
                            'kill_number': state.kill_number,
                            'timestamp': state.timestamp
                        })

                        # 调用回调函数
                        if callback:
                            print(f"📞 调用回调函数处理补充开奖结果")
                            try:
                                callback(state)
                            except Exception as e:
                                print(f"❌ 回调函数执行失败: {e}")
                        else:
                            print(f"⚠️  回调函数为空")

                    # 显示当前状态
                    status = "🟢 已开奖" if state.state == 2 else "🟡 等待开奖"
                    print(f"📊 [{current_time}] 期号: {state.issue}, state：{state.state}, 状态: {status}, 开出房间: {state.kill_number},倒计时：{state.countdown}")

                    # 如果是等待开奖状态，可以进行投注
                    if state.state == 1 and callback:
                        print("⏰ 等待开奖中，可以进行投注...")

                        # 在投注时机调用回调函数
                        if 15 <= state.countdown <= 25:
                            print(f"🎯 投注时机到达！倒计时{state.countdown}秒")
                            try:
                                callback(state)
                            except Exception as e:
                                print(f"❌ 投注回调函数执行失败: {e}")

                else:
                    print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] 无法获取游戏状态")

                # 每5秒检查一次
                time.sleep(5)

            except KeyboardInterrupt:
                print("🛑 监控已停止")
                self.is_monitoring = False
                break
            except Exception as e:
                print(f"❌ 监控异常: {e}")
                time.sleep(10)  # 异常时等待更长时间
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
    def get_recent_history(self, count: int = 10) -> List[int]:
        """获取最近的开奖历史"""
        if len(self.history) >= count:
            return [record['kill_number'] for record in self.history[-count:]]
        else:
            return [record['kill_number'] for record in self.history]

class AutoBettingSystem:
    """自动投注系统"""
    
    def __init__(self, api_client: GameAPIClient, prediction_rules: List[Dict]):
        """初始化自动投注系统"""
        self.api_client = api_client
        self.prediction_rules = prediction_rules
        self.monitor = GameMonitor(api_client)
        self.betting_enabled = False
        self.bet_amount = 0.1  # 默认投注金额
        self.max_consecutive_losses = 3  # 最大连续失败次数
        self.consecutive_losses = 0
        
    def predict_next_number(self, history: List[int]) -> Optional[Dict]:
        """预测下一个开出的房间号"""
        if len(history) < 3:
            return None
            
        # 尝试匹配预测规则
        for rule in self.prediction_rules:
            condition = rule['condition']
            condition_length = len(condition)
            
            if len(history) >= condition_length:
                recent_sequence = tuple(history[-condition_length:])
                if recent_sequence == condition:
                    return {
                        'predicted_number': rule['predicted_value'],
                        'confidence': rule['confidence'],
                        'rule': rule
                    }
        
        return None
    
    def calculate_bet_rooms(self, predicted_number: int) -> List[int]:
        """计算应该投注的房间（避开预测的房间）"""
        all_rooms = list(range(1, 9))  # 1-8房间
        bet_rooms = [room for room in all_rooms if room != predicted_number]
        return bet_rooms
    
    def should_place_bet(self, prediction: Dict) -> bool:
        """判断是否应该投注"""
        if not self.betting_enabled:
            return False
            
        # 检查连续失败次数
        if self.consecutive_losses >= self.max_consecutive_losses:
            print(f"连续失败{self.consecutive_losses}次，暂停投注")
            return False
            
        # 检查预测置信度
        if prediction['confidence'] < 0.6:
            print(f"预测置信度{prediction['confidence']:.3f}过低，跳过投注")
            return False
            
        return True
    
    def on_new_result(self, state: GameState):
        """处理新的开奖结果"""
        print(f"\n=== 新开奖结果 ===")
        print(f"期号: {state.issue}")
        print(f"开出房间: {state.kill_number}")
        
        # 获取历史数据
        history = self.monitor.get_recent_history(10)
        print(f"最近10期: {history}")
        
        # 进行预测
        prediction = self.predict_next_number(history)
        
        if prediction:
            print(f"\n预测下期开出: {prediction['predicted_number']} (置信度: {prediction['confidence']:.3f})")
            
            # 判断是否投注
            if self.should_place_bet(prediction):
                bet_rooms = self.calculate_bet_rooms(prediction['predicted_number'])
                print(f"建议投注房间: {bet_rooms}")
                
                # 这里可以选择投注一个或多个房间
                # 为简化，选择房间1（如果预测不是1的话）
                target_room = 1 if prediction['predicted_number'] != 1 else 2
                
                print(f"执行投注: 房间{target_room}, 金额{self.bet_amount}")
                
                # 执行投注
                bet_result = self.api_client.place_bet(target_room, self.bet_amount)
                
                if bet_result.success:
                    print(f"投注成功: {bet_result.message}")
                else:
                    print(f"投注失败: {bet_result.message}")
            else:
                print("不满足投注条件，跳过本期")
        else:
            print("未找到匹配的预测规则")
    
    def start_auto_betting(self):
        """开始自动投注"""
        print("启动自动投注系统...")
        self.betting_enabled = True
        self.monitor.start_monitoring(callback=self.on_new_result)
    
    def stop_auto_betting(self):
        """停止自动投注"""
        print("停止自动投注系统...")
        self.betting_enabled = False
        self.monitor.stop_monitoring()

def create_api_client_example():
    """创建API客户端示例"""
    
    # 示例配置（需要替换为实际值）
    base_url = "https://your-game-api.com"
    headers = {
        'User-Agent': 'your-user-agent',
        'packageId': 'your-package-id',
        'version': 'your-version',
        'channel': 'your-channel',
        'androidId': 'your-android-id',
        'userId': 'your-user-id',
        'token': 'your-token',
        'ts': str(int(time.time())),
        'sign': 'your-signature'
    }
    
    return GameAPIClient(base_url, headers)

def main():
    """主函数 - 演示用法"""
    print("=== API框架演示 ===")
    
    # 注意：这只是框架演示，需要实际的API配置
    print("1. 创建API客户端")
    print("2. 加载预测规则")
    print("3. 启动自动投注系统")
    print("4. 监控游戏状态并自动投注")
    
    print("\n⚠️  使用前需要配置:")
    print("- 实际的API地址和认证信息")
    print("- 加载我们训练的60个预测规则")
    print("- 设置合适的投注金额和风险控制参数")

if __name__ == "__main__":
    main()
