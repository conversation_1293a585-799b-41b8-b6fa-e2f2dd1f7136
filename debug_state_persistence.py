#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试状态持久化功能
"""

import os
import json
from datetime import datetime
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem

def debug_state_persistence():
    """调试状态持久化功能"""
    
    print("🔍 调试状态持久化功能")
    print("=" * 50)
    
    # 检查当前目录中的状态文件
    today = datetime.now().strftime('%Y%m%d')
    state_file = f"system_state_{today}.json"
    
    print(f"📂 检查状态文件: {state_file}")
    
    if os.path.exists(state_file):
        print("✅ 状态文件存在")
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            print("📊 当前状态文件内容:")
            for key, value in state_data.items():
                print(f"   {key}: {value}")
        except Exception as e:
            print(f"❌ 读取状态文件失败: {e}")
    else:
        print("❌ 状态文件不存在")
    
    print("\n" + "=" * 50)
    
    # 创建模拟API客户端
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return None
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 创建系统实例
    api_client = MockAPIClient()
    config = {
        'base_bet_amount': 2,
        'max_bet_amount': 20,
        'min_bet_amount': 1,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50,
        'initial_balance': 200,
        'auto_report_interval': 10,
        'risk_monitoring': True,
        'real_time_logging': True
    }
    
    print("🎯 创建系统实例...")
    system = OptimizedRandomBettingSystem(api_client, config)
    
    print(f"\n📊 系统初始状态:")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   当前余额: {system.current_balance}元")
    print(f"   总盈亏: {system.total_profit}元")
    print(f"   总投注: {system.total_bets}次")
    print(f"   总获胜: {system.total_wins}次")
    
    print("\n" + "=" * 50)
    
    # 模拟一些投注结果
    print("🎲 模拟投注结果...")
    
    # 模拟第一次投注 (失败)
    print("\n📍 模拟第130541期投注失败...")
    system.bet_history.append({
        'issue': 130541,
        'room': 1,
        'amount': 3.0,
        'timestamp': datetime.now().isoformat()
    })
    system.total_bets += 1
    system.process_result(130541, 1)  # 投注房间1，开奖房间1 (失败)
    
    print(f"\n📊 第一次投注后状态:")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   当前余额: {system.current_balance}元")
    print(f"   总盈亏: {system.total_profit}元")
    
    # 模拟第二次投注 (获胜)
    print("\n📍 模拟第130542期投注获胜...")
    system.bet_history.append({
        'issue': 130542,
        'room': 8,
        'amount': 3.0,
        'timestamp': datetime.now().isoformat()
    })
    system.total_bets += 1
    system.process_result(130542, 6)  # 投注房间8，开奖房间6 (获胜)
    
    print(f"\n📊 第二次投注后状态:")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   当前余额: {system.current_balance}元")
    print(f"   总盈亏: {system.total_profit}元")
    
    # 模拟第三次投注 (获胜)
    print("\n📍 模拟第130543期投注获胜...")
    system.bet_history.append({
        'issue': 130543,
        'room': 3,
        'amount': 3.0,
        'timestamp': datetime.now().isoformat()
    })
    system.total_bets += 1
    system.process_result(130543, 4)  # 投注房间3，开奖房间4 (获胜)
    
    print(f"\n📊 第三次投注后状态:")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   当前余额: {system.current_balance}元")
    print(f"   总盈亏: {system.total_profit}元")
    
    # 模拟第四次投注 (获胜)
    print("\n📍 模拟第130544期投注获胜...")
    system.bet_history.append({
        'issue': 130544,
        'room': 2,
        'amount': 3.0,
        'timestamp': datetime.now().isoformat()
    })
    system.total_bets += 1
    system.process_result(130544, 6)  # 投注房间2，开奖房间6 (获胜)
    
    print(f"\n📊 第四次投注后状态:")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   当前余额: {system.current_balance}元")
    print(f"   总盈亏: {system.total_profit}元")
    
    print("\n" + "=" * 50)
    
    # 检查状态文件是否已更新
    print("📂 检查状态文件是否已更新...")
    
    if os.path.exists(state_file):
        print("✅ 状态文件存在")
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                updated_state_data = json.load(f)
            
            print("📊 更新后的状态文件内容:")
            for key, value in updated_state_data.items():
                print(f"   {key}: {value}")
        except Exception as e:
            print(f"❌ 读取更新后的状态文件失败: {e}")
    else:
        print("❌ 状态文件仍然不存在")
    
    print("\n" + "=" * 50)
    
    # 测试重新加载
    print("🔄 测试重新创建系统实例 (应该加载之前的状态)...")
    
    system2 = OptimizedRandomBettingSystem(api_client, config)
    
    print(f"\n📊 新系统实例的状态:")
    print(f"   连续失败: {system2.consecutive_losses}次")
    print(f"   连续获胜: {system2.consecutive_wins}次")
    print(f"   当前余额: {system2.current_balance}元")
    print(f"   总盈亏: {system2.total_profit}元")
    print(f"   总投注: {system2.total_bets}次")
    print(f"   总获胜: {system2.total_wins}次")
    
    print("\n" + "=" * 50)
    
    # 验证结果
    print("✅ 验证结果:")
    
    expected_consecutive_wins = 3  # 连续3次获胜
    expected_consecutive_losses = 0  # 连败被重置
    expected_total_bets = 4
    expected_total_wins = 3
    
    if system2.consecutive_wins == expected_consecutive_wins:
        print(f"   ✅ 连续获胜次数正确: {system2.consecutive_wins}次")
    else:
        print(f"   ❌ 连续获胜次数错误: 期望{expected_consecutive_wins}次，实际{system2.consecutive_wins}次")
    
    if system2.consecutive_losses == expected_consecutive_losses:
        print(f"   ✅ 连续失败次数正确: {system2.consecutive_losses}次")
    else:
        print(f"   ❌ 连续失败次数错误: 期望{expected_consecutive_losses}次，实际{system2.consecutive_losses}次")
    
    if system2.total_bets == expected_total_bets:
        print(f"   ✅ 总投注次数正确: {system2.total_bets}次")
    else:
        print(f"   ❌ 总投注次数错误: 期望{expected_total_bets}次，实际{system2.total_bets}次")
    
    if system2.total_wins == expected_total_wins:
        print(f"   ✅ 总获胜次数正确: {system2.total_wins}次")
    else:
        print(f"   ❌ 总获胜次数错误: 期望{expected_total_wins}次，实际{system2.total_wins}次")
    
    print("\n🎯 结论:")
    if (system2.consecutive_wins == expected_consecutive_wins and 
        system2.consecutive_losses == expected_consecutive_losses and
        system2.total_bets == expected_total_bets and
        system2.total_wins == expected_total_wins):
        print("   ✅ 状态持久化功能正常工作!")
        print("   ✅ 连胜连败状态正确跟踪!")
        print("   ✅ 系统重启后能正确恢复状态!")
    else:
        print("   ❌ 状态持久化功能存在问题!")
        print("   ❌ 需要进一步调试!")

if __name__ == "__main__":
    debug_state_persistence()
