#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试状态持久化功能
"""

import os
import json
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem

def test_state_persistence():
    """测试状态持久化功能"""
    
    print("🧪 测试状态持久化功能")
    print("=" * 50)
    
    # 清理旧的状态文件
    state_files = [f for f in os.listdir('.') if f.startswith('system_state_') and f.endswith('.json')]
    for file in state_files:
        os.remove(file)
        print(f"🗑️ 清理旧状态文件: {file}")
    
    # 创建模拟API客户端
    api_client = GameAPIClient("http://mock", {})
    
    # 配置
    config = {
        'base_bet_amount': 1.5,
        'max_bet_amount': 15.0,
        'min_bet_amount': 1.0,
        'max_consecutive_losses': 5,
        'max_daily_loss': 30.0,
        'stop_loss_percentage': 0.3,
        'initial_balance': 150.0,
    }
    
    print(f"\n📊 第一次创建系统 (初始状态)")
    print("-" * 30)
    
    # 第一次创建系统
    system1 = OptimizedRandomBettingSystem(api_client, config)
    
    print(f"初始状态:")
    print(f"   连败: {system1.consecutive_losses}次")
    print(f"   连胜: {system1.consecutive_wins}次")
    print(f"   余额: {system1.current_balance:.2f}元")
    print(f"   总盈亏: {system1.total_profit:+.2f}元")
    
    # 模拟一些投注结果
    print(f"\n🎲 模拟投注结果")
    print("-" * 30)
    
    # 模拟连败2次
    system1.consecutive_losses = 2
    system1.consecutive_wins = 0
    system1.current_balance = 145.0
    system1.total_profit = -5.0
    system1.total_bets = 2
    system1.total_wins = 0
    
    print(f"模拟后状态:")
    print(f"   连败: {system1.consecutive_losses}次")
    print(f"   连胜: {system1.consecutive_wins}次")
    print(f"   余额: {system1.current_balance:.2f}元")
    print(f"   总盈亏: {system1.total_profit:+.2f}元")
    
    # 手动保存状态
    system1.save_persistent_state()
    
    # 删除第一个系统
    del system1
    
    print(f"\n📊 第二次创建系统 (应该加载持久化状态)")
    print("-" * 30)
    
    # 第二次创建系统，应该加载持久化状态
    system2 = OptimizedRandomBettingSystem(api_client, config)
    
    print(f"加载后状态:")
    print(f"   连败: {system2.consecutive_losses}次")
    print(f"   连胜: {system2.consecutive_wins}次")
    print(f"   余额: {system2.current_balance:.2f}元")
    print(f"   总盈亏: {system2.total_profit:+.2f}元")
    
    # 验证状态是否正确加载
    if (system2.consecutive_losses == 2 and 
        system2.consecutive_wins == 0 and 
        abs(system2.current_balance - 145.0) < 0.01 and
        abs(system2.total_profit - (-5.0)) < 0.01):
        print(f"✅ 状态持久化测试通过!")
    else:
        print(f"❌ 状态持久化测试失败!")
    
    # 测试动态金额计算
    print(f"\n💰 测试动态金额计算 (基于持久化状态)")
    print("-" * 30)
    
    amount = system2.calculate_enhanced_dynamic_amount()
    print(f"计算结果: {amount:.2f}元")
    
    # 模拟一次获胜，更新状态
    print(f"\n🎉 模拟一次获胜")
    print("-" * 30)
    
    # 模拟process_result调用
    system2.consecutive_losses = 0
    system2.consecutive_wins = 1
    system2.current_balance = 147.0
    system2.total_profit = -3.0
    system2.total_bets = 3
    system2.total_wins = 1
    
    # 保存更新后的状态
    system2.save_persistent_state()
    
    print(f"更新后状态:")
    print(f"   连败: {system2.consecutive_losses}次")
    print(f"   连胜: {system2.consecutive_wins}次")
    print(f"   余额: {system2.current_balance:.2f}元")
    print(f"   总盈亏: {system2.total_profit:+.2f}元")
    
    # 再次测试动态金额计算
    amount2 = system2.calculate_enhanced_dynamic_amount()
    print(f"新的计算结果: {amount2:.2f}元")
    
    if amount2 != amount:
        print(f"✅ 动态金额根据状态变化正确调整!")
        print(f"   连败时: {amount:.2f}元")
        print(f"   连胜时: {amount2:.2f}元")
    else:
        print(f"⚠️ 动态金额没有根据状态变化")
    
    # 检查状态文件内容
    print(f"\n📄 检查状态文件内容")
    print("-" * 30)
    
    if os.path.exists(system2.state_file):
        with open(system2.state_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        print(f"状态文件: {system2.state_file}")
        print(f"文件内容:")
        for key, value in state_data.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.2f}")
            else:
                print(f"   {key}: {value}")
    
    print(f"\n🎉 状态持久化测试完成!")

def test_real_scenario():
    """测试真实场景下的状态持久化"""
    
    print(f"\n🎯 测试真实场景下的状态持久化")
    print("=" * 50)
    
    # 清理旧的状态文件
    state_files = [f for f in os.listdir('.') if f.startswith('system_state_') and f.endswith('.json')]
    for file in state_files:
        os.remove(file)
    
    # 创建模拟API客户端
    api_client = GameAPIClient("http://mock", {})
    
    # 配置
    config = {
        'base_bet_amount': 1.5,
        'max_bet_amount': 15.0,
        'min_bet_amount': 1.0,
        'max_consecutive_losses': 5,
        'max_daily_loss': 30.0,
        'stop_loss_percentage': 0.3,
        'initial_balance': 150.0,
    }
    
    # 创建系统
    system = OptimizedRandomBettingSystem(api_client, config)
    
    # 模拟多次投注和结果处理
    print(f"🎲 模拟连续投注场景:")
    
    scenarios = [
        {'issue': 130001, 'bet_room': 1, 'winning_room': 2, 'expected_result': 'win'},
        {'issue': 130002, 'bet_room': 3, 'winning_room': 4, 'expected_result': 'win'},
        {'issue': 130003, 'bet_room': 5, 'winning_room': 6, 'expected_result': 'win'},
        {'issue': 130004, 'bet_room': 7, 'winning_room': 7, 'expected_result': 'loss'},
        {'issue': 130005, 'bet_room': 1, 'winning_room': 1, 'expected_result': 'loss'},
    ]
    
    for i, scenario in enumerate(scenarios):
        print(f"\n--- 第{i+1}次投注 ---")
        
        # 计算投注金额
        amount = system.calculate_enhanced_dynamic_amount()
        print(f"投注金额: {amount:.2f}元")
        
        # 模拟投注记录
        bet_info = {
            'issue': scenario['issue'],
            'room': scenario['bet_room'],
            'amount': amount,
            'timestamp': '2025-08-02T20:30:00'
        }
        system.bet_history.append(bet_info)
        
        # 处理结果
        system.process_result(scenario['issue'], scenario['winning_room'])
        
        print(f"当前状态: 连败{system.consecutive_losses}次, 连胜{system.consecutive_wins}次")
    
    print(f"\n📊 最终状态:")
    print(f"   连败: {system.consecutive_losses}次")
    print(f"   连胜: {system.consecutive_wins}次")
    print(f"   余额: {system.current_balance:.2f}元")
    print(f"   总盈亏: {system.total_profit:+.2f}元")
    
    # 验证状态文件是否正确保存
    if os.path.exists(system.state_file):
        print(f"✅ 状态文件已保存: {system.state_file}")
    else:
        print(f"❌ 状态文件未保存")

if __name__ == "__main__":
    test_state_persistence()
    test_real_scenario()
    
    print(f"\n🎉 所有测试完成!")
    print(f"💡 现在系统会自动保存和加载连胜连败状态")
    print(f"   每次投注结果处理后都会保存状态")
    print(f"   重新启动系统时会自动加载之前的状态")
    print(f"   这样就能确保增强动态金额计算的连续性")
