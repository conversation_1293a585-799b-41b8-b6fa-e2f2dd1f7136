#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新盈利系统实时记录功能
"""

import json
import time
from datetime import datetime
from new_profitable_system import NewProfitableSystem

def create_mock_api_client():
    """创建模拟API客户端"""
    
    class MockAPIClient:
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    return MockAPIClient()

def create_mock_game_state(issue: int, state: int, countdown: int = 20, kill_number: int = 0):
    """创建模拟游戏状态"""
    
    class MockGameState:
        def __init__(self, issue, state, countdown, kill_number):
            self.issue = issue
            self.state = state
            self.countdown = countdown
            self.kill_number = kill_number
    
    return MockGameState(issue, state, countdown, kill_number)

def test_real_time_logging():
    """测试实时记录功能"""
    
    print("🧪 测试新盈利系统实时记录功能")
    print("=" * 60)
    
    # 创建测试历史数据
    test_history = [
        1, 2, 3, 4, 5, 6, 7, 8,  # 基础数据
        2, 3, 4, 5, 6, 7, 8, 1,
        3, 4, 5, 6, 7, 8, 1, 2,
        4, 5, 6, 7, 8, 1, 2, 3,
        5, 6, 7, 8, 1, 2, 3, 4,
        # 添加重复模式
        1, 2, 3, 4, 1, 2, 3, 4,
        5, 6, 7, 8, 5, 6, 7, 8,
    ]
    
    # 保存测试历史数据
    test_data = {
        'history': test_history,
        'last_updated': datetime.now().isoformat(),
        'total_periods': len(test_history)
    }
    
    with open("game_history.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 创建测试历史数据: {len(test_history)}期")
    
    # 创建系统
    api_client = create_mock_api_client()
    config = {
        'base_bet_amount': 1.0,
        'max_bet_amount': 10.0,
        'profit_target': 50.0,
        'stop_loss': -20.0
    }
    
    try:
        system = NewProfitableSystem(api_client, config)
        
        print(f"\n📊 系统初始化结果:")
        print(f"   历史数据: {len(system.history)}期")
        print(f"   动态规则: {len(system.dynamic_rules)}个")
        print(f"   实时记录文件: {getattr(system, 'log_filename', 'None')}")
        
        # 模拟几轮投注和开奖
        test_scenarios = [
            {
                'issue': 129500,
                'betting_state': create_mock_game_state(129500, 1, 20),  # 投注状态
                'result_state': create_mock_game_state(129500, 2, 0, 3),  # 开奖状态
                'description': '第一轮投注'
            },
            {
                'issue': 129501,
                'betting_state': create_mock_game_state(129501, 1, 25),
                'result_state': create_mock_game_state(129501, 2, 0, 1),
                'description': '第二轮投注'
            },
            {
                'issue': 129502,
                'betting_state': create_mock_game_state(129502, 1, 18),
                'result_state': create_mock_game_state(129502, 2, 0, 5),
                'description': '第三轮投注'
            }
        ]
        
        print(f"\n🎯 开始模拟投注测试:")
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n--- {scenario['description']} ---")
            
            # 处理投注时机
            print(f"🎯 处理投注时机: 期号{scenario['issue']}")
            system.process_game_state(scenario['betting_state'])
            
            # 等待一下模拟时间流逝
            time.sleep(0.5)
            
            # 处理开奖结果
            print(f"🎯 处理开奖结果: 开出房间{scenario['result_state'].kill_number}")
            system.process_game_state(scenario['result_state'])
            
            print(f"✅ {scenario['description']}完成")
        
        # 检查记录文件
        if hasattr(system, 'log_filename') and system.log_filename:
            print(f"\n📝 检查实时记录文件:")
            try:
                with open(system.log_filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"✅ 记录文件创建成功")
                print(f"📊 文件大小: {len(content)}字符")
                
                # 显示部分内容
                lines = content.split('\n')
                print(f"📋 记录内容预览:")
                for line in lines[:15]:  # 显示前15行
                    if line.strip():
                        print(f"   {line}")
                
                if len(lines) > 15:
                    print(f"   ... (共{len(lines)}行)")
                
                return True
                
            except Exception as e:
                print(f"❌ 读取记录文件失败: {e}")
                return False
        else:
            print(f"❌ 实时记录文件未创建")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_time_logging()
    
    if success:
        print(f"\n🎊 实时记录功能测试成功！")
        print(f"💡 新盈利系统现在具备:")
        print(f"   1. ✅ 完整的实时记录功能")
        print(f"   2. ✅ 投注和结果的详细记录")
        print(f"   3. ✅ 实时统计信息更新")
        print(f"   4. ✅ Markdown格式的记录文件")
        print(f"   5. ✅ 会话统计和胜率计算")
    else:
        print(f"\n⚠️ 实时记录功能需要进一步调试")
