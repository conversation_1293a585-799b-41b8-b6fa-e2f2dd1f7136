#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时投注系统
整合预测算法和API调用的完整自动化投注系统
"""

import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

# 导入我们之前创建的模块
from api_framework import GameAPIClient, GameMonitor, GameState, BetResult
from prediction_strategy_adapter import PredictionRuleAdapter

@dataclass
class BettingSession:
    """投注会话记录"""
    session_id: str
    start_time: str
    end_time: Optional[str]
    total_bets: int
    total_wins: int
    total_amount_bet: float
    total_amount_won: float
    net_profit: float
    win_rate: float
    roi: float

@dataclass
class BettingRecord:
    """单次投注记录"""
    timestamp: str
    issue: int
    predicted_room: int
    actual_room: int
    bet_rooms: List[int]
    bet_amounts: List[float]
    prediction_confidence: float
    won: bool
    profit: float

class RealTimeBettingSystem:
    """实时投注系统"""
    
    def __init__(self, api_client: GameAPIClient, config: Dict):
        """初始化实时投注系统"""
        self.api_client = api_client
        self.config = config
        self.prediction_adapter = PredictionRuleAdapter()
        self.monitor = GameMonitor(api_client)
        
        # 系统状态
        self.is_running = False
        self.betting_enabled = False
        self.history = []  # 开奖历史
        self.betting_records = []  # 投注记录
        
        # 风险控制参数
        self.max_bet_amount = config.get('max_bet_amount', 1.0)
        self.min_confidence = config.get('min_confidence', 0.8)
        self.max_consecutive_losses = config.get('max_consecutive_losses', 3)
        self.daily_loss_limit = config.get('daily_loss_limit', 10.0)
        
        # 当前状态
        self.consecutive_losses = 0
        self.daily_loss = 0.0
        self.current_session = None
        
        # 设置日志
        self.setup_logging()
        
        # 初始化预测规则
        self.init_prediction_rules()
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'betting_log_{datetime.now().strftime("%Y%m%d")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def init_prediction_rules(self):
        """初始化预测规则"""
        rules = self.prediction_adapter.load_prediction_rules_from_analysis()
        self.prediction_adapter.categorize_rules(rules)
        self.logger.info(f"加载了{len(rules)}个预测规则")
    
    def start_session(self):
        """开始新的投注会话"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.current_session = BettingSession(
            session_id=session_id,
            start_time=datetime.now().isoformat(),
            end_time=None,
            total_bets=0,
            total_wins=0,
            total_amount_bet=0.0,
            total_amount_won=0.0,
            net_profit=0.0,
            win_rate=0.0,
            roi=0.0
        )
        self.logger.info(f"开始新的投注会话: {session_id}")
    
    def end_session(self):
        """结束当前投注会话"""
        if self.current_session:
            self.current_session.end_time = datetime.now().isoformat()
            
            # 计算会话统计
            if self.current_session.total_bets > 0:
                self.current_session.win_rate = self.current_session.total_wins / self.current_session.total_bets
                self.current_session.roi = (self.current_session.net_profit / self.current_session.total_amount_bet) * 100
            
            # 保存会话记录
            self.save_session_record()
            self.logger.info(f"结束投注会话: {self.current_session.session_id}")
            self.logger.info(f"会话统计 - 胜率: {self.current_session.win_rate:.3f}, ROI: {self.current_session.roi:.2f}%")
    
    def save_session_record(self):
        """保存会话记录"""
        if self.current_session:
            filename = f"session_{self.current_session.session_id}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'session': asdict(self.current_session),
                    'betting_records': [asdict(record) for record in self.betting_records]
                }, f, indent=2, ensure_ascii=False)
    
    def check_risk_limits(self) -> bool:
        """检查风险限制"""
        
        # 检查连续失败次数
        if self.consecutive_losses >= self.max_consecutive_losses:
            self.logger.warning(f"连续失败{self.consecutive_losses}次，触发风险控制")
            return False
        
        # 检查日损失限制
        if self.daily_loss >= self.daily_loss_limit:
            self.logger.warning(f"日损失{self.daily_loss:.2f}达到限制{self.daily_loss_limit}")
            return False
        
        return True
    
    def calculate_bet_amount(self, confidence: float, base_amount: float = 0.1) -> float:
        """根据置信度计算投注金额"""
        
        # 基于置信度的动态调整
        if confidence >= 0.95:
            multiplier = 2.0
        elif confidence >= 0.9:
            multiplier = 1.5
        elif confidence >= 0.8:
            multiplier = 1.0
        else:
            multiplier = 0.5
        
        # 基于连续失败的调整（马丁格尔策略的保守版本）
        if self.consecutive_losses > 0:
            multiplier *= (1.2 ** self.consecutive_losses)
        
        bet_amount = base_amount * multiplier
        
        # 确保不超过最大投注限制
        return min(bet_amount, self.max_bet_amount)
    
    def process_new_result(self, state: GameState):
        """处理新的开奖结果"""
        
        self.logger.info(f"新开奖: 期号{state.issue}, 开出房间{state.kill_number}")
        
        # 更新历史记录
        self.history.append(state.kill_number)
        
        # 如果历史记录太长，保留最近100个
        if len(self.history) > 100:
            self.history = self.history[-100:]
        
        # 检查上一次投注结果
        self.check_previous_bet_result(state)
        
        # 如果投注被禁用，只记录不投注
        if not self.betting_enabled:
            self.logger.info("投注已禁用，仅记录开奖结果")
            return
        
        # 检查风险限制
        if not self.check_risk_limits():
            self.logger.warning("触发风险控制，暂停投注")
            return
        
        # 进行预测
        if len(self.history) >= 5:  # 需要至少5个历史数据
            self.make_prediction_and_bet()
    
    def check_previous_bet_result(self, state: GameState):
        """检查上一次投注的结果"""
        
        if self.betting_records:
            last_record = self.betting_records[-1]
            
            # 检查是否是对应的开奖结果
            if last_record.issue == state.issue:
                actual_room = state.kill_number
                last_record.actual_room = actual_room
                
                # 判断是否获胜
                won = actual_room not in last_record.bet_rooms
                last_record.won = won
                
                # 计算盈亏
                total_bet = sum(last_record.bet_amounts)
                if won:
                    total_win = total_bet * 1.1  # 1:1.1赔率
                    profit = total_win - total_bet
                    self.consecutive_losses = 0
                    self.current_session.total_wins += 1
                    self.current_session.total_amount_won += total_win
                else:
                    profit = -total_bet
                    self.consecutive_losses += 1
                    self.daily_loss += total_bet
                
                last_record.profit = profit
                self.current_session.net_profit += profit
                
                self.logger.info(f"投注结果: {'获胜' if won else '失败'}, 盈亏: {profit:.2f}")
    
    def make_prediction_and_bet(self):
        """进行预测并投注"""
        
        # 使用最近10个数据进行预测
        recent_history = self.history[-10:]
        prediction = self.prediction_adapter.predict_next_room(recent_history, self.min_confidence)
        
        if prediction:
            predicted_room = prediction['predicted_room']
            confidence = prediction['confidence']
            
            self.logger.info(f"预测下期开出房间: {predicted_room} (置信度: {confidence:.3f})")
            
            # 生成投注建议
            recommendations = self.prediction_adapter.generate_betting_recommendations(
                recent_history, self.calculate_bet_amount(confidence)
            )
            
            if recommendations:
                self.execute_bets(recommendations, prediction)
            else:
                self.logger.info("未生成投注建议")
        else:
            self.logger.info("未找到匹配的预测规则，跳过本期")
    
    def execute_bets(self, recommendations: List[Dict], prediction: Dict):
        """执行投注"""
        
        bet_rooms = []
        bet_amounts = []
        total_bet_amount = 0
        
        for rec in recommendations:
            room_number = rec['room_number']
            bet_amount = rec['bet_amount']
            
            # 执行单个投注
            bet_result = self.api_client.place_bet(room_number, bet_amount)
            
            if bet_result.success:
                bet_rooms.append(room_number)
                bet_amounts.append(bet_amount)
                total_bet_amount += bet_amount
                
                self.logger.info(f"投注成功: 房间{room_number}, 金额{bet_amount:.2f}")
            else:
                self.logger.error(f"投注失败: 房间{room_number}, 原因: {bet_result.message}")
        
        # 记录投注
        if bet_rooms:
            betting_record = BettingRecord(
                timestamp=datetime.now().isoformat(),
                issue=0,  # 将在下次开奖时更新
                predicted_room=prediction['predicted_room'],
                actual_room=0,  # 将在开奖时更新
                bet_rooms=bet_rooms,
                bet_amounts=bet_amounts,
                prediction_confidence=prediction['confidence'],
                won=False,  # 将在开奖时更新
                profit=0.0  # 将在开奖时更新
            )
            
            self.betting_records.append(betting_record)
            
            # 更新会话统计
            self.current_session.total_bets += 1
            self.current_session.total_amount_bet += total_bet_amount
    
    def start_real_time_betting(self):
        """启动实时投注系统"""
        
        self.logger.info("启动实时投注系统...")
        
        # 开始新会话
        self.start_session()
        
        # 启用投注
        self.betting_enabled = True
        self.is_running = True
        
        try:
            # 开始监控
            self.monitor.start_monitoring(callback=self.process_new_result)
        except KeyboardInterrupt:
            self.logger.info("收到停止信号")
        finally:
            self.stop_real_time_betting()
    
    def stop_real_time_betting(self):
        """停止实时投注系统"""
        
        self.logger.info("停止实时投注系统...")
        
        self.is_running = False
        self.betting_enabled = False
        
        # 停止监控
        self.monitor.stop_monitoring()
        
        # 结束会话
        self.end_session()
        
        self.logger.info("实时投注系统已停止")

def create_betting_system_config():
    """创建投注系统配置"""
    
    return {
        'max_bet_amount': 1.0,          # 最大单次投注金额
        'min_confidence': 0.8,          # 最小预测置信度
        'max_consecutive_losses': 3,    # 最大连续失败次数
        'daily_loss_limit': 10.0,       # 日损失限制
        'base_bet_amount': 0.1,         # 基础投注金额
    }

def main():
    """主函数"""
    
    print("=== 实时投注系统 ===")
    print("⚠️  这是一个完整的自动化投注系统")
    print("使用前请确保:")
    print("1. 已配置正确的API认证信息")
    print("2. 已设置合适的风险控制参数")
    print("3. 已进行充分的测试验证")
    print("4. 准备好承担投注风险")
    
    # 示例配置
    config = create_betting_system_config()
    
    print(f"\n系统配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print(f"\n要启动系统，请:")
    print("1. 配置API客户端")
    print("2. 创建RealTimeBettingSystem实例")
    print("3. 调用start_real_time_betting()方法")

if __name__ == "__main__":
    main()
