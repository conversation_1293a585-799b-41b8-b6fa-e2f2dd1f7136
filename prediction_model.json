{"rules": [{"condition": [5, 8, 4, 1], "next_value": 1, "confidence": 1.0, "support": 5, "length": 4, "practical_score": 0.67, "all_outcomes": {"1": 5}}, {"condition": [2, 1, 2, 1, 5], "next_value": 6, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"6": 3}}, {"condition": [7, 7, 5, 4, 1], "next_value": 1, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"1": 3}}, {"condition": [3, 7, 2, 6, 8], "next_value": 8, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"8": 3}}, {"condition": [5, 1, 2, 4, 1], "next_value": 7, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"7": 3}}, {"condition": [2, 1, 7, 8, 5], "next_value": 4, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"4": 3}}, {"condition": [4, 1, 8, 6, 7], "next_value": 5, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"5": 3}}, {"condition": [1, 2, 1, 1, 8], "next_value": 4, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"4": 3}}, {"condition": [8, 7, 5, 5, 2], "next_value": 3, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"3": 3}}, {"condition": [2, 7, 1, 1, 6], "next_value": 4, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"4": 3}}, {"condition": [1, 8, 8, 1, 8], "next_value": 4, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"4": 3}}, {"condition": [2, 1, 4, 8, 4], "next_value": 4, "confidence": 1.0, "support": 3, "length": 5, "practical_score": 0.668, "all_outcomes": {"4": 3}}, {"condition": [4, 7, 7, 8, 3], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [8, 1, 6, 3, 7], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [4, 6, 6, 7, 3], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [5, 3, 7, 5, 7], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [6, 3, 5, 8, 1], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [8, 5, 6, 8, 1], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [8, 7, 5, 7, 6], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [5, 6, 5, 3, 8], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [5, 8, 7, 7, 1], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [3, 1, 5, 2, 7], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [7, 5, 6, 3, 7], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [2, 4, 2, 8, 2], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [7, 8, 3, 3, 3], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [5, 7, 2, 3, 4], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [5, 2, 5, 2, 4], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [4, 1, 7, 2, 8], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [5, 1, 1, 4, 3], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [3, 5, 6, 4, 8], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [8, 8, 8, 3, 5], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [4, 6, 4, 8, 1], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [7, 5, 5, 1, 8], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [2, 7, 7, 8, 5], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [8, 5, 6, 1, 4], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [6, 8, 7, 5, 3], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [6, 8, 4, 2, 7], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [3, 6, 3, 6, 4], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [2, 2, 8, 6, 6], "next_value": 4, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"4": 2}}, {"condition": [2, 8, 6, 6, 4], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [1, 8, 4, 3, 3], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [5, 6, 5, 1, 1], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [7, 1, 1, 2, 5], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [4, 1, 8, 8, 3], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [4, 5, 8, 1, 6], "next_value": 4, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"4": 2}}, {"condition": [6, 1, 5, 7, 2], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [8, 5, 5, 2, 2], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [2, 1, 4, 7, 5], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [5, 5, 2, 4, 8], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [7, 5, 8, 5, 4], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [2, 5, 8, 5, 4], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [8, 5, 4, 1, 1], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [1, 7, 2, 4, 4], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [7, 2, 4, 4, 2], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [2, 4, 4, 2, 5], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [3, 3, 4, 6, 7], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [8, 8, 3, 2, 5], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [1, 5, 2, 7, 8], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [2, 7, 8, 7, 3], "next_value": 4, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"4": 2}}, {"condition": [4, 6, 5, 3, 6], "next_value": 4, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"4": 2}}, {"condition": [4, 2, 1, 1, 7], "next_value": 4, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"4": 2}}, {"condition": [3, 5, 8, 2, 1], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [2, 1, 4, 5, 2], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [2, 6, 5, 8, 8], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [6, 5, 8, 8, 3], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [5, 5, 3, 3, 8], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [8, 2, 4, 6, 2], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [2, 4, 6, 2, 3], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [4, 5, 4, 7, 1], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [8, 6, 5, 4, 3], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [6, 3, 4, 2, 4], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [6, 6, 3, 3, 1], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [2, 6, 8, 4, 8], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [4, 8, 2, 3, 8], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [1, 5, 5, 6, 8], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [1, 1, 4, 6, 4], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [8, 8, 6, 8, 6], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [1, 1, 3, 5, 7], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [7, 4, 7, 5, 2], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [2, 6, 1, 8, 1], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [1, 6, 7, 3, 8], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [4, 3, 7, 5, 5], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [2, 8, 8, 2, 1], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [2, 1, 3, 6, 6], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [6, 4, 1, 1, 3], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [3, 3, 5, 3, 4], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [4, 2, 5, 3, 3], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [6, 8, 6, 2, 1], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [8, 7, 3, 2, 1], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}, {"condition": [7, 3, 2, 1, 2], "next_value": 3, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"3": 2}}, {"condition": [3, 3, 6, 1, 3], "next_value": 1, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"1": 2}}, {"condition": [5, 4, 6, 4, 1], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [1, 5, 5, 5, 8], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [5, 5, 5, 8, 8], "next_value": 6, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"6": 2}}, {"condition": [1, 3, 8, 8, 3], "next_value": 4, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"4": 2}}, {"condition": [6, 1, 5, 2, 1], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [5, 4, 6, 3, 4], "next_value": 5, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"5": 2}}, {"condition": [2, 2, 2, 1, 7], "next_value": 8, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"8": 2}}, {"condition": [2, 2, 1, 7, 8], "next_value": 7, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"7": 2}}, {"condition": [1, 7, 8, 7, 1], "next_value": 2, "confidence": 1.0, "support": 2, "length": 5, "practical_score": 0.662, "all_outcomes": {"2": 2}}], "strategies": {"global_frequency": {"method": "global_most_common", "value": 2, "confidence": 0.12928368704197907, "distribution": {"8": 2949, "1": 2964, "2": 3052, "4": 2947, "7": 2975, "3": 3011, "5": 2837, "6": 2872}}, "recent_trend": {"method": "recent_most_common", "value": 2, "confidence": 0.148, "window_size": 1000}, "position_pattern": {"0": {"most_common": 4, "confidence": 0.13554727211114875, "distribution": {"8": 373, "3": 384, "5": 343, "4": 400, "1": 367, "6": 348, "2": 377, "7": 359}}, "1": {"most_common": 1, "confidence": 0.13317519484920365, "distribution": {"1": 393, "8": 345, "7": 384, "4": 334, "2": 374, "6": 382, "3": 378, "5": 361}}, "2": {"most_common": 2, "confidence": 0.13724161301253812, "distribution": {"2": 405, "5": 355, "7": 389, "6": 328, "1": 388, "3": 360, "4": 370, "8": 356}}, "3": {"most_common": 2, "confidence": 0.13453066757031515, "distribution": {"4": 386, "8": 358, "3": 364, "7": 389, "6": 360, "2": 397, "5": 333, "1": 364}}, "4": {"most_common": 8, "confidence": 0.13690274483226025, "distribution": {"7": 387, "2": 377, "5": 349, "3": 380, "1": 348, "4": 353, "8": 404, "6": 353}}, "5": {"most_common": 2, "confidence": 0.13080311758725854, "distribution": {"7": 371, "8": 364, "6": 378, "2": 386, "1": 373, "3": 368, "5": 362, "4": 349}}, "6": {"most_common": 3, "confidence": 0.13046424940698068, "distribution": {"8": 379, "1": 351, "4": 381, "3": 385, "2": 385, "7": 343, "5": 374, "6": 353}}, "7": {"most_common": 3, "confidence": 0.13288135593220338, "distribution": {"3": 392, "6": 370, "2": 351, "8": 370, "5": 360, "7": 353, "1": 380, "4": 374}}}, "avoid_recent": {"method": "least_recent", "value": 5, "confidence": 0.2}}, "model_info": {"training_samples": 23607, "total_rules": 6278, "creation_time": 1753432338.623733}}