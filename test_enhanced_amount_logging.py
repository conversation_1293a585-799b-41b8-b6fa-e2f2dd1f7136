#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的投注金额日志记录功能
"""

import os
import json
from datetime import datetime
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem

def test_enhanced_amount_logging():
    """测试增强的投注金额日志记录功能"""
    
    print("🧪 测试增强的投注金额日志记录功能")
    print("=" * 60)
    
    # 创建模拟API客户端
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return None
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 创建系统实例
    api_client = MockAPIClient()
    config = {
        'base_bet_amount': 2,
        'max_bet_amount': 20,
        'min_bet_amount': 1,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50,
        'initial_balance': 200,
        'auto_report_interval': 10,
        'risk_monitoring': True,
        'real_time_logging': True
    }
    
    print("🎯 创建系统实例...")
    system = OptimizedRandomBettingSystem(api_client, config)
    
    # 设置一些状态来测试不同的动态金额计算
    print("\n📊 设置测试状态...")
    
    # 测试场景1: 连胜3次的情况
    system.consecutive_wins = 3
    system.consecutive_losses = 0
    system.total_bets = 15
    system.total_wins = 14  # 93.3%胜率，触发算法奖励
    
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   总投注: {system.total_bets}次")
    print(f"   总获胜: {system.total_wins}次")
    print(f"   胜率: {system.total_wins/system.total_bets*100:.1f}%")
    
    print("\n" + "=" * 60)
    
    # 测试动态金额计算
    print("💰 测试动态金额计算...")
    
    amount, calculation = system.calculate_enhanced_dynamic_amount()
    
    print(f"\n📊 计算结果:")
    print(f"   最终金额: {amount}元")
    print(f"   计算详情: {json.dumps(calculation, indent=2, ensure_ascii=False)}")
    
    print("\n" + "=" * 60)
    
    # 测试投注记录
    print("🎲 测试投注记录...")
    
    # 模拟一次投注
    current_issue = 130555
    bet_info = system.execute_optimized_bet(current_issue)
    
    if bet_info:
        print(f"✅ 投注记录成功")
        print(f"   期号: {bet_info['issue']}")
        print(f"   房间: {bet_info['room']}")
        print(f"   金额: {bet_info['amount']}元")
    else:
        print("❌ 投注记录失败")
    
    print("\n" + "=" * 60)
    
    # 模拟开奖结果
    print("🎰 模拟开奖结果...")
    
    winning_room = 1  # 假设开奖房间1，投注房间不是1则获胜
    system.process_result(current_issue, winning_room)
    
    print("\n" + "=" * 60)
    
    # 检查生成的日志文件
    print("📂 检查生成的日志文件...")
    
    # 查找最新的投注日志文件
    log_files = []
    for filename in os.listdir('.'):
        if filename.startswith('betting_log_') and filename.endswith('.md'):
            log_files.append(filename)
    
    if log_files:
        latest_log = sorted(log_files)[-1]
        print(f"✅ 找到日志文件: {latest_log}")
        
        # 读取并显示相关内容
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找投注金额相关的内容
            lines = content.split('\n')
            in_betting_section = False
            amount_lines = []
            
            for i, line in enumerate(lines):
                if f"期号{current_issue}" in line and "投注" in line:
                    in_betting_section = True
                    amount_lines.append(line)
                elif in_betting_section:
                    if line.strip().startswith('- **') or line.strip().startswith('  - '):
                        amount_lines.append(line)
                    elif line.strip() == '' or line.startswith('###'):
                        if line.startswith('###'):
                            break
                        continue
                    else:
                        break
            
            if amount_lines:
                print(f"\n📋 投注金额详情显示:")
                for line in amount_lines:
                    print(f"   {line}")
            else:
                print(f"⚠️ 未找到投注金额详情")
                
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("❌ 未找到日志文件")
    
    print("\n" + "=" * 60)
    
    # 测试不同状态下的动态金额计算
    print("🔄 测试不同状态下的动态金额计算...")
    
    test_scenarios = [
        {
            'name': '连败2次',
            'consecutive_wins': 0,
            'consecutive_losses': 2,
            'total_bets': 5,
            'total_wins': 2,
            'current_balance': 180
        },
        {
            'name': '连胜5次+高胜率',
            'consecutive_wins': 5,
            'consecutive_losses': 0,
            'total_bets': 20,
            'total_wins': 18,
            'current_balance': 220
        },
        {
            'name': '余额不足50%',
            'consecutive_wins': 1,
            'consecutive_losses': 0,
            'total_bets': 10,
            'total_wins': 4,
            'current_balance': 90  # 45%余额
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📊 测试场景: {scenario['name']}")
        
        # 设置状态
        system.consecutive_wins = scenario['consecutive_wins']
        system.consecutive_losses = scenario['consecutive_losses']
        system.total_bets = scenario['total_bets']
        system.total_wins = scenario['total_wins']
        system.current_balance = scenario['current_balance']
        
        # 计算动态金额
        amount, calculation = system.calculate_enhanced_dynamic_amount()
        
        print(f"   最终金额: {amount}元")
        print(f"   基础金额: {calculation['base_amount']}元")
        if calculation['martingale_adjustment'] > 0:
            print(f"   马丁格尔: +{calculation['martingale_adjustment']}元")
        if calculation['win_streak_bonus'] > 0:
            print(f"   连胜奖励: +{calculation['win_streak_bonus']}元")
        if calculation['algorithm_bonus'] > 0:
            print(f"   算法奖励: +{calculation['algorithm_bonus']}元")
        if calculation['risk_adjustment'] != 0:
            print(f"   风险调整: {calculation['risk_adjustment']:+}元")
        if calculation['balance_protection'] < 0:
            print(f"   余额保护: {calculation['balance_protection']}元")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成!")
    print("✅ 增强的投注金额日志记录功能已实现")
    print("✅ 支持详细的马丁格尔、连胜奖励、算法奖励等计算显示")
    print("✅ 在实时投注记录中可以看到完整的动态金额计算过程")

if __name__ == "__main__":
    test_enhanced_amount_logging()
