#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试整数投注金额计算
"""

import os
import json
from datetime import datetime

def test_integer_betting_amounts():
    """测试整数投注金额计算"""
    
    print("🧪 测试整数投注金额计算")
    print("=" * 50)
    
    # 模拟不同的连胜连败状态
    test_scenarios = [
        {
            'name': '初始状态',
            'consecutive_losses': 0,
            'consecutive_wins': 0,
            'current_balance': 200,
            'total_bets': 0,
            'total_wins': 0
        },
        {
            'name': '连败1次',
            'consecutive_losses': 1,
            'consecutive_wins': 0,
            'current_balance': 198,
            'total_bets': 1,
            'total_wins': 0
        },
        {
            'name': '连败2次',
            'consecutive_losses': 2,
            'consecutive_wins': 0,
            'current_balance': 195,
            'total_bets': 2,
            'total_wins': 0
        },
        {
            'name': '连败3次 (高风险)',
            'consecutive_losses': 3,
            'consecutive_wins': 0,
            'current_balance': 190,
            'total_bets': 3,
            'total_wins': 0
        },
        {
            'name': '连败4次 (危险)',
            'consecutive_losses': 4,
            'consecutive_wins': 0,
            'current_balance': 185,
            'total_bets': 4,
            'total_wins': 0
        },
        {
            'name': '连胜3次',
            'consecutive_losses': 0,
            'consecutive_wins': 3,
            'current_balance': 206,
            'total_bets': 5,
            'total_wins': 3
        },
        {
            'name': '连胜5次',
            'consecutive_losses': 0,
            'consecutive_wins': 5,
            'current_balance': 210,
            'total_bets': 8,
            'total_wins': 5
        },
        {
            'name': '余额不足 (50%以下)',
            'consecutive_losses': 1,
            'consecutive_wins': 0,
            'current_balance': 90,  # 45% of initial 200
            'total_bets': 10,
            'total_wins': 4
        }
    ]
    
    print("📊 投注金额计算结果:")
    print("-" * 80)
    print(f"{'场景':<15} {'连败':<6} {'连胜':<6} {'余额':<8} {'投注金额':<10} {'说明'}")
    print("-" * 80)
    
    for scenario in test_scenarios:
        # 模拟计算过程
        base_amount = 2  # 基础金额
        amount = base_amount
        
        # 马丁格尔调整
        if scenario['consecutive_losses'] > 0:
            martingale_add = scenario['consecutive_losses'] * 1
            amount += martingale_add
        
        # 连胜奖励
        if scenario['consecutive_wins'] >= 3:
            win_bonus = (scenario['consecutive_wins'] - 2) * 1
            amount += min(win_bonus, 5)
        
        # 算法奖励
        if scenario['total_bets'] > 10 and scenario['total_wins'] / scenario['total_bets'] > 0.88:
            amount += 1
        
        # 风险调整
        consecutive_losses = scenario['consecutive_losses']
        if consecutive_losses >= 4:
            risk_level = "critical"
            risk_adjustment = -2
        elif consecutive_losses >= 3:
            risk_level = "high"
            risk_adjustment = -1
        elif consecutive_losses >= 2:
            risk_level = "medium"
            risk_adjustment = 0
        else:
            risk_level = "low"
            risk_adjustment = 1
        
        amount += risk_adjustment
        
        # 余额保护
        balance_ratio = scenario['current_balance'] / 200  # initial_balance = 200
        if balance_ratio < 0.5:
            amount -= 1
        
        # 限制范围
        final_amount = max(1, min(amount, 20))  # min=1, max=20
        
        # 余额限制 (20%)
        max_allowed = int(scenario['current_balance'] * 0.2)
        final_amount = min(final_amount, max_allowed)
        
        # 最终确保是正整数
        final_amount = max(1, final_amount)
        
        # 生成说明
        explanation_parts = []
        if scenario['consecutive_losses'] > 0:
            explanation_parts.append(f"马丁格尔+{scenario['consecutive_losses']}")
        if scenario['consecutive_wins'] >= 3:
            win_bonus = min((scenario['consecutive_wins'] - 2) * 1, 5)
            explanation_parts.append(f"连胜+{win_bonus}")
        if risk_adjustment != 0:
            explanation_parts.append(f"风险{risk_adjustment:+d}")
        if balance_ratio < 0.5:
            explanation_parts.append("余额保护-1")
        
        explanation = ", ".join(explanation_parts) if explanation_parts else "基础金额"
        
        print(f"{scenario['name']:<15} {scenario['consecutive_losses']:<6} {scenario['consecutive_wins']:<6} {scenario['current_balance']:<8} {final_amount}元{'':<6} {explanation}")
    
    print("-" * 80)
    print()
    
    print("✅ 整数投注金额计算特点:")
    print("   1. 基础金额: 2元 (整数)")
    print("   2. 马丁格尔: 连败每次+1元 (而非倍数)")
    print("   3. 连胜奖励: 连胜3次以上每次+1元 (最多+5元)")
    print("   4. 风险调整: 根据风险等级±1-2元")
    print("   5. 余额保护: 余额不足50%时-1元")
    print("   6. 范围限制: 1-20元")
    print("   7. 余额限制: 不超过余额的20%")
    print("   8. 结果保证: 始终为正整数")
    print()
    
    print("🎯 实际效果对比:")
    print("   修复前: 1.66元 → API调整为1元 (小数被忽略)")
    print("   修复后: 2元、3元、4元... (直接是整数)")
    print()
    
    print("💡 API兼容性:")
    print("   ✅ 支持的金额: 1, 2, 3, 4, 5, 10, 20, 100, 500")
    print("   ✅ 智能分解: 如果需要7元，会分解为[5, 1, 1]")
    print("   ✅ 无小数: 避免小数点后的精度问题")

def test_smart_betting_handler_compatibility():
    """测试智能投注处理器兼容性"""
    
    print("\n🔧 测试智能投注处理器兼容性")
    print("=" * 50)
    
    # 模拟智能投注处理器的分解逻辑
    def calculate_bet_breakdown(target_amount: int):
        """计算投注金额分解"""
        supported_amounts = [500, 100, 10, 5, 1]  # 从大到小
        breakdown = []
        remaining = target_amount
        
        for amount in supported_amounts:
            count = remaining // amount
            if count > 0:
                breakdown.extend([amount] * count)
                remaining = remaining - (amount * count)
        
        return breakdown
    
    test_amounts = [1, 2, 3, 4, 5, 7, 8, 12, 15, 20]
    
    print("投注金额分解测试:")
    print("-" * 40)
    print(f"{'目标金额':<10} {'分解结果'}")
    print("-" * 40)
    
    for amount in test_amounts:
        breakdown = calculate_bet_breakdown(amount)
        breakdown_str = " + ".join(map(str, breakdown))
        print(f"{amount}元{'':<6} {breakdown_str}")
    
    print("-" * 40)
    print("✅ 所有整数金额都能完美分解!")

if __name__ == "__main__":
    test_integer_betting_amounts()
    test_smart_betting_handler_compatibility()
    
    print("\n🎉 整数投注金额系统测试完成!")
    print("💡 现在系统会:")
    print("   - 计算出整数投注金额 (无小数)")
    print("   - 根据连胜连败动态调整")
    print("   - 完美兼容API的整数要求")
    print("   - 避免小数精度问题")
