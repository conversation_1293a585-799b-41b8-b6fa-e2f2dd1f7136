#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强预测系统
"""

try:
    print("🔧 测试增强预测系统...")
    
    # 测试导入
    from enhanced_prediction_system import EnhancedPredictionSystem
    print("✅ 导入成功")
    
    # 测试API客户端
    from api_framework import GameAPIClient
    print("✅ API客户端导入成功")
    
    # 创建配置
    config = {
        'base_bet_amount': 0.1,
        'min_confidence': 0.6
    }
    
    # 创建API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    # 创建API客户端
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    print("✅ API客户端创建成功")
    
    # 创建增强预测系统
    system = EnhancedPredictionSystem(api_client, config)
    print("✅ 增强预测系统创建成功")
    
    print("🎉 所有测试通过！系统可以正常运行")
    
    # 询问是否启动
    response = input("\n是否启动增强预测系统？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        print("🚀 启动增强预测系统...")
        system.start_monitoring()
    else:
        print("系统未启动")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
