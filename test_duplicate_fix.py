#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复处理修复效果
"""

import json
import os
from datetime import datetime

def test_duplicate_prevention():
    """测试防重复处理机制"""
    
    print("🧪 测试防重复处理机制")
    print("=" * 50)
    
    try:
        from optimized_random_betting_system import OptimizedRandomBettingSystem
        from api_framework import MockAPIClient
        
        # 创建模拟API客户端
        api_client = MockAPIClient()
        
        # 创建投注系统
        system = OptimizedRandomBettingSystem(api_client)
        
        print(f"✅ 系统初始化成功")
        print(f"   连续失败: {system.consecutive_losses}次")
        print(f"   连续获胜: {system.consecutive_wins}次")
        print(f"   已处理期号: {len(system.processed_issues)}个")
        
        # 模拟投注
        test_issue = 999999
        test_room = 5
        test_amount = 10.0
        
        print(f"\n📝 模拟投注:")
        print(f"   期号: {test_issue}")
        print(f"   房间: {test_room}")
        print(f"   金额: {test_amount}元")
        
        # 执行投注
        system.place_bet(test_issue, test_room, test_amount)
        
        # 模拟开奖结果 (失败)
        winning_room = test_room  # 相同房间 = 失败
        
        print(f"\n🎰 模拟开奖:")
        print(f"   期号: {test_issue}")
        print(f"   开奖房间: {winning_room}")
        print(f"   投注房间: {test_room}")
        print(f"   结果: {'失败' if test_room == winning_room else '获胜'}")
        
        # 第一次处理结果
        print(f"\n1️⃣ 第一次处理结果:")
        old_losses = system.consecutive_losses
        system.process_result(test_issue, winning_room)
        new_losses = system.consecutive_losses
        
        print(f"   处理前连续失败: {old_losses}次")
        print(f"   处理后连续失败: {new_losses}次")
        print(f"   已处理期号: {system.processed_issues}")
        
        # 第二次处理相同结果 (应该被跳过)
        print(f"\n2️⃣ 第二次处理相同结果:")
        old_losses_2 = system.consecutive_losses
        system.process_result(test_issue, winning_room)
        new_losses_2 = system.consecutive_losses
        
        print(f"   处理前连续失败: {old_losses_2}次")
        print(f"   处理后连续失败: {new_losses_2}次")
        
        # 验证结果
        if new_losses_2 == old_losses_2:
            print(f"   ✅ 重复处理被正确阻止")
        else:
            print(f"   ❌ 重复处理未被阻止")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_current_state():
    """检查当前系统状态"""
    
    print("\n📊 检查当前系统状态")
    print("=" * 50)
    
    state_file = f"system_state_{datetime.now().strftime('%Y%m%d')}.json"
    
    if os.path.exists(state_file):
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            print(f"📂 状态文件: {state_file}")
            print(f"   连续失败: {state_data.get('consecutive_losses', 0)}次")
            print(f"   连续获胜: {state_data.get('consecutive_wins', 0)}次")
            print(f"   总投注: {state_data.get('total_bets', 0)}次")
            print(f"   总获胜: {state_data.get('total_wins', 0)}次")
            print(f"   当前余额: {state_data.get('current_balance', 0):.2f}元")
            print(f"   总盈亏: {state_data.get('total_profit', 0):.2f}元")
            print(f"   最后更新: {state_data.get('last_update', 'N/A')}")
            
            return state_data
            
        except Exception as e:
            print(f"❌ 读取状态文件失败: {e}")
            return None
    else:
        print(f"❌ 状态文件不存在: {state_file}")
        return None

def verify_betting_log():
    """验证投注记录的连续失败次数"""
    
    print("\n📋 验证投注记录")
    print("=" * 50)
    
    log_file = "betting_log_20250804_083153.md"
    
    if not os.path.exists(log_file):
        print(f"❌ 投注记录文件不存在: {log_file}")
        return None
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找最近的投注记录
        lines = content.split('\n')
        recent_results = []
        
        for line in lines:
            if '131890' in line and '失败' in line:
                recent_results.append('131890: 失败')
            elif '131891' in line and '失败' in line:
                recent_results.append('131891: 失败')
            elif '131892' in line and '失败' in line:
                recent_results.append('131892: 失败')
        
        print(f"📊 最近连续失败记录:")
        for result in recent_results:
            print(f"   {result}")
        
        consecutive_failures = len(recent_results)
        print(f"\n🎯 实际连续失败次数: {consecutive_failures}次")
        
        return consecutive_failures
        
    except Exception as e:
        print(f"❌ 验证投注记录失败: {e}")
        return None

def main():
    """主函数"""
    
    print("🔧 重复处理修复效果测试")
    print("=" * 60)
    
    # 1. 检查当前状态
    current_state = check_current_state()
    
    # 2. 验证投注记录
    actual_failures = verify_betting_log()
    
    # 3. 测试防重复机制
    test_success = test_duplicate_prevention()
    
    # 4. 总结
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    
    if current_state:
        system_failures = current_state.get('consecutive_losses', 0)
        print(f"   系统显示连续失败: {system_failures}次")
    
    if actual_failures is not None:
        print(f"   记录显示连续失败: {actual_failures}次")
    
    if test_success:
        print(f"   ✅ 防重复处理机制正常")
    else:
        print(f"   ❌ 防重复处理机制异常")
    
    # 判断是否修复成功
    if current_state and actual_failures is not None:
        if current_state.get('consecutive_losses', 0) == actual_failures:
            print(f"\n🎉 修复成功！系统状态与实际记录一致")
        else:
            print(f"\n⚠️ 仍有差异，需要进一步调整")
    
    print(f"\n📝 建议:")
    print(f"   1. 重新启动投注系统以应用修复")
    print(f"   2. 观察后续投注是否还有重复统计")
    print(f"   3. 如有问题，检查是否还有其他调用 process_result 的地方")

if __name__ == "__main__":
    main()
