#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的Markdown格式
验证简化后的投注记录表格
"""

import time
from datetime import datetime

def test_new_markdown_format():
    """测试新的Markdown格式"""
    
    print("🧪 测试新的Markdown记录格式")
    print("=" * 50)
    
    try:
        from real_time_logger import log_room_selection, log_betting, log_result, get_summary
        
        # 模拟多次投注记录
        test_data = [
            {
                'issue': 130020,
                'bet_room': 3,
                'bet_amount': 1.5,
                'actual_room': 7,
                'result': '获胜',
                'profit': 0.15
            },
            {
                'issue': 130021,
                'bet_room': 5,
                'bet_amount': 2.0,
                'actual_room': 5,
                'result': '失败',
                'profit': -2.0
            },
            {
                'issue': 130022,
                'bet_room': 1,
                'bet_amount': 3.0,
                'actual_room': 8,
                'result': '获胜',
                'profit': 0.3
            }
        ]
        
        print("📝 创建测试投注记录...")
        
        for i, data in enumerate(test_data):
            print(f"   {i+1}. 期号{data['issue']}: 房间{data['bet_room']}, {data['bet_amount']}元")
            
            # 记录房间选择
            log_room_selection(
                data['issue'], 
                list(range(1, 9)), 
                data['bet_room'], 
                "LCG算法", 
                {"algorithm": "LCG"}
            )
            
            # 记录投注
            log_betting(data['issue'], data['bet_room'], data['bet_amount'], True)
            
            # 记录结果
            log_result(data['issue'], data['actual_room'], data['result'], data['profit'])
            
            # 短暂延迟以区分时间
            time.sleep(0.1)
        
        print("✅ 测试记录创建完成")
        
        # 获取摘要
        summary = get_summary()
        print(f"\n📊 投注摘要:")
        print(f"   总投注: {summary['total_bets']}次")
        print(f"   总获胜: {summary['total_wins']}次")
        print(f"   胜率: {(summary['total_wins']/summary['total_bets']*100):.1f}%")
        print(f"   总盈亏: {summary['total_profit']:+.2f}元")
        
        print(f"\n✅ 新格式Markdown文件已生成")
        print(f"请查看生成的文件，验证新格式:")
        print(f"   - 简化的表格列: 时间 | 期号 | 投注房间 | 投注金额 | 开奖房间 | 结果 | 盈亏")
        print(f"   - 去掉了: 预测房间、置信度等不必要的列")
        print(f"   - 新增了: 投注金额列")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_markdown_preview():
    """显示Markdown格式预览"""
    
    print("\n📋 新的Markdown格式预览:")
    print("=" * 50)
    
    print("""
# 🎯 投注系统实时记录

**会话开始时间**: 2025-08-02 08:06:19
**最后更新时间**: 2025-08-02 08:06:25

## 📊 实时统计

- **总投注次数**: 3
- **获胜次数**: 2
- **胜率**: 66.7%
- **总盈亏**: -1.55元

## 📋 详细投注记录

| 时间 | 期号 | 投注房间 | 投注金额 | 开奖房间 | 结果 | 盈亏 |
|------|------|----------|----------|----------|------|------|
| 08:06:20 | 130020 | 3 | 1.50元 | 7 | 获胜 | +0.15元 |
| 08:06:21 | 130021 | 5 | 2.00元 | 5 | 失败 | -2.00元 |
| 08:06:22 | 130022 | 1 | 3.00元 | 8 | 获胜 | +0.30元 |

## 🎯 LCG算法选择详情

### 期号130020 - 第1次投注

- **选择策略**: LCG算法
- **投注房间**: 3
- **投注金额**: 1.50元
- **开奖房间**: 7
- **投注结果**: 获胜
- **盈亏**: +0.15元

### 期号130021 - 第2次投注

- **选择策略**: LCG算法
- **投注房间**: 5
- **投注金额**: 2.00元
- **开奖房间**: 5
- **投注结果**: 失败
- **盈亏**: -2.00元
    """)
    
    print("✨ 新格式特点:")
    print("   🎯 表格更简洁，只显示关键信息")
    print("   💰 新增投注金额列，方便查看每次投注")
    print("   🕒 显示投注时间，便于追踪")
    print("   📊 保留完整的统计信息")
    print("   🎲 LCG算法选择详情更清晰")

def main():
    """主测试函数"""
    
    print("🎨 Markdown格式优化测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示格式预览
    show_markdown_preview()
    
    # 执行测试
    test_result = test_new_markdown_format()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   新格式测试: {'✅ 通过' if test_result else '❌ 失败'}")
    
    if test_result:
        print("\n🎉 Markdown格式优化完成!")
        print("\n💡 现在您的投注记录将显示:")
        print("   ✅ 简化的表格 - 只显示关键信息")
        print("   ✅ 投注金额列 - 清楚显示每次投注金额")
        print("   ✅ 时间信息 - 精确到秒的投注时间")
        print("   ✅ LCG详情 - 清晰的算法选择过程")
        
        print("\n🚀 您可以:")
        print("   1. 重新运行真实投注查看新格式")
        print("   2. 检查生成的betting_log_*.md文件")
        print("   3. 享受更清晰的投注记录体验")
    else:
        print("\n⚠️ 测试失败，请检查相关问题")
    
    return 0 if test_result else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
