# 🎯 随机数算法投注系统流程图

## 📊 系统整体架构流程图

```mermaid
graph TD
    A[用户启动] --> B{选择启动方式}
    
    B -->|智能选择| C[strategy_selector.py]
    B -->|直接启动| D[单独系统启动]
    B -->|数据初始化| E[initialize_history.py]
    
    C --> F{检查历史数据量}
    F -->|0-40期| G[frequency_based_betting.py]
    F -->|40-50期| H[enhanced_prediction_system.py]
    F -->|50期以上| I[complete_betting_system.py]
    
    D --> J[single_room_betting.py]
    D --> K[api_framework.py]
    D --> L[game_data_collector.py]
    
    E --> M[创建初始历史数据]
    M --> N[game_history.json]
    
    G --> O[频率统计投注]
    H --> P[增强预测投注]
    I --> Q[完整预测投注]
    J --> R[单房间投注]
    
    O --> S[API调用]
    P --> S
    Q --> S
    R --> S
    
    S --> T[api_framework.py]
    T --> U{投注结果}
    
    U -->|成功| V[更新历史数据]
    U -->|失败| W[错误处理]
    
    V --> N
    W --> X[日志记录]
    
    N --> Y[数据持久化]
    Y --> Z[下一轮投注]
    Z --> F
```

## 🔄 投注执行流程图

```mermaid
graph TD
    A[系统启动] --> B[加载历史数据]
    B --> C[分析频率分布]
    C --> D[计算最佳投注房间]
    
    D --> E[监控游戏状态]
    E --> F{游戏状态检查}
    
    F -->|state=1 等待投注| G{倒计时检查}
    F -->|state=2 已开奖| H[处理开奖结果]
    
    G -->|15-25秒| I[执行投注]
    G -->|其他时间| E
    
    I --> J[API投注请求]
    J --> K{投注响应}
    
    K -->|200 成功| L[记录投注信息]
    K -->|400/其他| M[投注失败处理]
    
    L --> N[等待开奖]
    M --> O[错误日志]
    
    N --> H
    H --> P[更新历史数据]
    P --> Q[计算投注结果]
    Q --> R[更新统计信息]
    R --> E
    
    O --> E
```

## 📈 数据流转流程图

```mermaid
graph LR
    A[游戏API] --> B[game_data_collector.py]
    B --> C[实时数据收集]
    C --> D[game_history.json]
    
    D --> E[数据分析模块]
    E --> F[频率统计]
    E --> G[模式识别]
    E --> H[预测生成]
    
    F --> I[frequency_based_betting.py]
    G --> J[enhanced_prediction_system.py]
    H --> K[complete_betting_system.py]
    
    I --> L[投注决策]
    J --> L
    K --> L
    
    L --> M[api_framework.py]
    M --> N[投注执行]
    N --> O[结果反馈]
    O --> D
```

## 🎯 策略选择决策树

```mermaid
graph TD
    A[strategy_selector.py启动] --> B{检查game_history.json}
    
    B -->|文件不存在| C[推荐initialize_history.py]
    B -->|文件存在| D{检查数据量}
    
    D -->|0-40期| E[推荐frequency_based_betting.py]
    D -->|40-50期| F[推荐enhanced_prediction_system.py]
    D -->|50期以上| G[推荐complete_betting_system.py]
    
    C --> H[数据初始化]
    E --> I[频率投注策略]
    F --> J[增强预测策略]
    G --> K[完整预测策略]
    
    H --> L[开始数据收集]
    I --> M[87.5%理论胜率]
    J --> N[90-95%预期胜率]
    K --> O[95%+预期胜率]
    
    L --> P[自动切换到频率策略]
    M --> Q[数据积累到40期后升级]
    N --> R[数据积累到50期后升级]
    O --> S[持续优化]
```

## 🔧 问题诊断流程图

```mermaid
graph TD
    A[系统问题] --> B{问题类型}
    
    B -->|胜率为0| C[检查历史数据]
    B -->|投注失败| D[检查投注时机]
    B -->|数据不更新| E[检查数据持久化]
    B -->|重复投注| F[检查投注逻辑]
    
    C --> G{game_history.json存在?}
    G -->|否| H[运行initialize_history.py]
    G -->|是| I[检查数据格式]
    
    D --> J{倒计时检查}
    J -->|投注时机错误| K[调整投注时机到15-25秒]
    J -->|API限制| L[检查投注频率]
    
    E --> M{last_processed_issue字段}
    M -->|缺失| N[手动添加字段]
    M -->|存在| O[检查更新逻辑]
    
    F --> P{单房间投注?}
    P -->|否| Q[切换到single_room_betting.py]
    P -->|是| R[检查API响应]
    
    H --> S[问题解决]
    I --> S
    K --> S
    L --> S
    N --> S
    O --> S
    Q --> S
    R --> S
```

## 💡 系统优化建议流程

```mermaid
graph TD
    A[系统运行] --> B[性能监控]
    B --> C{胜率检查}
    
    C -->|<80%| D[策略调整]
    C -->|80-90%| E[继续运行]
    C -->|>90%| F[策略升级]
    
    D --> G[检查数据质量]
    G --> H[增加数据收集]
    H --> I[重新分析模式]
    
    E --> J[定期数据备份]
    J --> K[监控系统稳定性]
    
    F --> L{数据量检查}
    L -->|足够| M[升级到更高级策略]
    L -->|不足| N[继续数据积累]
    
    I --> O[策略优化]
    K --> P[系统维护]
    M --> Q[性能提升]
    N --> R[等待升级时机]
    
    O --> A
    P --> A
    Q --> A
    R --> A
```

---

## 📋 流程图说明

### 🎯 **主要流程**
1. **系统启动** → **策略选择** → **数据分析** → **投注执行** → **结果处理**
2. **数据收集** → **模式识别** → **策略优化** → **性能提升**

### 🔄 **核心循环**
- **监控** → **分析** → **投注** → **更新** → **监控**

### 📊 **数据流向**
- **API数据** → **历史存储** → **分析处理** → **投注决策** → **结果反馈**

### 🛠️ **问题处理**
- **问题识别** → **原因分析** → **解决方案** → **验证修复**

这些流程图清晰展示了整个系统的运行逻辑和各个组件之间的关系。
