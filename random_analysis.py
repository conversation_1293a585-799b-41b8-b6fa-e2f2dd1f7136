#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机数序列逆向分析工具
用于分析游戏中的随机数生成算法和种子
"""

import numpy as np
import matplotlib.pyplot as plt
from collections import Counter
import itertools
from typing import List, Tuple, Optional

class RandomAnalyzer:
    def __init__(self, data_file: str):
        """初始化分析器"""
        self.data_file = data_file
        self.sequence = self.load_data()
        self.n = len(self.sequence)
        
    def load_data(self) -> List[int]:
        """加载随机数序列数据"""
        with open(self.data_file, 'r', encoding='utf-8') as f:
            return [int(line.strip()) for line in f if line.strip()]
    
    def basic_statistics(self) -> dict:
        """基础统计分析"""
        seq = np.array(self.sequence)
        stats = {
            'count': len(seq),
            'min': int(np.min(seq)),
            'max': int(np.max(seq)),
            'mean': float(np.mean(seq)),
            'std': float(np.std(seq)),
            'variance': float(np.var(seq))
        }
        return stats
    
    def frequency_analysis(self) -> dict:
        """频率分布分析"""
        counter = Counter(self.sequence)
        total = len(self.sequence)
        
        freq_dist = {}
        for i in range(1, 9):  # 1-8的数字
            count = counter.get(i, 0)
            freq_dist[i] = {
                'count': count,
                'frequency': count / total,
                'expected': 1/8,  # 理论均匀分布
                'deviation': abs(count / total - 1/8)
            }
        
        return freq_dist
    
    def periodicity_check(self, max_period: int = 20) -> dict:
        """检查周期性"""
        results = {}
        
        for period in range(2, min(max_period + 1, self.n // 2)):
            matches = 0
            comparisons = 0
            
            for i in range(self.n - period):
                if self.sequence[i] == self.sequence[i + period]:
                    matches += 1
                comparisons += 1
            
            if comparisons > 0:
                match_rate = matches / comparisons
                results[period] = {
                    'matches': matches,
                    'comparisons': comparisons,
                    'match_rate': match_rate
                }
        
        return results
    
    def autocorrelation_analysis(self, max_lag: int = 10) -> dict:
        """自相关分析"""
        seq = np.array(self.sequence, dtype=float)
        seq_centered = seq - np.mean(seq)
        
        autocorr = {}
        for lag in range(1, min(max_lag + 1, len(seq) // 2)):
            if len(seq_centered) > lag:
                corr = np.corrcoef(seq_centered[:-lag], seq_centered[lag:])[0, 1]
                autocorr[lag] = float(corr) if not np.isnan(corr) else 0.0
        
        return autocorr
    
    def pattern_analysis(self) -> dict:
        """模式分析 - 查找重复的子序列"""
        patterns = {}
        
        # 检查长度为2-5的子序列
        for length in range(2, 6):
            pattern_count = Counter()
            
            for i in range(self.n - length + 1):
                pattern = tuple(self.sequence[i:i + length])
                pattern_count[pattern] += 1
            
            # 只保留出现次数大于1的模式
            repeated_patterns = {k: v for k, v in pattern_count.items() if v > 1}
            if repeated_patterns:
                patterns[length] = repeated_patterns
        
        return patterns
    
    def run_basic_analysis(self) -> dict:
        """运行基础分析"""
        print("=== 随机数序列基础分析 ===")
        print(f"序列长度: {self.n}")
        print(f"序列内容: {self.sequence}")
        print()
        
        # 基础统计
        stats = self.basic_statistics()
        print("基础统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        print()
        
        # 频率分析
        freq_dist = self.frequency_analysis()
        print("频率分布分析:")
        for num in range(1, 9):
            info = freq_dist[num]
            print(f"  数字 {num}: 出现 {info['count']} 次 "
                  f"({info['frequency']:.3f}, 期望: {info['expected']:.3f}, "
                  f"偏差: {info['deviation']:.3f})")
        print()
        
        # 周期性检查
        periods = self.periodicity_check()
        print("周期性检查:")
        high_correlation_periods = []
        for period, info in periods.items():
            if info['match_rate'] > 0.3:  # 匹配率超过30%认为可能有周期性
                high_correlation_periods.append((period, info['match_rate']))
                print(f"  周期 {period}: 匹配率 {info['match_rate']:.3f} "
                      f"({info['matches']}/{info['comparisons']})")
        
        if not high_correlation_periods:
            print("  未发现明显的周期性")
        print()
        
        # 自相关分析
        autocorr = self.autocorrelation_analysis()
        print("自相关分析:")
        significant_lags = []
        for lag, corr in autocorr.items():
            if abs(corr) > 0.2:  # 相关系数绝对值大于0.2认为显著
                significant_lags.append((lag, corr))
                print(f"  滞后 {lag}: 相关系数 {corr:.3f}")
        
        if not significant_lags:
            print("  未发现显著的自相关性")
        print()
        
        # 模式分析
        patterns = self.pattern_analysis()
        print("重复模式分析:")
        if patterns:
            for length, pattern_dict in patterns.items():
                print(f"  长度 {length} 的重复模式:")
                for pattern, count in sorted(pattern_dict.items(), 
                                           key=lambda x: x[1], reverse=True)[:5]:
                    print(f"    {pattern}: 出现 {count} 次")
        else:
            print("  未发现明显的重复模式")
        
        return {
            'statistics': stats,
            'frequency': freq_dist,
            'periodicity': periods,
            'autocorrelation': autocorr,
            'patterns': patterns
        }

if __name__ == "__main__":
    analyzer = RandomAnalyzer("随机生成1-8.txt")
    results = analyzer.run_basic_analysis()
