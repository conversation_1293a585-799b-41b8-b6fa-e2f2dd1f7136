#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于历史数据分析的新盈利投注系统
解决87.8%胜率仍亏损的核心问题
"""

import json
import time
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
from collections import Counter, defaultdict

from api_framework import GameAPIClient, GameMonitor, GameState
from prediction_strategy_adapter import PredictionRuleAdapter
from profitable_betting_strategy import ProfitableBettingStrategy
from real_time_logger import log_prediction, log_room_selection, log_betting, log_result
from smart_betting_handler import SmartBettingHandler


class AdvancedRuleExpander:
    """高级规则扩展器 - 科学的规则扩展策略"""

    def __init__(self):
        self.expansion_strategies = [
            'multi_threshold',      # 多层置信度阈值
            'condition_variants',   # 条件序列变体
            'confidence_adjustment', # 置信度调整
            'support_weighting',    # 支持度加权
            'pattern_generalization' # 模式泛化
        ]

    def expand_rules_comprehensive(self, base_rules: List[Dict]) -> List[Dict]:
        """综合规则扩展策略"""
        expanded_rules = []

        print(f"🔧 开始综合规则扩展，基础规则: {len(base_rules)}个")

        # 策略1: 多层置信度阈值扩展
        threshold_rules = self.expand_by_multiple_thresholds(base_rules)
        expanded_rules.extend(threshold_rules)
        print(f"   多层阈值扩展: +{len(threshold_rules)}个")

        # 策略2: 条件序列变体生成
        variant_rules = self.generate_condition_variants(base_rules)
        expanded_rules.extend(variant_rules)
        print(f"   条件变体生成: +{len(variant_rules)}个")

        # 策略3: 置信度自适应调整
        adjusted_rules = self.adjust_confidence_adaptively(base_rules)
        expanded_rules.extend(adjusted_rules)
        print(f"   置信度调整: +{len(adjusted_rules)}个")

        # 策略4: 支持度加权扩展
        weighted_rules = self.expand_by_support_weighting(base_rules)
        expanded_rules.extend(weighted_rules)
        print(f"   支持度加权: +{len(weighted_rules)}个")

        return expanded_rules

    def expand_by_multiple_thresholds(self, base_rules: List[Dict]) -> List[Dict]:
        """多层置信度阈值扩展"""
        expanded_rules = []

        # 定义多个置信度阈值层级
        thresholds = [
            {'min_conf': 0.85, 'weight': 1.0, 'label': 'ultra_high'},
            {'min_conf': 0.80, 'weight': 0.9, 'label': 'high'},
            {'min_conf': 0.75, 'weight': 0.8, 'label': 'medium_high'},
            {'min_conf': 0.70, 'weight': 0.7, 'label': 'medium'},
            {'min_conf': 0.65, 'weight': 0.6, 'label': 'medium_low'}
        ]

        for threshold in thresholds:
            qualified_rules = [
                rule for rule in base_rules
                if rule['confidence'] >= threshold['min_conf']
            ]

            for rule in qualified_rules:
                expanded_rule = rule.copy()
                expanded_rule['threshold_level'] = threshold['label']
                expanded_rule['expansion_weight'] = threshold['weight']
                expanded_rule['expansion_method'] = 'multi_threshold'
                expanded_rules.append(expanded_rule)

        return expanded_rules

    def generate_condition_variants(self, base_rules: List[Dict]) -> List[Dict]:
        """生成条件序列变体"""
        variant_rules = []

        for rule in base_rules:
            condition = rule['condition']

            # 生成子序列变体（如果条件长度>3）
            if len(condition) > 3:
                for start_idx in range(len(condition) - 2):
                    for end_idx in range(start_idx + 3, len(condition) + 1):
                        sub_condition = condition[start_idx:end_idx]

                        variant_rule = rule.copy()
                        variant_rule['condition'] = sub_condition
                        variant_rule['confidence'] *= 0.85  # 降低置信度
                        variant_rule['expansion_method'] = 'condition_variant'
                        variant_rule['original_condition'] = condition
                        variant_rules.append(variant_rule)

        return variant_rules

    def adjust_confidence_adaptively(self, base_rules: List[Dict]) -> List[Dict]:
        """自适应置信度调整"""
        adjusted_rules = []

        # 计算置信度分布统计
        confidences = [rule['confidence'] for rule in base_rules]
        mean_conf = np.mean(confidences)
        std_conf = np.std(confidences)

        for rule in base_rules:
            # 基于统计分布调整置信度
            z_score = (rule['confidence'] - mean_conf) / std_conf if std_conf > 0 else 0

            # 创建调整后的规则变体
            if abs(z_score) < 1.0:  # 在1个标准差内的规则
                adjusted_rule = rule.copy()
                adjusted_rule['confidence'] = min(0.95, rule['confidence'] * 1.1)
                adjusted_rule['expansion_method'] = 'confidence_boost'
                adjusted_rule['z_score'] = z_score
                adjusted_rules.append(adjusted_rule)

        return adjusted_rules

    def expand_by_support_weighting(self, base_rules: List[Dict]) -> List[Dict]:
        """基于支持度的加权扩展"""
        weighted_rules = []

        # 计算支持度统计
        supports = [rule.get('support', 1) for rule in base_rules]
        max_support = max(supports) if supports else 1

        for rule in base_rules:
            support = rule.get('support', 1)
            support_ratio = support / max_support

            # 高支持度规则生成更多变体
            if support_ratio > 0.5:
                # 生成置信度微调变体
                for adjustment in [0.95, 1.0, 1.05]:
                    weighted_rule = rule.copy()
                    weighted_rule['confidence'] = min(0.99, rule['confidence'] * adjustment)
                    weighted_rule['expansion_method'] = 'support_weighted'
                    weighted_rule['support_ratio'] = support_ratio
                    weighted_rule['adjustment_factor'] = adjustment
                    weighted_rules.append(weighted_rule)

        return weighted_rules

    def evaluate_and_filter_rules(self, expanded_rules: List[Dict]) -> List[Dict]:
        """规则质量评估和筛选"""
        print(f"🔍 开始规则质量评估，待评估: {len(expanded_rules)}个")

        # 去重：基于条件和预测值
        unique_rules = {}
        for rule in expanded_rules:
            rule_key = f"{rule['condition']}_{rule['predicted_value']}"

            # 保留置信度最高的规则
            if rule_key not in unique_rules or rule['confidence'] > unique_rules[rule_key]['confidence']:
                unique_rules[rule_key] = rule

        deduplicated_rules = list(unique_rules.values())
        print(f"   去重后: {len(deduplicated_rules)}个")

        # 质量筛选
        quality_rules = []
        for rule in deduplicated_rules:
            quality_score = self.calculate_rule_quality_score(rule)

            if quality_score >= 0.6:  # 质量阈值
                rule['quality_score'] = quality_score
                quality_rules.append(rule)

        # 按质量分数排序
        quality_rules.sort(key=lambda x: x['quality_score'], reverse=True)

        print(f"   质量筛选后: {len(quality_rules)}个")
        return quality_rules

    def calculate_rule_quality_score(self, rule: Dict) -> float:
        """计算规则质量分数"""
        score = 0.0

        # 置信度权重 (40%)
        confidence_score = rule['confidence'] * 0.4
        score += confidence_score

        # 支持度权重 (20%)
        support = rule.get('support', 1)
        support_score = min(1.0, support / 10) * 0.2
        score += support_score

        # 条件长度权重 (20%) - 适中长度更好
        condition_length = len(rule['condition'])
        if 3 <= condition_length <= 5:
            length_score = 0.2
        elif condition_length == 6:
            length_score = 0.15
        else:
            length_score = 0.1
        score += length_score

        # 扩展方法权重 (10%)
        method_scores = {
            'multi_threshold': 0.1,
            'condition_variant': 0.08,
            'confidence_boost': 0.09,
            'support_weighted': 0.07
        }
        method_score = method_scores.get(rule.get('expansion_method', ''), 0.05)
        score += method_score

        # 扩展权重 (10%)
        expansion_weight = rule.get('expansion_weight', 0.5) * 0.1
        score += expansion_weight

        return min(1.0, score)


class NewProfitableSystem:
    """新盈利投注系统"""
    
    def __init__(self, api_client: GameAPIClient, config: Dict):
        """初始化新盈利系统"""
        self.api_client = api_client
        self.config = config
        self.monitor = GameMonitor(api_client)
        
        # 核心组件
        self.prediction_adapter = PredictionRuleAdapter()
        self.profitable_strategy = ProfitableBettingStrategy()
        self.smart_betting_handler = SmartBettingHandler(api_client)

        # 正确的规则扩展机制
        self.implement_correct_rule_expansion()

        # 历史数据和动态规则
        self.history = []
        self.dynamic_rules = []
        self.load_history_data()

        # 生成动态规则
        if len(self.history) >= 20:
            self.generate_dynamic_rules()

        # 系统状态
        self.is_running = False
        self.current_issue = 0
        self.session_stats = {
            'total_bets': 0,
            'total_wins': 0,
            'total_profit': 0.0,
            'max_consecutive_losses': 0,
            'current_consecutive_losses': 0
        }

        # 初始化实时记录器
        self.init_real_time_logger()
        
        print("🚀 新盈利投注系统已初始化")
        print(f"📊 历史数据: {len(self.history)}期")
        print(f"💡 核心改进: 解决高胜率低盈利问题")

    def implement_correct_rule_expansion(self):
        """实现科学的规则扩展机制"""
        try:
            print("🔧 实施科学的规则扩展策略...")

            # 获取基础规则
            base_rules = self.prediction_adapter.load_prediction_rules_from_analysis()

            # 创建高级规则扩展器
            rule_expander = AdvancedRuleExpander()

            # 多策略规则扩展
            expanded_rules = rule_expander.expand_rules_comprehensive(base_rules)

            # 规则质量评估和筛选
            quality_rules = rule_expander.evaluate_and_filter_rules(expanded_rules)

            # 重新分类扩展后的规则
            self.prediction_adapter.categorize_rules_correctly(quality_rules)

            print(f"✅ 科学规则扩展完成:")
            print(f"   基础规则: {len(base_rules)}个")
            print(f"   扩展规则: {len(expanded_rules)}个")
            print(f"   质量筛选后: {len(quality_rules)}个")

            # 保存扩展统计信息
            self.rule_expansion_stats = {
                'base_rules': len(base_rules),
                'expanded_rules': len(expanded_rules),
                'quality_rules': len(quality_rules),
                'expansion_ratio': len(quality_rules) / len(base_rules) if base_rules else 0
            }

        except Exception as e:
            print(f"⚠️ 规则扩展失败: {e}")



    def init_real_time_logger(self):
        """初始化实时记录器"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_filename = f"new_profitable_log_{timestamp}.md"

        # 创建记录文件
        log_content = f"""# 🎯 新盈利投注系统实时记录

**会话开始时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**最后更新时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 📊 实时统计

- **总投注次数**: 0
- **获胜次数**: 0
- **胜率**: 0.0%
- **总盈亏**: +0.00元

## 📋 详细投注记录

| 时间 | 期号 | 预测房间 | 置信度 | 规则类型 | 投注房间 | 选择策略 | 开奖房间 | 结果 | 盈亏 |
|------|------|----------|--------|----------|----------|----------|----------|------|------|


## 🎯 智能房间选择详情

"""

        try:
            with open(self.log_filename, 'w', encoding='utf-8') as f:
                f.write(log_content)

            print(f"📝 实时记录器已启动，文件将保存为: {self.log_filename}")

        except Exception as e:
            print(f"⚠️ 实时记录器初始化失败: {e}")
            self.log_filename = None

    def log_betting_record(self, issue: int, prediction: Dict, bet_room: int, bet_amount: float,
                          result_room: int = None, profit: float = None, selection_details: Dict = None):
        """记录投注信息"""

        if not self.log_filename:
            return

        try:
            current_time = datetime.now().strftime("%H:%M:%S")

            # 准备记录数据
            prediction_room = prediction.get('room', 'N/A')
            confidence = prediction.get('confidence', 0.0)
            rule_type = prediction.get('rule_type', 'unknown')
            selection_strategy = selection_details.get('strategy', '未知策略') if selection_details else '未知策略'

            if result_room is not None and profit is not None:
                # 完整记录（包含结果）
                result_text = "获胜" if profit > 0 else "失败"
                profit_text = f"{profit:+.2f}元"

                record_line = f"| {current_time} | {issue} | {prediction_room} | {confidence:.3f} | {rule_type} | {bet_room} | {selection_strategy} | {result_room} | {result_text} | {profit_text} |\n"

                # 添加详细选择分析
                self.add_selection_details(issue, prediction, bet_room, result_room, profit, selection_details)
            else:
                # 投注记录（等待结果）
                record_line = f"| {current_time} | {issue} | {prediction_room} | {confidence:.3f} | {rule_type} | {bet_room} | {selection_strategy} | - | - | - |\n"

            # 读取现有内容
            with open(self.log_filename, 'r', encoding='utf-8') as f:
                content = f.read()

            # 添加新记录
            content += record_line

            # 更新统计信息和时间戳
            if result_room is not None and profit is not None:
                content = self.update_statistics_in_log(content, profit)

            content = self.update_timestamp_in_log(content)

            # 写回文件
            with open(self.log_filename, 'w', encoding='utf-8') as f:
                f.write(content)

        except Exception as e:
            print(f"⚠️ 记录投注信息失败: {e}")

    def add_selection_details(self, issue: int, prediction: Dict, bet_room: int, result_room: int,
                             profit: float, selection_details: Dict):
        """添加详细的房间选择分析"""

        if not selection_details:
            return

        try:
            # 计算投注次数
            bet_count = getattr(self, '_bet_counter', 0) + 1
            setattr(self, '_bet_counter', bet_count)

            result_text = "获胜" if profit > 0 else "失败"
            profit_text = f"{profit:+.2f}元"

            details_content = f"""
### 期号{issue} - 第{bet_count}次投注

- **预测房间**: {prediction.get('room', 'N/A')} (置信度: {prediction.get('confidence', 0.0):.3f})
- **规则类型**: {prediction.get('rule_type', 'unknown')}
- **可选房间**: {selection_details.get('available_rooms', [])}
- **选择策略**: {selection_details.get('strategy', '未知策略')}
- **投注房间**: {bet_room}
- **开奖房间**: {result_room}
- **投注结果**: {result_text}
- **盈亏**: {profit_text}

**选择详情**:
- predicted_room: {prediction.get('room', 'N/A')}
- available_rooms: {selection_details.get('available_rooms', [])}
- confidence: {prediction.get('confidence', 0.0)}
- strategy: {selection_details.get('strategy', '未知策略')}
- room_frequencies: {selection_details.get('room_frequencies', {})}
- selection_reason: {selection_details.get('reason', '无详细说明')}

"""

            # 读取现有内容
            with open(self.log_filename, 'r', encoding='utf-8') as f:
                content = f.read()

            # 在智能房间选择详情部分添加内容
            content += details_content

            # 写回文件
            with open(self.log_filename, 'w', encoding='utf-8') as f:
                f.write(content)

        except Exception as e:
            print(f"⚠️ 添加选择详情失败: {e}")

    def update_timestamp_in_log(self, content: str) -> str:
        """更新日志中的时间戳"""

        lines = content.split('\n')
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        for i, line in enumerate(lines):
            if line.startswith('**最后更新时间**'):
                lines[i] = f"**最后更新时间**: {current_time}"
                break

        return '\n'.join(lines)

    def calculate_room_frequencies(self) -> Dict[int, int]:
        """计算最近历史中各房间的出现频率"""

        if len(self.history) < 10:
            return {}

        # 使用最近20期数据计算频率
        recent_history = self.history[-20:] if len(self.history) >= 20 else self.history

        frequencies = {}
        for room in range(1, 9):
            frequencies[room] = recent_history.count(room)

        return frequencies

    def update_statistics_in_log(self, content: str, profit: float) -> str:
        """更新日志中的统计信息"""

        # 更新会话统计
        self.session_stats['total_bets'] += 1
        if profit > 0:
            self.session_stats['total_wins'] += 1
            self.session_stats['current_consecutive_losses'] = 0
        else:
            self.session_stats['current_consecutive_losses'] += 1
            self.session_stats['max_consecutive_losses'] = max(
                self.session_stats['max_consecutive_losses'],
                self.session_stats['current_consecutive_losses']
            )

        self.session_stats['total_profit'] += profit

        # 计算胜率
        win_rate = (self.session_stats['total_wins'] / self.session_stats['total_bets'] * 100) if self.session_stats['total_bets'] > 0 else 0.0

        # 更新日志内容中的统计信息
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('- **总投注次数**'):
                lines[i] = f"- **总投注次数**: {self.session_stats['total_bets']}"
            elif line.startswith('- **获胜次数**'):
                lines[i] = f"- **获胜次数**: {self.session_stats['total_wins']}"
            elif line.startswith('- **胜率**'):
                lines[i] = f"- **胜率**: {win_rate:.1f}%"
            elif line.startswith('- **总盈亏**'):
                profit_sign = '+' if self.session_stats['total_profit'] >= 0 else ''
                lines[i] = f"- **总盈亏**: {profit_sign}{self.session_stats['total_profit']:.2f}元"

        return '\n'.join(lines)
    
    def load_history_data(self):
        """加载历史数据"""
        try:
            with open("game_history.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.history = data.get('history', [])
            print(f"✅ 加载历史数据: {len(self.history)}期")
        except Exception as e:
            print(f"⚠️ 加载历史数据失败: {e}")
            self.history = []

    def generate_dynamic_rules(self):
        """基于历史数据生成动态规则"""

        if len(self.history) < 20:
            print("📊 历史数据不足，无法生成动态规则")
            return

        print(f"🔄 基于{len(self.history)}期历史数据生成动态规则...")

        rules = []

        # 生成3-5元条件规则
        for condition_length in [3, 4, 5]:
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)

            for i in range(condition_length, len(self.history)):
                condition = tuple(self.history[i-condition_length:i])
                next_val = self.history[i]

                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1

            # 提取规则
            min_support = 2
            min_confidence = 0.6 if condition_length == 3 else 0.7 if condition_length == 4 else 0.8

            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total

                    if confidence >= min_confidence:
                        rules.append({
                            'condition': condition,
                            'predicted_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length,
                            'source': 'dynamic'
                        })

        # 按置信度排序
        rules.sort(key=lambda x: x['confidence'], reverse=True)

        self.dynamic_rules = rules

        print(f"✅ 生成动态规则: {len(rules)}个")
        if rules:
            print("最佳动态规则:")
            for i, rule in enumerate(rules[:5], 1):
                print(f"  {i}. {rule['condition']} -> {rule['predicted_value']} "
                      f"(置信度: {rule['confidence']:.3f})")

        return rules

    def get_prediction(self) -> Optional[Dict]:
        """获取预测结果（优先动态规则）"""

        if len(self.history) < 10:
            print("📊 历史数据不足，无法预测")
            return None

        recent_history = self.history[-20:] if len(self.history) >= 20 else self.history

        try:
            # 1. 首先尝试动态规则
            dynamic_prediction = self.predict_with_dynamic_rules(recent_history, 0.6)
            if dynamic_prediction:
                return dynamic_prediction

            # 2. 如果动态规则无效，尝试静态规则
            static_prediction = self.prediction_adapter.predict_next_room(recent_history, 0.6)
            if static_prediction:
                return {
                    'room': static_prediction['predicted_room'],
                    'confidence': static_prediction['confidence'],
                    'rule_type': 'static'
                }

            print("📊 未找到匹配的预测规则")
            return None

        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            return None

    def predict_with_dynamic_rules(self, recent_history: List[int], min_confidence: float = 0.6) -> Optional[Dict]:
        """使用动态规则进行预测"""

        if not self.dynamic_rules:
            return None

        # 尝试匹配动态规则
        for rule in self.dynamic_rules:
            if rule['confidence'] < min_confidence:
                continue

            condition = rule['condition']
            condition_length = len(condition)

            if len(recent_history) >= condition_length:
                recent_sequence = tuple(recent_history[-condition_length:])

                if recent_sequence == condition:
                    return {
                        'room': rule['predicted_value'],
                        'confidence': rule['confidence'],
                        'rule_type': 'dynamic',
                        'condition': condition,
                        'support': rule['support']
                    }

        return None
    
    def execute_new_betting_strategy(self, prediction: Dict):
        """执行新的投注策略"""
        
        print(f"\n🎯 新策略投注分析:")
        print(f"   预测房间: {prediction['room']}")
        print(f"   置信度: {prediction['confidence']:.3f}")
        print(f"   规则类型: {prediction['rule_type']}")
        
        # 记录预测
        log_prediction(self.current_issue, prediction['room'], 
                      prediction['confidence'], prediction['rule_type'])
        
        # 获取策略建议
        game_state = {'issue': self.current_issue, 'countdown': 25}
        strategy_result = self.profitable_strategy.execute_profitable_strategy(
            game_state, prediction, self.history
        )
        
        if strategy_result['action'] == 'skip':
            print(f"⏸️ 跳过投注: {strategy_result['reason']}")
            print(f"💡 建议: {strategy_result['recommendation']}")
            return
        
        # 执行投注
        allocation = strategy_result['allocation']
        total_amount = strategy_result['total_amount']
        
        print(f"\n💰 新策略投注:")
        print(f"   策略类型: {strategy_result['strategy_type']}")
        print(f"   风险等级: {strategy_result['risk_level']}")
        print(f"   期望收益: {strategy_result['expected_profit']:+.3f}元")
        print(f"   投注分配: {allocation}")
        print(f"   总投注额: {total_amount:.2f}元")
        
        # 记录房间选择
        available_rooms = [i for i in range(1, 9) if i != prediction['room']]
        selected_rooms = list(allocation.keys())

        # 收集详细的房间选择信息
        selection_details = {
            'available_rooms': available_rooms,
            'strategy': strategy_result['strategy_type'],
            'room_frequencies': self.calculate_room_frequencies(),
            'reason': f"{strategy_result['strategy_type']} - {strategy_result['risk_level']}风险",
            'allocation': allocation,
            'expected_profit': strategy_result['expected_profit']
        }

        log_room_selection(
            self.current_issue,
            available_rooms,
            selected_rooms[0] if len(selected_rooms) == 1 else selected_rooms,
            strategy_result['strategy_type'],
            {
                'allocation': allocation,
                'risk_level': strategy_result['risk_level'],
                'expected_profit': strategy_result['expected_profit']
            }
        )
        
        # 执行智能投注 (支持自定义金额)
        bet_results = []
        total_bet_amount = 0

        # 由于API限制，allocation只包含一个房间
        room, target_amount = list(allocation.items())[0]

        # 使用智能投注处理器
        print(f"💰 智能投注房间{room}: 目标金额{target_amount:.2f}元")

        # 获取最优金额调整建议
        optimal_amount, adjustment_note = self.smart_betting_handler.get_optimal_amount_adjustment(target_amount)
        print(f"📊 金额调整: {adjustment_note} (实际投注{optimal_amount:.2f}元)")

        # 执行智能投注
        smart_bet_result = self.smart_betting_handler.execute_smart_bet(room, target_amount)

        if smart_bet_result.success:
            print(f"✅ 房间{room}智能投注成功")
            print(f"   成功投注: {len(smart_bet_result.successful_bets)}次")
            print(f"   实际金额: {smart_bet_result.total_amount:.2f}元")

            bet_results.append({
                'room': room,
                'target_amount': target_amount,
                'actual_amount': smart_bet_result.total_amount,
                'success': True,
                'bet_count': len(smart_bet_result.successful_bets),
                'details': smart_bet_result.successful_bets
            })
            total_bet_amount = smart_bet_result.total_amount

            # 记录成功投注 (使用实际金额)
            log_betting(self.current_issue, room, smart_bet_result.total_amount, True)

            # 记录到实时日志
            self.log_betting_record(self.current_issue, prediction, room, smart_bet_result.total_amount,
                                   selection_details=selection_details)

        else:
            print(f"❌ 房间{room}智能投注失败: {smart_bet_result.message}")
            print(f"   失败投注: {len(smart_bet_result.failed_bets)}次")

            bet_results.append({
                'room': room,
                'target_amount': target_amount,
                'actual_amount': smart_bet_result.total_amount,
                'success': False,
                'error': smart_bet_result.message,
                'failed_details': smart_bet_result.failed_bets
            })
            total_bet_amount = smart_bet_result.total_amount

            # 记录失败投注
            log_betting(self.current_issue, room, target_amount, False)
        
        # 更新统计
        if any(result['success'] for result in bet_results):
            self.session_stats['total_bets'] += 1
            
        print(f"📊 投注完成: 总投注{total_bet_amount:.2f}元")
        
        # 存储投注信息用于后续结果分析
        self.current_bet_info = {
            'issue': self.current_issue,
            'prediction': prediction,
            'allocation': allocation,
            'total_amount': total_bet_amount,
            'strategy_result': strategy_result,
            'bet_results': bet_results,
            'selection_details': selection_details
        }
    
    def analyze_betting_result(self, state: GameState):
        """分析投注结果"""
        
        if not hasattr(self, 'current_bet_info') or not self.current_bet_info:
            print("📊 本期未投注，无需分析")
            return
        
        bet_info = self.current_bet_info
        if bet_info['issue'] != state.issue:
            print("📊 期号不匹配，无需分析")
            return
        
        actual_room = state.kill_number
        allocation = bet_info['allocation']
        total_bet = bet_info['total_amount']
        
        print(f"\n📊 新策略结果分析:")
        print(f"   预测房间: {bet_info['prediction']['room']}")
        print(f"   开奖房间: {actual_room}")
        print(f"   投注分配: {allocation}")
        print(f"   总投注额: {total_bet:.2f}元")
        
        # 计算实际盈亏 (单房间投注)
        room, amount = list(allocation.items())[0]

        if room != actual_room:
            # 获胜：投注房间不是开奖房间
            profit = amount * 0.1  # 10%收益
            total_profit = profit
            result_detail = f"房间{room}获胜(+{profit:.2f}元)"
        else:
            # 失败：投注房间是开奖房间
            loss = amount
            total_profit = -loss
            result_detail = f"房间{room}失败(-{loss:.2f}元)"
        
        # 判断投注结果
        if total_profit > 0:
            result = "获胜"
            self.session_stats['total_wins'] += 1
            self.session_stats['current_consecutive_losses'] = 0
        else:
            result = "失败"
            self.session_stats['current_consecutive_losses'] += 1
            self.session_stats['max_consecutive_losses'] = max(
                self.session_stats['max_consecutive_losses'],
                self.session_stats['current_consecutive_losses']
            )

        self.session_stats['total_profit'] += total_profit

        print(f"🎯 投注结果: {result}")
        print(f"📊 详情: {result_detail}")
        print(f"💰 本期盈亏: {total_profit:+.2f}元")
        
        # 更新策略表现
        self.profitable_strategy.update_performance({
            'result': result,
            'profit': total_profit if total_profit > 0 else 0,
            'loss': abs(total_profit) if total_profit < 0 else 0
        })
        
        # 计算统计信息
        win_rate = (self.session_stats['total_wins'] / self.session_stats['total_bets'] * 100) if self.session_stats['total_bets'] > 0 else 0
        
        print(f"📈 会话统计:")
        print(f"   总投注: {self.session_stats['total_bets']}次")
        print(f"   获胜: {self.session_stats['total_wins']}次")
        print(f"   胜率: {win_rate:.1f}%")
        print(f"   总盈亏: {self.session_stats['total_profit']:+.2f}元")
        print(f"   连败: {self.session_stats['current_consecutive_losses']}次")
        print(f"   最大连败: {self.session_stats['max_consecutive_losses']}次")
        
        # 记录结果
        log_result(state.issue, actual_room, result, total_profit)

        # 更新实时记录
        if hasattr(self, 'log_filename') and self.log_filename:
            # 重建选择详情
            selection_details = bet_info.get('selection_details', {
                'available_rooms': [i for i in range(1, 9) if i != bet_info['prediction']['room']],
                'strategy': bet_info.get('strategy_result', {}).get('strategy_type', '未知策略'),
                'room_frequencies': self.calculate_room_frequencies(),
                'reason': '投注策略执行',
                'allocation': allocation,
                'expected_profit': bet_info.get('strategy_result', {}).get('expected_profit', 0.0)
            })

            self.log_betting_record(
                state.issue,
                bet_info['prediction'],
                room,
                amount,
                actual_room,
                total_profit,
                selection_details
            )

        # 清除当前投注信息
        self.current_bet_info = None
    
    def process_game_state(self, state: GameState):
        """处理游戏状态"""
        
        if state.state == 1:  # 等待开奖 - 投注时机
            self.handle_betting_opportunity(state)
        elif state.state == 2:  # 已开奖 - 分析结果
            self.handle_lottery_result(state)
    
    def handle_betting_opportunity(self, state: GameState):
        """处理投注时机"""
        
        print(f"\n🎯 投注时机: 期号{state.issue}, 倒计时{state.countdown}秒")
        
        self.current_issue = state.issue
        
        # 投注时机判断
        if not (15 <= state.countdown <= 30):
            print(f"⏰ 投注时机不佳，倒计时{state.countdown}秒")
            return
        
        # 获取预测
        prediction = self.get_prediction()
        if not prediction:
            print("📊 无可用预测，跳过投注")
            return
        
        # 执行新投注策略
        self.execute_new_betting_strategy(prediction)
    
    def handle_lottery_result(self, state: GameState):
        """处理开奖结果"""
        
        print(f"\n🎯 开奖结果: 期号{state.issue}, 开出房间{state.kill_number}")
        
        # 分析投注结果
        self.analyze_betting_result(state)
        
        # 更新历史数据
        if state.kill_number > 0:
            # 避免重复添加
            if not self.history or state.issue != getattr(self, 'last_issue', 0):
                self.history.append(state.kill_number)
                self.last_issue = state.issue
                self.save_history_data()
                print(f"📊 历史数据更新: 共{len(self.history)}期")

                # 重新生成动态规则 (每10期重新生成)
                if len(self.history) % 10 == 0:
                    self.generate_dynamic_rules()
    
    def save_history_data(self):
        """保存历史数据"""
        try:
            data = {
                'history': self.history,
                'last_updated': datetime.now().isoformat(),
                'total_periods': len(self.history)
            }
            
            with open("game_history.json", 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ 保存历史数据失败: {e}")
    
    def start_monitoring(self):
        """启动新盈利系统"""
        print("\n🚀 启动新盈利投注系统...")
        print("💡 核心改进:")
        print("   1. 动态投注金额调整")
        print("   2. 智能单房间选择")
        print("   3. 高置信度风险控制")
        print("   4. 时间段风险管理")
        print("   5. 连败保护机制")
        
        self.is_running = True
        self.monitor.start_monitoring(self.process_game_state)

def main():
    """主函数"""
    
    print("🎯 新盈利投注系统")
    print("=" * 50)
    print("基于历史数据分析，解决高胜率低盈利问题")
    print()
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    # 系统配置
    config = {
        'base_bet_amount': 1.0,
        'max_bet_amount': 10.0,
        'profit_target': 50.0,
        'stop_loss': -20.0
    }
    
    try:
        # 创建API客户端
        api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
        
        # 创建新盈利系统
        system = NewProfitableSystem(api_client, config)
        
        response = input("是否启动新盈利投注系统？(yes/no): ").lower().strip()
        
        if response in ['yes', 'y', '是']:
            system.start_monitoring()
        else:
            print("系统未启动")
            
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
