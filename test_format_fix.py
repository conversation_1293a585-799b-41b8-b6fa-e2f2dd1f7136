#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试格式化错误修复
"""

import os
import json
from datetime import datetime
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem
from real_time_logger import log_betting, log_result

def test_format_fix():
    """测试格式化错误修复"""
    
    print("🧪 测试格式化错误修复")
    print("=" * 60)
    
    # 创建模拟API客户端
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return None
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 创建系统实例
    api_client = MockAPIClient()
    config = {
        'base_bet_amount': 2,
        'max_bet_amount': 20,
        'min_bet_amount': 1,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50,
        'initial_balance': 200,
        'auto_report_interval': 10,
        'risk_monitoring': True,
        'real_time_logging': True
    }
    
    print("🎯 创建系统实例...")
    system = OptimizedRandomBettingSystem(api_client, config)
    
    # 设置测试状态
    print("\n📊 设置测试状态...")
    system.consecutive_wins = 3
    system.consecutive_losses = 0
    system.total_bets = 15
    system.total_wins = 14  # 93.3%胜率，触发算法奖励
    
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   总投注: {system.total_bets}次")
    print(f"   总获胜: {system.total_wins}次")
    print(f"   胜率: {system.total_wins/system.total_bets*100:.1f}%")
    
    print("\n" + "=" * 60)
    
    # 测试动态金额计算
    print("💰 测试动态金额计算...")
    
    amount, calculation = system.calculate_enhanced_dynamic_amount()
    
    print(f"\n📊 计算结果:")
    print(f"   最终金额: {amount}元")
    print(f"   计算详情类型: {type(calculation)}")
    
    # 检查计算详情中是否有元组
    for key, value in calculation.items():
        print(f"   {key}: {value} (类型: {type(value).__name__})")
    
    print("\n" + "=" * 60)
    
    # 测试投注记录 - 直接调用log_betting
    print("🎲 测试投注记录...")
    
    current_issue = 130555
    bet_room = 5
    bet_amount = amount
    
    print(f"\n📍 测试期号{current_issue}投注记录...")
    
    try:
        # 直接调用log_betting函数，传递计算详情
        log_betting(current_issue, bet_room, bet_amount, True, calculation)
        print(f"✅ log_betting调用成功")
        
        # 模拟开奖结果
        winning_room = 1
        result = "获胜" if bet_room != winning_room else "失败"
        profit = bet_amount * 0.1 if result == "获胜" else -bet_amount
        
        print(f"   开奖房间: {winning_room}")
        print(f"   投注结果: {result}")
        print(f"   盈亏: {profit:+.2f}元")
        
        # 记录开奖结果
        log_result(current_issue, winning_room, result, profit)
        print(f"✅ log_result调用成功")
        
    except Exception as e:
        print(f"❌ 记录调用失败: {e}")
        print(f"   错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    
    # 检查生成的日志文件
    print("📂 检查生成的日志文件...")
    
    # 查找最新的投注日志文件
    log_files = []
    for filename in os.listdir('.'):
        if filename.startswith('betting_log_') and filename.endswith('.md'):
            log_files.append(filename)
    
    if log_files:
        latest_log = sorted(log_files)[-1]
        print(f"✅ 找到日志文件: {latest_log}")
        
        # 读取并检查内容
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 日志文件内容长度: {len(content)}字符")
            
            # 检查是否包含动态计算详情
            if "动态计算" in content:
                print(f"✅ 包含动态计算详情")
            else:
                print(f"⚠️ 未包含动态计算详情")
            
            # 检查是否包含各种奖励信息
            checks = [
                ("基础金额", "基础金额"),
                ("连胜奖励", "连胜奖励"),
                ("算法奖励", "算法奖励"),
                ("风险调整", "风险调整"),
                ("计算公式", "计算公式")
            ]
            
            for check_name, check_text in checks:
                if check_text in content:
                    print(f"✅ 包含{check_name}")
                else:
                    print(f"⚠️ 未包含{check_name}")
            
            # 显示相关部分内容
            lines = content.split('\n')
            in_lcg_section = False
            lcg_lines = []
            
            for line in lines:
                if "LCG算法选择详情" in line:
                    in_lcg_section = True
                    lcg_lines.append(line)
                elif in_lcg_section:
                    lcg_lines.append(line)
                    if len(lcg_lines) > 20:  # 限制显示行数
                        break
            
            if lcg_lines:
                print(f"\n📋 LCG算法选择详情部分:")
                for line in lcg_lines[:15]:  # 显示前15行
                    print(f"   {line}")
                if len(lcg_lines) > 15:
                    print(f"   ... (还有{len(lcg_lines)-15}行)")
            
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
            return False
    else:
        print("❌ 未找到日志文件")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试第二次投注 - 连败场景
    print("🔄 测试第二次投注 - 连败场景...")
    
    # 设置连败状态
    system.consecutive_wins = 0
    system.consecutive_losses = 2
    
    current_issue = 130556
    
    print(f"\n📍 第{current_issue}期投注 (连败2次)...")
    
    try:
        # 计算动态金额
        amount2, calculation2 = system.calculate_enhanced_dynamic_amount()
        
        print(f"   计算金额: {amount2}元")
        
        # 记录投注
        log_betting(current_issue, 6, amount2, True, calculation2)
        
        # 模拟开奖 - 这次获胜
        log_result(current_issue, 1, "获胜", amount2 * 0.1)
        
        print(f"✅ 第二次投注记录成功")
        
    except Exception as e:
        print(f"❌ 第二次投注记录失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("🎯 测试完成!")
    print("✅ 格式化错误修复测试通过")
    print("✅ 动态金额计算详情正确记录")
    print("✅ Markdown文件生成无错误")
    
    return True

if __name__ == "__main__":
    success = test_format_fix()
    if success:
        print("\n🎉 所有测试通过！格式化错误已修复。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
