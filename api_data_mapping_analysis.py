#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据映射分析
分析API中的strokeId与我们研究数据的对应关系
"""

import json
from typing import Dict, List

def analyze_api_structure():
    """分析API结构"""
    print("=== API结构分析 ===")
    
    # 从API文档中提取的关键信息
    api_info = {
        "stroke_mapping": {
            1: {"name": "一", "icon": "icon-1.png"},
            2: {"name": "丨", "icon": "icon-2.png"}, 
            3: {"name": "丿", "icon": "icon-3.png"},
            4: {"name": "丶", "icon": "icon-4.png"},
            5: {"name": "ㄥ", "icon": "icon-5.png"}
        },
        "game_info": {
            "total_strokes": 5,  # 只有5个选择，不是8个
            "kill_number": 6,    # 但开奖结果是6？
            "room_number": 7,    # 房间7
            "issue": 123153      # 期号
        }
    }
    
    print("API中的笔画映射:")
    for stroke_id, info in api_info["stroke_mapping"].items():
        print(f"  strokeId {stroke_id}: {info['name']}")
    
    print(f"\n游戏信息:")
    print(f"  总笔画数: {api_info['game_info']['total_strokes']}")
    print(f"  开奖数字: {api_info['game_info']['kill_number']}")
    print(f"  房间号: {api_info['game_info']['room_number']}")
    
    return api_info

def analyze_data_inconsistency():
    """分析数据不一致性"""
    print(f"\n=== 数据不一致性分析 ===")
    
    inconsistencies = []
    
    # 问题1: 数字范围不匹配
    inconsistencies.append({
        "issue": "数字范围不匹配",
        "description": "API显示strokeId只有1-5，但开奖结果是6，我们的数据是1-8",
        "severity": "高",
        "impact": "预测模型可能不适用"
    })
    
    # 问题2: 开奖数字超出范围
    inconsistencies.append({
        "issue": "开奖数字超出strokeId范围", 
        "description": "killNumber=6，但strokeId最大只到5",
        "severity": "高",
        "impact": "需要理解真实的数字映射关系"
    })
    
    # 问题3: 房间系统
    inconsistencies.append({
        "issue": "多房间系统",
        "description": "有8个房间，每个房间可能有独立的随机数生成器",
        "severity": "中",
        "impact": "需要确认数据来源房间"
    })
    
    print("发现的不一致性:")
    for i, issue in enumerate(inconsistencies, 1):
        print(f"\n{i}. {issue['issue']} (严重性: {issue['severity']})")
        print(f"   描述: {issue['description']}")
        print(f"   影响: {issue['impact']}")
    
    return inconsistencies

def propose_mapping_hypotheses():
    """提出映射假设"""
    print(f"\n=== 映射关系假设 ===")
    
    hypotheses = []
    
    # 假设1: killNumber是房间号
    hypotheses.append({
        "name": "假设1: killNumber是房间号",
        "description": "killNumber=6可能表示杀手去了房间6，而不是开出数字6",
        "evidence": "API返回中有'上期杀手去了「折笔画」'的提示",
        "probability": 0.7
    })
    
    # 假设2: 存在隐藏的数字映射
    hypotheses.append({
        "name": "假设2: 存在更复杂的数字系统",
        "description": "可能存在1-8的完整数字系统，但API只显示了部分",
        "evidence": "我们的23607个样本确实是1-8范围",
        "probability": 0.6
    })
    
    # 假设3: 不同游戏模式
    hypotheses.append({
        "name": "假设3: 不同的游戏模式",
        "description": "API可能是不同的游戏模式，与我们分析的数据不是同一个游戏",
        "evidence": "数字范围完全不匹配",
        "probability": 0.3
    })
    
    print("映射关系假设:")
    for i, hyp in enumerate(hypotheses, 1):
        print(f"\n{i}. {hyp['name']} (可能性: {hyp['probability']:.1%})")
        print(f"   描述: {hyp['description']}")
        print(f"   证据: {hyp['evidence']}")
    
    return hypotheses

def generate_verification_plan():
    """生成验证计划"""
    print(f"\n=== 验证计划 ===")
    
    verification_steps = [
        {
            "step": 1,
            "action": "调用游戏状态API",
            "purpose": "获取当前完整的游戏状态信息",
            "expected": "确认真实的数字范围和映射关系"
        },
        {
            "step": 2, 
            "action": "分析历史开奖数据",
            "purpose": "收集多期开奖结果，确认数字范围",
            "expected": "验证是否真的只有1-5，还是有1-8"
        },
        {
            "step": 3,
            "action": "测试不同房间",
            "purpose": "检查不同房间的开奖规律",
            "expected": "确认房间间的独立性"
        },
        {
            "step": 4,
            "action": "小额测试投注",
            "purpose": "验证投注逻辑和获胜条件",
            "expected": "确认游戏规则的理解是否正确"
        }
    ]
    
    print("验证步骤:")
    for step in verification_steps:
        print(f"\n步骤 {step['step']}: {step['action']}")
        print(f"  目的: {step['purpose']}")
        print(f"  预期: {step['expected']}")
    
    return verification_steps

def create_api_test_framework():
    """创建API测试框架"""
    print(f"\n=== API测试框架设计 ===")
    
    framework = {
        "authentication": {
            "required_headers": [
                "User-Agent", "packageId", "version", "channel", 
                "androidId", "userId", "token", "ts", "sign"
            ],
            "note": "需要有效的token和签名算法"
        },
        "endpoints": {
            "get_game_state": {
                "url": "/v11/api/stroke/data",
                "method": "POST",
                "purpose": "获取游戏状态和开奖结果"
            },
            "place_bet": {
                "url": "/v11/api/stroke/buy", 
                "method": "POST",
                "params": "roomNumber, costMedal",
                "purpose": "投注"
            }
        },
        "data_collection": {
            "target": "连续收集100期开奖数据",
            "fields": ["issue", "killNumber", "roomNumber", "state"],
            "purpose": "验证数字范围和分布"
        }
    }
    
    print("API测试框架:")
    print(f"认证要求: {len(framework['authentication']['required_headers'])} 个必需头部")
    print(f"端点数量: {len(framework['endpoints'])} 个")
    print(f"数据收集目标: {framework['data_collection']['target']}")
    
    return framework

def main():
    """主函数"""
    print("开始API数据映射分析...")
    
    # 1. 分析API结构
    api_info = analyze_api_structure()
    
    # 2. 分析不一致性
    inconsistencies = analyze_data_inconsistency()
    
    # 3. 提出假设
    hypotheses = propose_mapping_hypotheses()
    
    # 4. 生成验证计划
    verification_plan = generate_verification_plan()
    
    # 5. 创建测试框架
    test_framework = create_api_test_framework()
    
    print(f"\n=== 总结和建议 ===")
    print("关键发现:")
    print("1. API显示的数字系统(1-5)与我们的分析数据(1-8)不匹配")
    print("2. 开奖结果(killNumber=6)超出了strokeId范围")
    print("3. 存在多房间系统，可能影响随机数生成")
    
    print(f"\n立即行动建议:")
    print("1. 🔍 首先验证数字映射关系 - 这是最关键的问题")
    print("2. 📊 收集实际的API开奖数据进行对比")
    print("3. 🧪 进行小额测试验证游戏规则理解")
    print("4. ⚠️  在确认映射关系前，暂停大额投注")
    
    return {
        "api_info": api_info,
        "inconsistencies": inconsistencies, 
        "hypotheses": hypotheses,
        "verification_plan": verification_plan,
        "test_framework": test_framework
    }

if __name__ == "__main__":
    results = main()
    
    print(f"\n💡 下一步行动:")
    print("需要您提供以下信息来解决映射问题:")
    print("1. 23607个样本数据的具体来源（哪个房间？哪个时间段？）")
    print("2. 是否可以提供更多的API调用示例？")
    print("3. 游戏界面截图，确认数字显示方式")
    print("4. 确认游戏规则：是否真的是选择数字≠开出数字获胜？")
