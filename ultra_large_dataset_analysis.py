#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超大数据集分析 (23607个样本)
高精度随机数算法逆向分析
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import time
import math

class UltraLargeDatasetAnalyzer:
    def __init__(self, sequence: List[int]):
        """初始化超大数据集分析器"""
        self.sequence = sequence
        self.n = len(sequence)
        print(f"超大数据集大小: {self.n} 个样本")
        
    def comprehensive_statistics(self) -> Dict:
        """全面统计分析"""
        print("=== 全面统计分析 ===")
        
        # 基础统计
        seq_array = np.array(self.sequence)
        stats = {
            'count': self.n,
            'mean': float(np.mean(seq_array)),
            'std': float(np.std(seq_array)),
            'min': int(np.min(seq_array)),
            'max': int(np.max(seq_array)),
            'median': float(np.median(seq_array))
        }
        
        print(f"基础统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 频率分布
        counter = Counter(self.sequence)
        expected_freq = self.n / 8
        chi_square = 0
        
        print(f"\n频率分布 (期望: {expected_freq:.1f}):")
        for i in range(1, 9):
            count = counter.get(i, 0)
            freq = count / self.n
            deviation = count - expected_freq
            chi_square += deviation ** 2 / expected_freq
            print(f"  数字 {i}: {count:5d} 次 ({freq:.4f}, 偏差: {deviation:+6.1f})")
        
        print(f"\n卡方统计量: {chi_square:.3f}")
        print(f"随机性评估: {'优秀' if chi_square < 7 else '良好' if chi_square < 14 else '一般'}")
        
        return {
            'basic_stats': stats,
            'frequency_distribution': dict(counter),
            'chi_square': chi_square,
            'randomness_quality': 'excellent' if chi_square < 7 else 'good' if chi_square < 14 else 'fair'
        }
    
    def advanced_pattern_mining(self) -> Dict:
        """高级模式挖掘"""
        print(f"\n=== 高级模式挖掘 ===")
        
        all_rules = {}
        total_rules = 0
        
        # 挖掘1-6元条件规则
        for condition_length in range(1, 7):
            print(f"\n挖掘 {condition_length} 元条件规则:")
            
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            condition_positions = defaultdict(list)
            
            # 统计条件出现情况
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
                condition_positions[condition].append(i)
            
            # 提取高质量规则
            rules = []
            confidence_thresholds = {
                1: 0.15, 2: 0.20, 3: 0.30, 4: 0.40, 5: 0.50, 6: 0.60
            }
            min_support = max(3, self.n // 10000)  # 动态调整最小支持度
            
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    # 找出最可能的下一个值
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    # 计算信息增益
                    entropy_before = 3.0  # log2(8)
                    probs = [count/total for count in next_counts.values()]
                    entropy_after = -sum(p * math.log2(p) for p in probs if p > 0)
                    information_gain = entropy_before - entropy_after
                    
                    # 计算分布均匀性
                    positions = condition_positions[condition]
                    position_variance = np.var(positions) if len(positions) > 1 else 0
                    distribution_score = 1 / (1 + position_variance / 1000000)
                    
                    if confidence >= confidence_thresholds[condition_length]:
                        rules.append({
                            'condition': condition,
                            'next_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'information_gain': information_gain,
                            'distribution_score': distribution_score,
                            'length': condition_length,
                            'all_outcomes': dict(next_counts)
                        })
            
            # 按置信度排序
            rules.sort(key=lambda x: x['confidence'], reverse=True)
            
            print(f"  发现 {len(rules)} 个高质量规则")
            if rules:
                print(f"  最佳规则 (前5个):")
                for rule in rules[:5]:
                    print(f"    {rule['condition']} -> {rule['next_value']} "
                          f"(置信度: {rule['confidence']:.3f}, 支持度: {rule['support']}, "
                          f"信息增益: {rule['information_gain']:.3f})")
            
            all_rules[condition_length] = rules
            total_rules += len(rules)
        
        print(f"\n总共挖掘出 {total_rules} 个高质量规则")
        return all_rules
    
    def time_series_analysis(self) -> Dict:
        """时间序列分析"""
        print(f"\n=== 时间序列分析 ===")
        
        # 分段分析
        segment_size = 1000
        segments = []
        
        for start in range(0, self.n - segment_size, segment_size):
            segment = self.sequence[start:start + segment_size]
            
            # 计算段内统计
            segment_stats = {
                'start': start,
                'mean': np.mean(segment),
                'std': np.std(segment),
                'entropy': self.calculate_entropy(segment)
            }
            
            # 计算段内最强规则
            condition_stats = defaultdict(lambda: defaultdict(int))
            for i in range(3, len(segment)):
                condition = tuple(segment[i-3:i])
                next_val = segment[i]
                condition_stats[condition][next_val] += 1
            
            max_confidence = 0
            strong_rules = 0
            for condition, next_counts in condition_stats.items():
                total = sum(next_counts.values())
                if total >= 3:
                    confidence = max(next_counts.values()) / total
                    max_confidence = max(max_confidence, confidence)
                    if confidence >= 0.6:
                        strong_rules += 1
            
            segment_stats['max_confidence'] = max_confidence
            segment_stats['strong_rules'] = strong_rules
            segments.append(segment_stats)
        
        # 寻找异常段
        confidences = [s['max_confidence'] for s in segments]
        mean_confidence = np.mean(confidences)
        std_confidence = np.std(confidences)
        
        anomalous_segments = []
        for segment in segments:
            if segment['max_confidence'] > mean_confidence + 2 * std_confidence:
                anomalous_segments.append(segment)
        
        print(f"分析了 {len(segments)} 个时间段")
        print(f"平均最大置信度: {mean_confidence:.3f}")
        print(f"发现 {len(anomalous_segments)} 个异常段 (置信度异常高)")
        
        if anomalous_segments:
            print("异常段详情:")
            for segment in anomalous_segments[:5]:
                print(f"  位置 {segment['start']}-{segment['start']+1000}: "
                      f"最大置信度 {segment['max_confidence']:.3f}, "
                      f"强规则数 {segment['strong_rules']}")
        
        return {
            'segments': segments,
            'anomalous_segments': anomalous_segments,
            'mean_confidence': mean_confidence,
            'std_confidence': std_confidence
        }
    
    def calculate_entropy(self, sequence: List[int]) -> float:
        """计算序列熵"""
        counter = Counter(sequence)
        total = len(sequence)
        entropy = -sum((count/total) * math.log2(count/total) 
                      for count in counter.values() if count > 0)
        return entropy
    
    def cross_validation_system(self, rules: Dict, test_ratio: float = 0.1) -> Dict:
        """交叉验证系统"""
        print(f"\n=== 交叉验证系统 (测试比例: {test_ratio:.1%}) ===")
        
        # 分割数据
        test_size = int(self.n * test_ratio)
        train_end = self.n - test_size
        
        print(f"训练集: {train_end} 个样本")
        print(f"测试集: {test_size} 个样本")
        
        # 合并所有规则并排序
        all_rules = []
        for length, rule_list in rules.items():
            for rule in rule_list:
                # 计算综合得分
                rule['score'] = (
                    rule['confidence'] * 0.4 +
                    min(rule['information_gain'] / 3, 1) * 0.3 +
                    rule['distribution_score'] * 0.2 +
                    min(rule['support'] / 100, 1) * 0.1
                )
                all_rules.append(rule)
        
        all_rules.sort(key=lambda x: x['score'], reverse=True)
        
        # 验证预测
        correct_predictions = 0
        total_predictions = 0
        rule_performance = defaultdict(lambda: {'correct': 0, 'total': 0})
        
        for i in range(train_end + 6, self.n):
            predicted = None
            used_rule = None
            
            # 尝试应用规则
            for rule in all_rules:
                condition_length = rule['length']
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        used_rule = rule
                        break
            
            if predicted is not None:
                actual = self.sequence[i]
                rule_id = str(used_rule['condition'])
                
                rule_performance[rule_id]['total'] += 1
                if predicted == actual:
                    correct_predictions += 1
                    rule_performance[rule_id]['correct'] += 1
                
                total_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        print(f"验证准确率: {accuracy:.4f} ({correct_predictions}/{total_predictions})")
        
        # 分析最佳规则
        best_rules = []
        for rule_id, perf in rule_performance.items():
            if perf['total'] >= 5:
                rule_accuracy = perf['correct'] / perf['total']
                if rule_accuracy >= 0.7:
                    best_rules.append((rule_id, rule_accuracy, perf['correct'], perf['total']))
        
        best_rules.sort(key=lambda x: (x[1], x[3]), reverse=True)
        
        print(f"\n表现最佳的规则 (准确率≥70%, 使用≥5次):")
        for rule_id, acc, correct, total in best_rules[:10]:
            print(f"  {rule_id}: {acc:.3f} ({correct}/{total})")
        
        return {
            'accuracy': accuracy,
            'total_predictions': total_predictions,
            'best_rules': best_rules,
            'rule_performance': dict(rule_performance)
        }
    
    def predict_future_ultra(self, rules: Dict, validation: Dict, count: int = 50) -> List[Dict]:
        """超大数据集未来预测"""
        print(f"\n=== 预测未来 {count} 个值 ===")
        
        # 选择最佳规则
        best_rule_ids = set(rule_id for rule_id, _, _, _ in validation['best_rules'])
        
        optimized_rules = []
        for length, rule_list in rules.items():
            for rule in rule_list:
                rule_id = str(rule['condition'])
                if rule_id in best_rule_ids:
                    perf = validation['rule_performance'][rule_id]
                    rule['validated_accuracy'] = perf['correct'] / perf['total']
                    optimized_rules.append(rule)
        
        optimized_rules.sort(key=lambda x: x['validated_accuracy'], reverse=True)
        
        # 预测
        predictions = []
        current_sequence = self.sequence.copy()
        
        for step in range(count):
            predicted = None
            confidence = 0
            used_rule = None
            
            # 尝试应用最佳规则
            for rule in optimized_rules:
                condition_length = rule['length']
                if len(current_sequence) >= condition_length:
                    current_condition = tuple(current_sequence[-condition_length:])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        confidence = rule['validated_accuracy']
                        used_rule = rule
                        break
            
            if predicted is None:
                # 智能回退策略
                recent_window = current_sequence[-200:]
                counter = Counter(recent_window)
                predicted = counter.most_common(1)[0][0]
                confidence = 0.15
                used_rule = {'condition': 'frequency_fallback'}
            
            prediction_info = {
                'step': step + 1,
                'predicted_value': predicted,
                'confidence': confidence,
                'rule_condition': used_rule['condition'],
                'rule_length': used_rule.get('length', 0)
            }
            
            predictions.append(prediction_info)
            current_sequence.append(predicted)
            
            if step < 20:
                print(f"  步骤 {step+1}: 预测 {predicted} "
                      f"(置信度: {confidence:.3f}, 规则长度: {prediction_info['rule_length']})")
        
        return predictions
    
    def run_ultra_analysis(self) -> Dict:
        """运行超大数据集分析"""
        print("开始超大数据集分析...")
        print(f"数据集大小: {self.n}")
        print(f"数据前20个: {self.sequence[:20]}")
        print()
        
        start_time = time.time()
        
        # 1. 全面统计分析
        stats = self.comprehensive_statistics()
        
        # 2. 高级模式挖掘
        rules = self.advanced_pattern_mining()
        
        # 3. 时间序列分析
        time_analysis = self.time_series_analysis()
        
        # 4. 交叉验证
        validation = self.cross_validation_system(rules)
        
        # 5. 未来预测
        predictions = self.predict_future_ultra(rules, validation, 50)
        
        elapsed_time = time.time() - start_time
        
        return {
            'statistics': stats,
            'rules': rules,
            'time_analysis': time_analysis,
            'validation': validation,
            'predictions': predictions,
            'analysis_time': elapsed_time
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机23607.txt")
    analyzer = UltraLargeDatasetAnalyzer(sequence)
    results = analyzer.run_ultra_analysis()
    
    print(f"\n=== 超大数据集分析总结 ===")
    print(f"随机性质量: {results['statistics']['randomness_quality']}")
    print(f"验证准确率: {results['validation']['accuracy']:.4f}")
    print(f"最佳规则数量: {len(results['validation']['best_rules'])}")
    print(f"异常时间段: {len(results['time_analysis']['anomalous_segments'])} 个")
    print(f"分析耗时: {results['analysis_time']:.2f} 秒")
    
    # 提取预测值
    predicted_values = [p['predicted_value'] for p in results['predictions']]
    print(f"\n预测的未来50个值: {predicted_values}")
    
    # 统计高置信度预测
    high_conf_predictions = [p for p in results['predictions'] if p['confidence'] >= 0.7]
    print(f"\n高置信度预测 (≥70%): {len(high_conf_predictions)} 个")
    
    for pred in high_conf_predictions[:10]:
        print(f"  步骤 {pred['step']}: {pred['predicted_value']} "
              f"(置信度: {pred['confidence']:.3f}, 规则长度: {pred['rule_length']})")
    
    if results['validation']['accuracy'] > 0.5:
        print(f"\n🎯 超大数据集分析取得重大突破！")
        print(f"   准确率: {results['validation']['accuracy']:.1%}")
        print(f"   发现了可利用的模式！")
    elif results['validation']['accuracy'] > 0.3:
        print(f"\n✅ 发现了一定的可预测性")
        print(f"   准确率: {results['validation']['accuracy']:.1%}")
    else:
        print(f"\n📊 数据显示高随机性特征")
        print(f"   准确率: {results['validation']['accuracy']:.1%}")
    
    print(f"\n💡 基于23607个样本的分析为随机数算法逆向提供了最可靠的结果！")
