#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试收益计算修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from optimized_random_betting_system import OptimizedRandomBettingSystem

def test_profit_calculation_fix():
    """测试收益计算修复"""
    
    print("🧪 测试收益计算修复")
    print("=" * 60)
    
    # 创建模拟API客户端
    class MockAPIClient:
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
            return MockResult()

    # 创建系统配置
    config = {
        'base_bet_amount': 2.0,
        'max_bet_amount': 10.0,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50.0,
        'initial_balance': 200.0
    }

    # 创建系统实例
    system = OptimizedRandomBettingSystem(MockAPIClient(), config)
    
    print(f"📊 初始状态:")
    print(f"   余额: {system.current_balance:.2f}元")
    print(f"   总盈亏: {system.total_profit:.2f}元")
    
    # 模拟一次投注
    issue = 130940
    bet_room = 6
    bet_amount = 8.0
    winning_room = 4
    
    # 添加投注记录
    bet_info = {
        'issue': issue,
        'room': bet_room,
        'amount': bet_amount,
        'timestamp': 1234567890,
        'strategy': 'LCG算法'
    }
    system.bet_history.append(bet_info)
    system.total_bets += 1
    
    print(f"\n🎯 模拟投注:")
    print(f"   期号: {issue}")
    print(f"   投注房间: {bet_room}")
    print(f"   投注金额: {bet_amount:.2f}元")
    print(f"   开奖房间: {winning_room}")
    print(f"   结果: {'获胜' if bet_room != winning_room else '失败'}")
    
    # 测试1: 使用固定收益计算 (模拟模式)
    print(f"\n🧮 测试1: 模拟模式收益计算")
    system_copy1 = OptimizedRandomBettingSystem(MockAPIClient(), config)
    system_copy1.bet_history = system.bet_history.copy()
    system_copy1.total_bets = system.total_bets
    
    # 不传递actual_profit，使用固定10%计算
    system_copy1.process_result(issue, winning_room)
    
    print(f"   计算方式: 固定10%收益")
    print(f"   计算收益: {bet_amount * 0.1:.2f}元")
    print(f"   系统余额: {system_copy1.current_balance:.2f}元")
    print(f"   系统总盈亏: {system_copy1.total_profit:.2f}元")
    
    # 测试2: 使用API真实收益 (真实模式)
    print(f"\n🧮 测试2: 真实模式收益计算")
    system_copy2 = OptimizedRandomBettingSystem(MockAPIClient(), config)
    system_copy2.bet_history = system.bet_history.copy()
    system_copy2.total_bets = system.total_bets
    
    # 传递API真实收益
    api_profit = 1.58  # 用户报告的真实收益
    system_copy2.process_result(issue, winning_room, api_profit)
    
    print(f"   计算方式: API真实收益")
    print(f"   API收益: {api_profit:.2f}元")
    print(f"   系统余额: {system_copy2.current_balance:.2f}元")
    print(f"   系统总盈亏: {system_copy2.total_profit:.2f}元")
    
    # 对比差异
    print(f"\n📊 收益差异对比:")
    profit_diff = api_profit - (bet_amount * 0.1)
    balance_diff = system_copy2.current_balance - system_copy1.current_balance
    
    print(f"   收益差异: {profit_diff:+.2f}元")
    print(f"   余额差异: {balance_diff:+.2f}元")
    print(f"   收益率差异: {(api_profit/bet_amount - 0.1)*100:+.1f}%")
    
    # 测试3: 连续多次投注的累积效应
    print(f"\n🧮 测试3: 累积效应测试")
    
    # 模拟5次获胜投注
    test_data = [
        {'issue': 130941, 'bet_amount': 8.0, 'api_profit': 1.45},
        {'issue': 130942, 'bet_amount': 7.0, 'api_profit': 1.23},
        {'issue': 130943, 'bet_amount': 6.0, 'api_profit': 0.98},
        {'issue': 130944, 'bet_amount': 5.0, 'api_profit': 0.87},
        {'issue': 130945, 'bet_amount': 4.0, 'api_profit': 0.65},
    ]
    
    # 模拟模式累积
    sim_system = OptimizedRandomBettingSystem(MockAPIClient(), config)
    # 真实模式累积
    real_system = OptimizedRandomBettingSystem(MockAPIClient(), config)
    
    for data in test_data:
        # 添加投注记录
        for sys in [sim_system, real_system]:
            bet_info = {
                'issue': data['issue'],
                'room': 6,
                'amount': data['bet_amount'],
                'timestamp': 1234567890,
                'strategy': 'LCG算法'
            }
            sys.bet_history.append(bet_info)
            sys.total_bets += 1
        
        # 处理结果
        sim_system.process_result(data['issue'], 4)  # 模拟模式
        real_system.process_result(data['issue'], 4, data['api_profit'])  # 真实模式
    
    print(f"   模拟模式累积盈亏: {sim_system.total_profit:+.2f}元")
    print(f"   真实模式累积盈亏: {real_system.total_profit:+.2f}元")
    print(f"   累积差异: {real_system.total_profit - sim_system.total_profit:+.2f}元")
    
    # 计算理论差异
    sim_total = sum(data['bet_amount'] * 0.1 for data in test_data)
    real_total = sum(data['api_profit'] for data in test_data)
    theoretical_diff = real_total - sim_total
    
    print(f"   理论差异: {theoretical_diff:+.2f}元")
    print(f"   实际差异: {real_system.total_profit - sim_system.total_profit:+.2f}元")
    
    print(f"\n✅ 收益计算修复测试完成!")
    print(f"💡 现在系统可以正确使用API真实收益进行计算")
    print(f"💡 模拟模式仍使用固定10%收益率")
    print(f"💡 真实模式使用API返回的准确收益")

if __name__ == "__main__":
    test_profit_calculation_fix()
