#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
伪随机数生成器算法识别
测试常见的PRNG算法，尝试匹配观察到的序列
"""

import numpy as np
from typing import List, Tuple, Optional
import itertools

class PRNGIdentifier:
    def __init__(self, target_sequence: List[int]):
        """初始化PRNG识别器"""
        self.target_sequence = target_sequence
        self.n = len(target_sequence)
        
    def lcg_generator(self, seed: int, a: int, c: int, m: int, count: int) -> List[int]:
        """线性同余生成器 (LCG): X_{n+1} = (a * X_n + c) mod m"""
        sequence = []
        x = seed
        for _ in range(count):
            x = (a * x + c) % m
            # 将结果映射到1-8范围
            mapped = (x % 8) + 1
            sequence.append(mapped)
        return sequence
    
    def test_common_lcg_parameters(self) -> List[Tuple[int, int, int, int, float]]:
        """测试常见的LCG参数组合"""
        # 常见的LCG参数 (a, c, m)
        common_params = [
            # C标准库常用参数
            (1103515245, 12345, 2**31),
            (214013, 2531011, 2**32),
            (1664525, 1013904223, 2**32),
            # 其他常见参数
            (16807, 0, 2**31 - 1),  # Park and Miller
            (48271, 0, 2**31 - 1),  # Park and <PERSON> improved
            (69621, 0, 2**32),
            (1103515245, 12345, 2**32),
            # 小模数参数（适合游戏）
            (17, 5, 256),
            (25, 7, 256),
            (37, 3, 256),
            (41, 7, 256),
            (13, 11, 64),
            (21, 5, 64),
            (29, 3, 128),
            (45, 21, 128),
        ]
        
        results = []
        print("测试常见LCG参数...")
        
        for a, c, m in common_params:
            best_match = 0
            best_seed = 0
            
            # 测试不同的种子值
            seed_range = min(1000, m // 10) if m > 1000 else m
            for seed in range(1, seed_range, max(1, seed_range // 100)):
                try:
                    generated = self.lcg_generator(seed, a, c, m, self.n)
                    matches = sum(1 for i in range(self.n) 
                                if generated[i] == self.target_sequence[i])
                    match_rate = matches / self.n
                    
                    if match_rate > best_match:
                        best_match = match_rate
                        best_seed = seed
                        
                except (OverflowError, ZeroDivisionError):
                    continue
            
            if best_match > 0.1:  # 只记录匹配率超过10%的结果
                results.append((a, c, m, best_seed, best_match))
                print(f"  LCG({a}, {c}, {m}), seed={best_seed}: {best_match:.3f}")
        
        return sorted(results, key=lambda x: x[4], reverse=True)
    
    def test_simple_transformations(self) -> List[Tuple[str, float]]:
        """测试简单的数学变换"""
        results = []
        
        # 测试是否是某种简单的数学序列
        print("\n测试简单变换...")
        
        # 测试线性变换
        for i in range(1, min(10, self.n)):
            # 检查是否存在 x[n] = (a * n + b) mod 8 + 1 的模式
            for a in range(1, 20):
                for b in range(0, 20):
                    generated = [((a * j + b) % 8) + 1 for j in range(self.n)]
                    matches = sum(1 for k in range(self.n) 
                                if generated[k] == self.target_sequence[k])
                    match_rate = matches / self.n
                    
                    if match_rate > 0.3:
                        results.append((f"Linear: ({a}*n + {b}) mod 8 + 1", match_rate))
                        print(f"  线性变换 ({a}*n + {b}) mod 8 + 1: {match_rate:.3f}")
        
        # 测试基于前一个数的简单变换
        for offset in range(1, 8):
            generated = [self.target_sequence[0]]  # 第一个数保持不变
            for i in range(1, self.n):
                next_val = ((self.target_sequence[i-1] + offset - 1) % 8) + 1
                generated.append(next_val)
            
            matches = sum(1 for k in range(self.n) 
                        if generated[k] == self.target_sequence[k])
            match_rate = matches / self.n
            
            if match_rate > 0.3:
                results.append((f"Previous + {offset}", match_rate))
                print(f"  前数+{offset}: {match_rate:.3f}")
        
        return sorted(results, key=lambda x: x[1], reverse=True)
    
    def analyze_differences(self) -> dict:
        """分析相邻数字的差值模式"""
        print("\n分析相邻数字差值...")
        
        differences = []
        for i in range(1, self.n):
            diff = self.target_sequence[i] - self.target_sequence[i-1]
            differences.append(diff)
        
        # 统计差值分布
        from collections import Counter
        diff_counter = Counter(differences)
        
        print("差值分布:")
        for diff in sorted(diff_counter.keys()):
            count = diff_counter[diff]
            print(f"  差值 {diff:2d}: 出现 {count:2d} 次 ({count/len(differences):.3f})")
        
        # 检查差值是否有周期性
        print("\n差值序列:", differences[:20], "..." if len(differences) > 20 else "")
        
        return {
            'differences': differences,
            'distribution': dict(diff_counter)
        }
    
    def run_identification(self) -> dict:
        """运行PRNG识别分析"""
        print("=== PRNG算法识别分析 ===")
        print(f"目标序列: {self.target_sequence}")
        print()
        
        # 测试LCG参数
        lcg_results = self.test_common_lcg_parameters()
        
        # 测试简单变换
        transform_results = self.test_simple_transformations()
        
        # 分析差值模式
        diff_analysis = self.analyze_differences()
        
        return {
            'lcg_results': lcg_results,
            'transform_results': transform_results,
            'difference_analysis': diff_analysis
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机生成1-8.txt")
    identifier = PRNGIdentifier(sequence)
    results = identifier.run_identification()
    
    print("\n=== 总结 ===")
    if results['lcg_results']:
        print("最佳LCG匹配:")
        for i, (a, c, m, seed, rate) in enumerate(results['lcg_results'][:3]):
            print(f"  {i+1}. LCG({a}, {c}, {m}), seed={seed}, 匹配率={rate:.3f}")
    
    if results['transform_results']:
        print("最佳变换匹配:")
        for i, (transform, rate) in enumerate(results['transform_results'][:3]):
            print(f"  {i+1}. {transform}, 匹配率={rate:.3f}")
