#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模型融合系统
结合条件概率、机器学习和时间序列分析的融合预测系统
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
from sklearn.ensemble import RandomForestRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class MultiModelFusionSystem:
    def __init__(self, sequence: List[int]):
        """初始化多模型融合系统"""
        self.sequence = sequence
        self.n = len(sequence)
        self.models = {}
        
    def build_conditional_model(self) -> Dict:
        """构建优化的条件概率模型"""
        print("=== 构建条件概率模型 ===")
        
        # 只使用最有效的3-4元条件
        best_rules = []
        
        for condition_length in [3, 4]:
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= 3:  # 至少出现3次
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    if confidence >= 0.6:  # 高置信度规则
                        best_rules.append({
                            'condition': condition,
                            'next_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length
                        })
        
        # 按置信度和长度排序
        best_rules.sort(key=lambda x: (x['length'], x['confidence']), reverse=True)
        
        print(f"选择了 {len(best_rules)} 个高质量规则")
        
        return {'rules': best_rules}
    
    def build_ml_model(self, window_size: int = 8) -> Dict:
        """构建机器学习模型"""
        print("=== 构建机器学习模型 ===")
        
        # 创建特征
        features = []
        labels = []
        
        for i in range(window_size, self.n):
            feature = self.sequence[i-window_size:i]
            # 添加统计特征
            feature_extended = feature + [
                np.mean(feature),
                np.std(feature),
                np.max(feature) - np.min(feature),  # 范围
                len(set(feature)),  # 唯一值数量
            ]
            features.append(feature_extended)
            labels.append(self.sequence[i])
        
        X = np.array(features)
        y = np.array(labels)
        
        # 分割数据
        split_point = int(len(X) * 0.8)
        X_train, X_test = X[:split_point], X[split_point:]
        y_train, y_test = y[:split_point], y[split_point:]
        
        # 训练多个模型
        models = {}
        
        # 随机森林
        rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
        rf_model.fit(X_train, y_train)
        rf_pred = rf_model.predict(X_test)
        rf_accuracy = np.mean(np.round(np.clip(rf_pred, 1, 8)) == y_test)
        models['random_forest'] = {'model': rf_model, 'accuracy': rf_accuracy}
        
        # 神经网络
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        nn_model = MLPRegressor(hidden_layer_sizes=(50, 25), max_iter=1000, random_state=42)
        nn_model.fit(X_train_scaled, y_train)
        nn_pred = nn_model.predict(X_test_scaled)
        nn_accuracy = np.mean(np.round(np.clip(nn_pred, 1, 8)) == y_test)
        models['neural_network'] = {'model': nn_model, 'accuracy': nn_accuracy, 'scaler': scaler}
        
        print(f"随机森林准确率: {rf_accuracy:.4f}")
        print(f"神经网络准确率: {nn_accuracy:.4f}")
        
        return {
            'models': models,
            'window_size': window_size,
            'X_test': X_test,
            'y_test': y_test
        }
    
    def build_frequency_model(self) -> Dict:
        """构建频率模型"""
        print("=== 构建频率模型 ===")
        
        # 分析不同位置的频率模式
        position_patterns = {}
        
        # 按位置模8分组
        for pos_mod in range(8):
            values_at_position = []
            for i in range(pos_mod, self.n, 8):
                values_at_position.append(self.sequence[i])
            
            if values_at_position:
                counter = Counter(values_at_position)
                most_common = counter.most_common(1)[0]
                position_patterns[pos_mod] = {
                    'most_common_value': most_common[0],
                    'frequency': most_common[1] / len(values_at_position),
                    'distribution': dict(counter)
                }
        
        # 分析最近趋势
        recent_window = 50
        recent_sequence = self.sequence[-recent_window:] if self.n >= recent_window else self.sequence
        recent_counter = Counter(recent_sequence)
        
        return {
            'position_patterns': position_patterns,
            'recent_distribution': dict(recent_counter),
            'recent_most_common': recent_counter.most_common(1)[0][0]
        }
    
    def fusion_predict(self, conditional_model: Dict, ml_model: Dict, 
                      frequency_model: Dict, position: int) -> Dict:
        """融合预测"""
        predictions = {}
        confidences = {}
        
        # 1. 条件概率预测
        conditional_pred = None
        conditional_conf = 0
        
        for rule in conditional_model['rules']:
            condition_length = rule['length']
            if len(self.sequence) >= condition_length:
                current_condition = tuple(self.sequence[-condition_length:])
                if current_condition == rule['condition']:
                    conditional_pred = rule['next_value']
                    conditional_conf = rule['confidence']
                    break
        
        if conditional_pred:
            predictions['conditional'] = conditional_pred
            confidences['conditional'] = conditional_conf
        
        # 2. 机器学习预测
        window_size = ml_model['window_size']
        if len(self.sequence) >= window_size:
            last_window = self.sequence[-window_size:]
            feature = last_window + [
                np.mean(last_window),
                np.std(last_window),
                np.max(last_window) - np.min(last_window),
                len(set(last_window)),
            ]
            
            # 随机森林预测
            rf_pred = ml_model['models']['random_forest']['model'].predict([feature])[0]
            rf_pred_rounded = int(np.round(np.clip(rf_pred, 1, 8)))
            predictions['random_forest'] = rf_pred_rounded
            confidences['random_forest'] = ml_model['models']['random_forest']['accuracy']
            
            # 神经网络预测
            feature_scaled = ml_model['models']['neural_network']['scaler'].transform([feature])
            nn_pred = ml_model['models']['neural_network']['model'].predict(feature_scaled)[0]
            nn_pred_rounded = int(np.round(np.clip(nn_pred, 1, 8)))
            predictions['neural_network'] = nn_pred_rounded
            confidences['neural_network'] = ml_model['models']['neural_network']['accuracy']
        
        # 3. 频率模型预测
        pos_mod = position % 8
        if pos_mod in frequency_model['position_patterns']:
            freq_pred = frequency_model['position_patterns'][pos_mod]['most_common_value']
            freq_conf = frequency_model['position_patterns'][pos_mod]['frequency']
            predictions['frequency'] = freq_pred
            confidences['frequency'] = freq_conf
        
        # 4. 最近趋势预测
        predictions['recent_trend'] = frequency_model['recent_most_common']
        confidences['recent_trend'] = 0.2  # 低置信度
        
        return predictions, confidences
    
    def weighted_fusion(self, predictions: Dict, confidences: Dict) -> Tuple[int, float]:
        """加权融合预测结果"""
        if not predictions:
            return Counter(self.sequence).most_common(1)[0][0], 0.1
        
        # 计算加权投票
        vote_scores = defaultdict(float)
        total_weight = 0
        
        for model_name, pred_value in predictions.items():
            weight = confidences.get(model_name, 0.1)
            vote_scores[pred_value] += weight
            total_weight += weight
        
        # 选择得分最高的预测
        best_prediction = max(vote_scores.keys(), key=lambda k: vote_scores[k])
        best_confidence = vote_scores[best_prediction] / total_weight if total_weight > 0 else 0
        
        return best_prediction, best_confidence
    
    def cross_validate_system(self, test_size: int = 200) -> Dict:
        """交叉验证系统性能"""
        print(f"=== 交叉验证 (测试最后 {test_size} 个数据点) ===")
        
        if self.n <= test_size:
            print("数据量不足进行交叉验证")
            return {'accuracy': 0, 'predictions': []}
        
        # 使用前面的数据训练，后面的数据测试
        train_sequence = self.sequence[:-test_size]
        test_sequence = self.sequence[-test_size:]
        
        # 临时修改序列进行训练
        original_sequence = self.sequence
        self.sequence = train_sequence
        self.n = len(train_sequence)
        
        # 构建模型
        conditional_model = self.build_conditional_model()
        ml_model = self.build_ml_model()
        frequency_model = self.build_frequency_model()
        
        # 恢复原序列
        self.sequence = original_sequence
        self.n = len(original_sequence)
        
        # 进行预测
        correct_predictions = 0
        prediction_details = []
        
        current_sequence = train_sequence.copy()
        
        for i, actual_value in enumerate(test_sequence):
            # 临时更新序列用于预测
            temp_sequence = self.sequence
            self.sequence = current_sequence
            
            predictions, confidences = self.fusion_predict(
                conditional_model, ml_model, frequency_model, len(current_sequence)
            )
            
            predicted_value, prediction_confidence = self.weighted_fusion(predictions, confidences)
            
            # 恢复序列
            self.sequence = temp_sequence
            
            is_correct = (predicted_value == actual_value)
            if is_correct:
                correct_predictions += 1
            
            prediction_details.append({
                'position': len(train_sequence) + i,
                'predicted': predicted_value,
                'actual': actual_value,
                'correct': is_correct,
                'confidence': prediction_confidence,
                'contributing_models': list(predictions.keys())
            })
            
            # 更新当前序列
            current_sequence.append(actual_value)
        
        accuracy = correct_predictions / test_size
        print(f"交叉验证准确率: {accuracy:.4f} ({correct_predictions}/{test_size})")
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': test_size,
            'prediction_details': prediction_details[-20:]  # 最后20个预测详情
        }
    
    def predict_future_fusion(self, count: int = 20) -> List[Dict]:
        """融合预测未来值"""
        print(f"=== 融合预测未来 {count} 个值 ===")
        
        # 构建所有模型
        conditional_model = self.build_conditional_model()
        ml_model = self.build_ml_model()
        frequency_model = self.build_frequency_model()
        
        predictions = []
        current_sequence = self.sequence.copy()
        
        for step in range(count):
            # 临时更新序列
            temp_sequence = self.sequence
            self.sequence = current_sequence
            
            step_predictions, step_confidences = self.fusion_predict(
                conditional_model, ml_model, frequency_model, len(current_sequence)
            )
            
            final_prediction, final_confidence = self.weighted_fusion(step_predictions, step_confidences)
            
            # 恢复序列
            self.sequence = temp_sequence
            
            prediction_info = {
                'step': step + 1,
                'predicted_value': final_prediction,
                'confidence': final_confidence,
                'contributing_models': step_predictions,
                'model_confidences': step_confidences
            }
            
            predictions.append(prediction_info)
            current_sequence.append(final_prediction)
            
            print(f"  步骤 {step+1}: 预测 {final_prediction} "
                  f"(置信度: {final_confidence:.3f}, "
                  f"模型: {list(step_predictions.keys())})")
        
        return predictions
    
    def run_fusion_system(self) -> Dict:
        """运行融合系统"""
        print("开始多模型融合系统...")
        print(f"序列长度: {self.n}")
        print()
        
        # 交叉验证
        cv_results = self.cross_validate_system()
        
        # 未来预测
        future_predictions = self.predict_future_fusion(25)
        
        return {
            'cross_validation': cv_results,
            'future_predictions': future_predictions
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    system = MultiModelFusionSystem(sequence)
    results = system.run_fusion_system()
    
    print(f"\n=== 多模型融合系统总结 ===")
    print(f"交叉验证准确率: {results['cross_validation']['accuracy']:.4f}")
    
    # 提取预测值
    predicted_values = [p['predicted_value'] for p in results['future_predictions']]
    print(f"预测的未来25个值: {predicted_values}")
    
    # 统计高置信度预测
    high_conf_predictions = [p for p in results['future_predictions'] if p['confidence'] > 0.5]
    print(f"高置信度预测 (>0.5): {len(high_conf_predictions)} 个")
    
    if results['cross_validation']['accuracy'] > 0.75:
        print(f"\n🎯 融合系统表现优秀！准确率超过75%")
    elif results['cross_validation']['accuracy'] > 0.65:
        print(f"\n✅ 融合系统表现良好！准确率超过65%")
    else:
        print(f"\n📊 当前准确率: {results['cross_validation']['accuracy']:.1%}")
