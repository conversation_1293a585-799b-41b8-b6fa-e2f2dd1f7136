#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LCG真实投注系统演示脚本
展示如何使用真实投注功能
"""

import time
from datetime import datetime
from lcg_betting_system_main import LCGBettingSystemMain

def demo_real_betting_features():
    """演示真实投注功能"""
    
    print("🎯 LCG真实投注系统功能演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 创建主程序实例
    print("1️⃣ 创建LCG投注系统实例...")
    main_system = LCGBettingSystemMain()
    print("   ✅ 系统实例创建成功")
    print()
    
    # 演示模拟模式
    print("2️⃣ 演示模拟投注模式...")
    print("   🎮 初始化模拟模式...")
    
    mock_system = main_system.initialize_system(real_betting=False)
    print("   ✅ 模拟模式初始化成功")
    
    # 执行几次模拟投注
    print("   🎯 执行模拟投注演示...")
    for i in range(3):
        issue = 124000 + i
        print(f"\n   --- 第{issue}期模拟投注 ---")
        main_system.execute_single_bet(issue)
        time.sleep(0.5)  # 短暂延迟
    
    print("\n   📊 模拟投注演示完成")
    print()
    
    # 演示真实模式初始化
    print("3️⃣ 演示真实投注模式初始化...")
    print("   ⚠️ 注意: 这只是初始化演示，不会执行真实投注")
    
    try:
        print("   🎯 初始化真实投注模式...")
        real_system = main_system.initialize_system(real_betting=True)
        
        if real_system and main_system.real_betting_mode:
            print("   ✅ 真实投注模式初始化成功")
            print("   🔧 真实投注组件状态:")
            print(f"      - 预测适配器: {'✅' if main_system.prediction_adapter else '❌'}")
            print(f"      - 盈利策略: {'✅' if main_system.profitable_strategy else '❌'}")
            print(f"      - 智能投注处理器: {'✅' if main_system.smart_betting_handler else '❌'}")
            
            # 演示API客户端功能
            print("\n   📡 测试API客户端连接...")
            try:
                game_state = main_system.api_client.get_game_state()
                if game_state:
                    print(f"   ✅ API连接成功: 期号{game_state.issue}")
                else:
                    print("   ⚠️ API连接失败 (可能是认证问题)")
            except Exception as e:
                print(f"   ⚠️ API连接异常: {e}")
            
            # 演示智能投注处理器
            if main_system.smart_betting_handler:
                print("\n   🤖 演示智能投注处理器...")
                test_amounts = [1.5, 3.7, 8.2]
                
                for amount in test_amounts:
                    breakdown = main_system.smart_betting_handler.calculate_bet_breakdown(amount)
                    optimal, note = main_system.smart_betting_handler.get_optimal_amount_adjustment(amount)
                    print(f"      {amount}元 -> 分解: {breakdown}, 调整: {optimal}元 ({note})")
            
        else:
            print("   ⚠️ 真实投注模式初始化失败")
            
    except Exception as e:
        print(f"   ❌ 真实投注模式演示失败: {e}")
    
    print()
    
    # 演示系统配置
    print("4️⃣ 演示系统配置...")
    config = main_system.get_default_config()
    print("   📋 默认配置参数:")
    for key, value in config.items():
        print(f"      {key}: {value}")
    print()
    
    # 演示LCG算法
    print("5️⃣ 演示LCG算法特性...")
    if mock_system:
        print("   🎲 LCG房间选择演示:")
        rooms = []
        for i in range(10):
            room = mock_system.select_optimal_random_room()
            rooms.append(room)
        
        print(f"      生成序列: {rooms}")
        
        # 统计分布
        from collections import Counter
        distribution = Counter(rooms)
        print("      分布统计:")
        for room in range(1, 9):
            count = distribution.get(room, 0)
            percentage = count / len(rooms) * 100
            print(f"        房间{room}: {count}次 ({percentage:.1f}%)")
    
    print()
    
    # 演示报告功能
    print("6️⃣ 演示报告生成功能...")
    if mock_system:
        print("   📝 生成演示报告...")
        report_file = mock_system.generate_session_report()
        if report_file:
            print(f"   ✅ 报告生成成功: {report_file}")
        else:
            print("   ⚠️ 报告生成失败")
    
    print()
    
    # 总结
    print("7️⃣ 功能演示总结")
    print("   ✅ 模拟投注模式: 正常工作")
    print("   ✅ 真实投注模式: 初始化成功")
    print("   ✅ LCG算法: 房间选择正常")
    print("   ✅ 智能投注处理器: 金额分解正常")
    print("   ✅ 报告生成: 功能正常")
    print()
    
    print("🎉 LCG真实投注系统功能演示完成!")
    print()
    print("💡 使用建议:")
    print("   1. 新用户建议先使用模拟模式熟悉系统")
    print("   2. 真实投注前请仔细阅读使用指南")
    print("   3. 建议从小额投注开始测试")
    print("   4. 严格遵守风险控制设置")
    print("   5. 定期检查投注记录和报告")

def interactive_demo():
    """交互式演示"""
    
    print("\n🎯 交互式演示模式")
    print("您可以选择以下演示选项:")
    print("1. 完整功能演示")
    print("2. 仅模拟投注演示")
    print("3. 仅真实模式初始化演示")
    print("4. LCG算法演示")
    print("5. 退出")
    
    while True:
        try:
            choice = input("\n请选择演示选项 (1-5): ").strip()
            
            if choice == '1':
                demo_real_betting_features()
                break
            elif choice == '2':
                demo_simulation_only()
                break
            elif choice == '3':
                demo_real_mode_init()
                break
            elif choice == '4':
                demo_lcg_algorithm()
                break
            elif choice == '5':
                print("👋 退出演示")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 演示被中断")
            break
        except Exception as e:
            print(f"❌ 演示发生错误: {e}")

def demo_simulation_only():
    """仅演示模拟投注"""
    
    print("\n🎮 模拟投注演示")
    print("-" * 40)
    
    main_system = LCGBettingSystemMain()
    system = main_system.initialize_system(real_betting=False)
    
    print("\n执行5期模拟投注...")
    for i in range(5):
        issue = 124000 + i
        print(f"\n--- 第{issue}期 ---")
        main_system.execute_single_bet(issue)
        time.sleep(0.3)
    
    print("\n📊 生成演示报告...")
    system.generate_session_report()
    print("✅ 模拟投注演示完成")

def demo_real_mode_init():
    """仅演示真实模式初始化"""
    
    print("\n🎯 真实模式初始化演示")
    print("-" * 40)
    
    main_system = LCGBettingSystemMain()
    
    print("⚠️ 注意: 这只是初始化演示，不会执行真实投注")
    system = main_system.initialize_system(real_betting=True)
    
    if system and main_system.real_betting_mode:
        print("✅ 真实模式初始化成功")
        
        # 测试组件
        print("\n🔧 测试各组件...")
        if main_system.prediction_adapter:
            print("   ✅ 预测适配器正常")
        if main_system.profitable_strategy:
            print("   ✅ 盈利策略引擎正常")
        if main_system.smart_betting_handler:
            print("   ✅ 智能投注处理器正常")
            
            # 演示金额处理
            print("\n   💰 金额处理演示:")
            test_amounts = [2.3, 5.7, 12.8]
            for amount in test_amounts:
                breakdown = main_system.smart_betting_handler.calculate_bet_breakdown(amount)
                print(f"      {amount}元 -> {breakdown}")
    else:
        print("❌ 真实模式初始化失败")

def demo_lcg_algorithm():
    """演示LCG算法"""
    
    print("\n🧮 LCG算法演示")
    print("-" * 40)
    
    main_system = LCGBettingSystemMain()
    system = main_system.initialize_system(real_betting=False)
    
    print("🎲 生成50个房间选择...")
    rooms = []
    for i in range(50):
        room = system.select_optimal_random_room()
        rooms.append(room)
    
    print(f"生成序列: {rooms}")
    
    # 统计分析
    from collections import Counter
    distribution = Counter(rooms)
    
    print("\n📊 分布分析:")
    print("房间 | 次数 | 比例  | 理论比例")
    print("-" * 35)
    for room in range(1, 9):
        count = distribution.get(room, 0)
        percentage = count / len(rooms) * 100
        theoretical = 12.5  # 理论均匀分布
        print(f"  {room}  |  {count:2d}  | {percentage:4.1f}% |  {theoretical:4.1f}%")
    
    print(f"\n✅ LCG算法演示完成")
    print(f"   总生成: {len(rooms)}个")
    print(f"   范围: {min(rooms)}-{max(rooms)}")
    print(f"   分布: 接近理论均匀分布")

def main():
    """主函数"""
    
    print("🎯 LCG真实投注系统演示")
    print("=" * 60)
    
    try:
        # 检查是否为交互模式
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
            interactive_demo()
        else:
            demo_real_betting_features()
            
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🎯 演示结束")

if __name__ == "__main__":
    main()
