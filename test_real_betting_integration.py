#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LCG真实投注系统集成测试
验证真实投注功能的集成和兼容性
"""

import sys
import time
from datetime import datetime

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心模块
        from lcg_betting_system_main import LCGBettingSystemMain
        print("   ✅ LCG主程序模块导入成功")
        
        from optimized_random_betting_system import OptimizedRandomBettingSystem
        print("   ✅ 优化随机投注系统导入成功")
        
        from api_framework import GameAPIClient, GameMonitor, GameState
        print("   ✅ API框架模块导入成功")
        
        # 测试真实投注模块
        try:
            from prediction_strategy_adapter import PredictionRuleAdapter
            from profitable_betting_strategy import ProfitableBettingStrategy
            from smart_betting_handler import SmartBettingHandler
            from real_time_logger import log_prediction, log_room_selection, log_betting, log_result
            print("   ✅ 真实投注模块导入成功")
            real_betting_available = True
        except ImportError as e:
            print(f"   ⚠️ 真实投注模块导入失败: {e}")
            real_betting_available = False
        
        return True, real_betting_available
        
    except ImportError as e:
        print(f"   ❌ 核心模块导入失败: {e}")
        return False, False

def test_system_initialization():
    """测试系统初始化"""
    print("\n🚀 测试系统初始化...")
    
    try:
        from lcg_betting_system_main import LCGBettingSystemMain
        
        # 创建主程序实例
        main_system = LCGBettingSystemMain()
        print("   ✅ 主程序实例创建成功")
        
        # 测试模拟模式初始化
        print("   🎮 测试模拟模式初始化...")
        system = main_system.initialize_system(real_betting=False)
        if system:
            print("   ✅ 模拟模式初始化成功")
        else:
            print("   ❌ 模拟模式初始化失败")
            return False
        
        # 测试真实模式初始化 (如果可用)
        try:
            print("   🎯 测试真实模式初始化...")
            real_system = main_system.initialize_system(real_betting=True)
            if real_system:
                print("   ✅ 真实模式初始化成功")
            else:
                print("   ⚠️ 真实模式初始化失败 (可能是组件缺失)")
        except Exception as e:
            print(f"   ⚠️ 真实模式初始化异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 系统初始化测试失败: {e}")
        return False

def test_lcg_algorithm():
    """测试LCG算法"""
    print("\n🧮 测试LCG算法...")
    
    try:
        from optimized_random_betting_system import OptimizedRandomBettingSystem
        from api_framework import GameAPIClient
        
        # 创建模拟API客户端
        api_client = GameAPIClient("http://mock", {})
        
        # 创建系统实例
        config = {
            'base_bet_amount': 1.0,
            'max_bet_amount': 10.0,
            'max_consecutive_losses': 5,
            'max_daily_loss': 20.0,
            'stop_loss_percentage': 0.3,
            'initial_balance': 100.0
        }
        
        system = OptimizedRandomBettingSystem(api_client, config)
        print("   ✅ LCG系统创建成功")
        
        # 测试房间选择
        rooms = []
        for i in range(10):
            room = system.select_optimal_random_room()
            rooms.append(room)
        
        print(f"   🎲 LCG房间选择测试: {rooms}")
        
        # 验证房间范围
        if all(1 <= room <= 8 for room in rooms):
            print("   ✅ 房间选择范围正确 (1-8)")
        else:
            print("   ❌ 房间选择范围错误")
            return False
        
        # 测试金额计算
        amount = system.calculate_enhanced_dynamic_amount()
        print(f"   💰 动态金额计算: {amount:.2f}元")
        
        if amount > 0:
            print("   ✅ 金额计算正常")
        else:
            print("   ❌ 金额计算异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ LCG算法测试失败: {e}")
        return False

def test_api_client():
    """测试API客户端"""
    print("\n📡 测试API客户端...")
    
    try:
        from api_framework import GameAPIClient, GameState
        
        # 创建真实API客户端配置
        api_config = {
            'base_url': 'https://fks-api.lucklyworld.com',
            'headers': {
                'User-Agent': 'com.caike.union/5.2.2-official',
                'userId': '8607652',
                'token': 'test_token',
                'ts': str(int(time.time() * 1000))
            }
        }
        
        client = GameAPIClient(api_config['base_url'], api_config['headers'])
        print("   ✅ API客户端创建成功")
        
        # 测试游戏状态获取 (可能会失败，这是正常的)
        try:
            state = client.get_game_state()
            if state:
                print(f"   ✅ 游戏状态获取成功: 期号{state.issue}")
            else:
                print("   ⚠️ 游戏状态获取失败 (可能是网络或认证问题)")
        except Exception as e:
            print(f"   ⚠️ 游戏状态获取异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API客户端测试失败: {e}")
        return False

def test_smart_betting_handler():
    """测试智能投注处理器"""
    print("\n🤖 测试智能投注处理器...")
    
    try:
        from smart_betting_handler import SmartBettingHandler
        from api_framework import GameAPIClient
        
        # 创建模拟API客户端
        api_client = GameAPIClient("http://mock", {})
        
        # 创建智能投注处理器
        handler = SmartBettingHandler(api_client)
        print("   ✅ 智能投注处理器创建成功")
        
        # 测试金额分解
        test_amounts = [1.0, 2.5, 5.0, 10.0, 15.5]
        
        for amount in test_amounts:
            breakdown = handler.calculate_bet_breakdown(amount)
            print(f"   💰 {amount}元分解: {breakdown}")
        
        # 测试最优金额调整
        optimal_amount, note = handler.get_optimal_amount_adjustment(7.3)
        print(f"   📊 7.3元调整: {optimal_amount}元 ({note})")
        
        return True
        
    except ImportError:
        print("   ⚠️ 智能投注处理器模块未找到")
        return True  # 不是致命错误
    except Exception as e:
        print(f"   ❌ 智能投注处理器测试失败: {e}")
        return False

def test_integration():
    """集成测试"""
    print("\n🔗 集成测试...")
    
    try:
        from lcg_betting_system_main import LCGBettingSystemMain
        
        # 创建主程序
        main_system = LCGBettingSystemMain()
        
        # 初始化模拟模式
        system = main_system.initialize_system(real_betting=False)
        
        # 测试单次投注流程
        print("   🎯 测试单次投注流程...")
        
        # 模拟投注
        issue = 124000
        bet_info = system.execute_optimized_bet(issue)
        
        if bet_info:
            print(f"   ✅ 投注信息生成成功: 房间{bet_info['room']}, 金额{bet_info['amount']:.2f}元")
            
            # 模拟开奖
            winning_room = 5
            system.process_result(issue, winning_room)
            print(f"   ✅ 开奖结果处理成功: 房间{winning_room}")
            
        else:
            print("   ⚠️ 投注被风控阻止")
        
        # 测试报告生成
        print("   📝 测试报告生成...")
        report_file = system.generate_session_report()
        if report_file:
            print(f"   ✅ 报告生成成功: {report_file}")
        else:
            print("   ⚠️ 报告生成失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 LCG真实投注系统集成测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = []
    
    # 执行测试
    tests = [
        ("模块导入测试", test_imports),
        ("系统初始化测试", test_system_initialization),
        ("LCG算法测试", test_lcg_algorithm),
        ("API客户端测试", test_api_client),
        ("智能投注处理器测试", test_smart_betting_handler),
        ("集成测试", test_integration)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}发生异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print()
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        if isinstance(result, tuple):
            # 特殊处理导入测试的返回值
            core_success, real_betting_available = result
            if core_success:
                status = "✅ 通过"
                passed += 1
                if real_betting_available:
                    print(f"{status} {test_name} (真实投注功能可用)")
                else:
                    print(f"{status} {test_name} (仅模拟投注功能)")
            else:
                status = "❌ 失败"
                print(f"{status} {test_name}")
        else:
            if result:
                status = "✅ 通过"
                passed += 1
            else:
                status = "❌ 失败"
            print(f"{status} {test_name}")
    
    print()
    print(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过! 系统集成正常")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
        return 1

if __name__ == "__main__":
    sys.exit(main())
