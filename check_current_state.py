#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查当前系统状态
"""

import os
import json
from datetime import datetime
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem

def check_current_state():
    """检查当前系统状态"""
    
    print("🔍 检查当前系统状态")
    print("=" * 50)
    
    # 检查状态文件
    today = datetime.now().strftime('%Y%m%d')
    state_file = f"system_state_{today}.json"
    
    print(f"📂 状态文件: {state_file}")
    
    if os.path.exists(state_file):
        print("✅ 状态文件存在")
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            print("📊 状态文件内容:")
            print(f"   连续失败: {state_data.get('consecutive_losses', 0)}次")
            print(f"   连续获胜: {state_data.get('consecutive_wins', 0)}次")
            print(f"   当前余额: {state_data.get('current_balance', 0):.2f}元")
            print(f"   总盈亏: {state_data.get('total_profit', 0):+.2f}元")
            print(f"   总投注: {state_data.get('total_bets', 0)}次")
            print(f"   总获胜: {state_data.get('total_wins', 0)}次")
            print(f"   最后更新: {state_data.get('last_update', 'N/A')}")
        except Exception as e:
            print(f"❌ 读取状态文件失败: {e}")
    else:
        print("❌ 状态文件不存在")
    
    print("\n" + "=" * 50)
    
    # 创建系统实例测试加载
    print("🎯 创建系统实例测试状态加载...")
    
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return None
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    api_client = MockAPIClient()
    config = {
        'base_bet_amount': 2,
        'max_bet_amount': 20,
        'min_bet_amount': 1,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50,
        'initial_balance': 200,
        'auto_report_interval': 10,
        'risk_monitoring': True,
        'real_time_logging': True
    }
    
    system = OptimizedRandomBettingSystem(api_client, config)
    
    print(f"\n📊 系统加载后的状态:")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   当前余额: {system.current_balance:.2f}元")
    print(f"   总盈亏: {system.total_profit:+.2f}元")
    print(f"   总投注: {system.total_bets}次")
    print(f"   总获胜: {system.total_wins}次")
    
    print("\n" + "=" * 50)
    
    # 测试动态金额计算
    print("💰 测试增强动态金额计算...")
    
    dynamic_amount = system.calculate_enhanced_dynamic_amount()
    
    print(f"\n🎯 计算结果:")
    print(f"   基础金额: {system.base_bet_amount}元")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   最终金额: {dynamic_amount}元")
    
    print("\n" + "=" * 50)
    
    # 分析问题
    print("🔍 问题分析:")
    
    if os.path.exists(state_file):
        with open(state_file, 'r', encoding='utf-8') as f:
            file_state = json.load(f)
        
        file_wins = file_state.get('consecutive_wins', 0)
        file_losses = file_state.get('consecutive_losses', 0)
        
        if system.consecutive_wins == file_wins and system.consecutive_losses == file_losses:
            print("   ✅ 状态加载正常")
            print("   ✅ 连胜连败次数正确")
            
            if system.consecutive_wins > 0:
                print(f"   🎉 当前连胜{system.consecutive_wins}次，动态金额应该有连胜奖励")
            elif system.consecutive_losses > 0:
                print(f"   ⚠️ 当前连败{system.consecutive_losses}次，动态金额应该有马丁格尔调整")
            else:
                print("   📊 当前无连胜连败，显示基础金额")
                
        else:
            print("   ❌ 状态加载异常")
            print(f"   文件中连胜: {file_wins}次，系统中连胜: {system.consecutive_wins}次")
            print(f"   文件中连败: {file_losses}次，系统中连败: {system.consecutive_losses}次")
    else:
        print("   ⚠️ 状态文件不存在，系统使用初始状态")
    
    print("\n🎯 结论:")
    if system.consecutive_wins > 0 or system.consecutive_losses > 0:
        print("   ✅ 状态持久化正常工作")
        print("   ✅ 系统应该显示正确的连胜连败次数")
        print("   💡 如果控制台仍显示0次，可能是显示时机问题")
    else:
        print("   ⚠️ 系统状态为初始状态")
        print("   💡 可能需要等待投注结果处理后才会更新状态")

if __name__ == "__main__":
    check_current_state()
