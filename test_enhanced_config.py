#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强配置后的动态金额效果
"""

from lcg_betting_system_main import LCGBettingSystemMain

def test_enhanced_config():
    """测试增强配置后的效果"""
    
    print("🧪 测试增强配置后的动态金额效果")
    print("=" * 60)
    
    # 创建主系统
    main_system = LCGBettingSystemMain()
    
    # 使用新的默认配置初始化
    system = main_system.initialize_system()
    
    print(f"\n📊 新配置参数:")
    print(f"   基础金额: {system.base_bet_amount}元")
    print(f"   最小金额: {system.min_bet_amount}元")
    print(f"   最大金额: {system.max_bet_amount}元")
    print(f"   初始余额: {system.initial_balance}元")
    
    # 测试不同状态下的动态金额
    test_scenarios = [
        {'name': '初始状态', 'losses': 0, 'wins': 0},
        {'name': '连败1次', 'losses': 1, 'wins': 0},
        {'name': '连败2次', 'losses': 2, 'wins': 0},
        {'name': '连败3次', 'losses': 3, 'wins': 0},
        {'name': '连胜3次', 'losses': 0, 'wins': 3},
        {'name': '连胜5次', 'losses': 0, 'wins': 5},
    ]
    
    print(f"\n🎯 动态金额测试:")
    print("=" * 40)
    
    for scenario in test_scenarios:
        print(f"\n📈 场景: {scenario['name']}")
        
        # 重置系统状态
        system.consecutive_losses = scenario['losses']
        system.consecutive_wins = scenario['wins']
        system.current_balance = system.initial_balance
        
        # 计算动态金额
        amount = system.calculate_enhanced_dynamic_amount()
        
        print(f"   最终投注金额: {amount:.2f}元")
        
        # 测试智能投注处理器的分解
        if hasattr(system, 'smart_betting_handler') and system.smart_betting_handler:
            breakdown = system.smart_betting_handler.calculate_bet_breakdown(amount)
            actual_amount = sum(breakdown)
            print(f"   API分解: {breakdown}")
            print(f"   实际投注: {actual_amount:.2f}元")
            if actual_amount != amount:
                diff = actual_amount - amount
                print(f"   差异: {diff:+.2f}元")

def test_simulation_with_enhanced_config():
    """测试增强配置下的模拟投注"""
    
    print(f"\n🎲 模拟投注测试 (增强配置)")
    print("=" * 40)
    
    # 创建主系统
    main_system = LCGBettingSystemMain()
    
    # 初始化系统
    system = main_system.initialize_system()
    
    print(f"\n开始模拟5次投注...")
    
    for i in range(5):
        print(f"\n--- 第{i+1}次投注 ---")
        
        # 执行投注
        issue = 130000 + i
        bet_info = system.execute_optimized_bet(issue)
        
        if bet_info:
            print(f"✅ 投注成功:")
            print(f"   期号: {bet_info['issue']}")
            print(f"   房间: {bet_info['room']}")
            print(f"   金额: {bet_info['amount']:.2f}元")
            print(f"   策略: {bet_info['strategy']}")
            
            # 模拟开奖结果
            import random
            winning_room = random.randint(1, 8)
            print(f"🎰 模拟开奖: 房间{winning_room}")
            
            # 处理结果
            system.process_result(issue, winning_room)
            
            # 显示当前状态
            print(f"📊 当前状态:")
            print(f"   连败: {system.consecutive_losses}次")
            print(f"   连胜: {system.consecutive_wins}次")
            print(f"   余额: {system.current_balance:.2f}元")
            print(f"   总盈亏: {system.total_profit:+.2f}元")
        else:
            print("❌ 投注被阻止")

if __name__ == "__main__":
    test_enhanced_config()
    test_simulation_with_enhanced_config()
    
    print(f"\n🎉 测试完成!")
    print(f"💡 现在您应该能看到:")
    print(f"   ✅ 增强动态金额的详细计算过程")
    print(f"   ✅ 不同状态下的金额变化")
    print(f"   ✅ 马丁格尔、连胜奖励等机制的实际效果")
    print(f"   ✅ 智能投注处理器的金额分解过程")
