#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对10000样本的自适应分析
基于数据特征动态调整分析策略
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import time

class AdaptiveAnalyzer10K:
    def __init__(self, sequence: List[int]):
        """初始化自适应分析器"""
        self.sequence = sequence
        self.n = len(sequence)
        
    def detect_data_characteristics(self) -> Dict:
        """检测数据特征"""
        print("=== 检测数据特征 ===")
        
        # 1. 随机性强度检测
        counter = Counter(self.sequence)
        expected_freq = self.n / 8
        chi_square = sum((counter.get(i, 0) - expected_freq) ** 2 / expected_freq for i in range(1, 9))
        randomness_strength = "高" if chi_square < 7 else "中" if chi_square < 14 else "低"
        
        print(f"随机性强度: {randomness_strength} (卡方值: {chi_square:.3f})")
        
        # 2. 局部模式检测
        segment_size = 1000
        local_patterns = []
        
        for start in range(0, self.n - segment_size, segment_size):
            segment = self.sequence[start:start + segment_size]
            
            # 检测段内的条件概率
            condition_stats = defaultdict(lambda: defaultdict(int))
            for i in range(2, len(segment)):
                condition = tuple(segment[i-2:i])
                next_val = segment[i]
                condition_stats[condition][next_val] += 1
            
            strong_rules = 0
            total_rules = 0
            for condition, next_counts in condition_stats.items():
                total = sum(next_counts.values())
                if total >= 3:
                    total_rules += 1
                    best_confidence = max(next_counts.values()) / total
                    if best_confidence >= 0.6:
                        strong_rules += 1
            
            pattern_strength = strong_rules / total_rules if total_rules > 0 else 0
            local_patterns.append({
                'start': start,
                'pattern_strength': pattern_strength,
                'total_rules': total_rules
            })
        
        # 3. 时间趋势检测
        time_segments = 10
        segment_length = self.n // time_segments
        trend_analysis = []
        
        for i in range(time_segments):
            start_idx = i * segment_length
            end_idx = min((i + 1) * segment_length, self.n)
            segment = self.sequence[start_idx:end_idx]
            
            segment_mean = np.mean(segment)
            segment_std = np.std(segment)
            
            trend_analysis.append({
                'segment': i,
                'mean': segment_mean,
                'std': segment_std
            })
        
        print(f"检测到 {len([p for p in local_patterns if p['pattern_strength'] > 0.1])} 个有模式的局部段")
        
        return {
            'randomness_strength': randomness_strength,
            'chi_square': chi_square,
            'local_patterns': local_patterns,
            'trend_analysis': trend_analysis
        }
    
    def adaptive_rule_extraction(self, characteristics: Dict) -> Dict:
        """基于数据特征的自适应规则提取"""
        print(f"\n=== 自适应规则提取 ===")
        
        # 根据随机性强度调整策略
        if characteristics['randomness_strength'] == '高':
            print("检测到高随机性，使用弱模式挖掘策略")
            min_confidence = 0.4
            min_support = 3
            max_condition_length = 4
        elif characteristics['randomness_strength'] == '中':
            print("检测到中等随机性，使用标准模式挖掘策略")
            min_confidence = 0.5
            min_support = 4
            max_condition_length = 5
        else:
            print("检测到低随机性，使用强模式挖掘策略")
            min_confidence = 0.6
            min_support = 5
            max_condition_length = 6
        
        all_rules = []
        
        # 全局规则提取
        for condition_length in range(1, max_condition_length + 1):
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    if confidence >= min_confidence:
                        all_rules.append({
                            'condition': condition,
                            'next_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length,
                            'type': 'global'
                        })
        
        # 局部规则提取（针对有模式的段）
        strong_pattern_segments = [p for p in characteristics['local_patterns'] 
                                 if p['pattern_strength'] > 0.1]
        
        if strong_pattern_segments:
            print(f"在 {len(strong_pattern_segments)} 个强模式段中提取局部规则")
            
            for segment_info in strong_pattern_segments:
                start = segment_info['start']
                end = min(start + 1000, self.n)
                segment = self.sequence[start:end]
                
                # 在段内提取规则
                for condition_length in range(2, 4):
                    condition_stats = defaultdict(lambda: defaultdict(int))
                    
                    for i in range(condition_length, len(segment)):
                        condition = tuple(segment[i-condition_length:i])
                        next_val = segment[i]
                        condition_stats[condition][next_val] += 1
                    
                    for condition, next_counts in condition_stats.items():
                        total = sum(next_counts.values())
                        if total >= 3:
                            best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                            confidence = next_counts[best_next] / total
                            
                            if confidence >= 0.6:
                                all_rules.append({
                                    'condition': condition,
                                    'next_value': best_next,
                                    'confidence': confidence,
                                    'support': total,
                                    'length': condition_length,
                                    'type': 'local',
                                    'segment_start': start
                                })
        
        # 按置信度排序
        all_rules.sort(key=lambda x: x['confidence'], reverse=True)
        
        print(f"提取了 {len(all_rules)} 个自适应规则")
        if all_rules:
            print("最佳规则 (前10个):")
            for rule in all_rules[:10]:
                print(f"  {rule['condition']} -> {rule['next_value']} "
                      f"(置信度: {rule['confidence']:.3f}, 类型: {rule['type']})")
        
        return {'rules': all_rules}
    
    def frequency_based_prediction(self) -> Dict:
        """基于频率的预测模型"""
        print(f"\n=== 构建频率预测模型 ===")
        
        # 1. 全局频率
        global_counter = Counter(self.sequence)
        global_probs = {k: v/self.n for k, v in global_counter.items()}
        
        # 2. 位置相关频率
        position_patterns = {}
        for mod in range(8):
            position_values = []
            for i in range(mod, self.n, 8):
                position_values.append(self.sequence[i])
            
            if position_values:
                pos_counter = Counter(position_values)
                position_patterns[mod] = {
                    'distribution': {k: v/len(position_values) for k, v in pos_counter.items()},
                    'most_common': pos_counter.most_common(1)[0][0]
                }
        
        # 3. 最近趋势
        recent_size = min(500, self.n // 4)
        recent_sequence = self.sequence[-recent_size:]
        recent_counter = Counter(recent_sequence)
        recent_probs = {k: v/len(recent_sequence) for k, v in recent_counter.items()}
        
        print(f"全局最频繁: {global_counter.most_common(3)}")
        print(f"最近最频繁: {recent_counter.most_common(3)}")
        
        return {
            'global_probs': global_probs,
            'position_patterns': position_patterns,
            'recent_probs': recent_probs,
            'global_most_common': global_counter.most_common(1)[0][0],
            'recent_most_common': recent_counter.most_common(1)[0][0]
        }
    
    def hybrid_prediction_system(self, rules: Dict, frequency_model: Dict) -> List[Dict]:
        """混合预测系统"""
        print(f"\n=== 混合预测系统 (预测30个值) ===")
        
        predictions = []
        current_sequence = self.sequence.copy()
        
        for step in range(30):
            prediction_candidates = []
            
            # 1. 尝试规则预测
            rule_prediction = None
            rule_confidence = 0
            
            for rule in rules['rules']:
                condition_length = rule['length']
                if len(current_sequence) >= condition_length:
                    current_condition = tuple(current_sequence[-condition_length:])
                    if current_condition == rule['condition']:
                        rule_prediction = rule['next_value']
                        rule_confidence = rule['confidence']
                        break
            
            if rule_prediction:
                prediction_candidates.append({
                    'value': rule_prediction,
                    'confidence': rule_confidence,
                    'method': 'rule'
                })
            
            # 2. 频率预测
            position_mod = len(current_sequence) % 8
            if position_mod in frequency_model['position_patterns']:
                pos_prediction = frequency_model['position_patterns'][position_mod]['most_common']
                prediction_candidates.append({
                    'value': pos_prediction,
                    'confidence': 0.3,
                    'method': 'position'
                })
            
            # 3. 最近趋势预测
            recent_prediction = frequency_model['recent_most_common']
            prediction_candidates.append({
                'value': recent_prediction,
                'confidence': 0.2,
                'method': 'recent'
            })
            
            # 4. 全局频率预测
            global_prediction = frequency_model['global_most_common']
            prediction_candidates.append({
                'value': global_prediction,
                'confidence': 0.15,
                'method': 'global'
            })
            
            # 选择最佳预测
            if prediction_candidates:
                best_prediction = max(prediction_candidates, key=lambda x: x['confidence'])
                final_value = best_prediction['value']
                final_confidence = best_prediction['confidence']
                final_method = best_prediction['method']
            else:
                final_value = np.random.randint(1, 9)
                final_confidence = 0.1
                final_method = 'random'
            
            prediction_info = {
                'step': step + 1,
                'predicted_value': final_value,
                'confidence': final_confidence,
                'method': final_method,
                'candidates': len(prediction_candidates)
            }
            
            predictions.append(prediction_info)
            current_sequence.append(final_value)
            
            if step < 15:  # 显示前15个预测
                print(f"  步骤 {step+1}: 预测 {final_value} "
                      f"(置信度: {final_confidence:.3f}, 方法: {final_method})")
        
        return predictions
    
    def run_adaptive_analysis(self) -> Dict:
        """运行自适应分析"""
        print("开始针对10000样本的自适应分析...")
        print(f"数据集大小: {self.n}")
        print()
        
        start_time = time.time()
        
        # 1. 检测数据特征
        characteristics = self.detect_data_characteristics()
        
        # 2. 自适应规则提取
        rules = self.adaptive_rule_extraction(characteristics)
        
        # 3. 频率预测模型
        frequency_model = self.frequency_based_prediction()
        
        # 4. 混合预测
        predictions = self.hybrid_prediction_system(rules, frequency_model)
        
        elapsed_time = time.time() - start_time
        
        return {
            'characteristics': characteristics,
            'rules': rules,
            'frequency_model': frequency_model,
            'predictions': predictions,
            'analysis_time': elapsed_time
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机10000个数字.txt")
    analyzer = AdaptiveAnalyzer10K(sequence)
    results = analyzer.run_adaptive_analysis()
    
    print(f"\n=== 自适应分析总结 ===")
    print(f"数据随机性: {results['characteristics']['randomness_strength']}")
    print(f"提取规则数: {len(results['rules']['rules'])}")
    print(f"分析耗时: {results['analysis_time']:.2f} 秒")
    
    # 提取预测值
    predicted_values = [p['predicted_value'] for p in results['predictions']]
    print(f"\n预测的未来30个值: {predicted_values}")
    
    # 统计预测方法
    method_counts = Counter(p['method'] for p in results['predictions'])
    print(f"\n预测方法统计: {dict(method_counts)}")
    
    # 高置信度预测
    high_conf_predictions = [p for p in results['predictions'] if p['confidence'] >= 0.4]
    print(f"中等置信度预测 (≥40%): {len(high_conf_predictions)} 个")
    
    print(f"\n💡 针对高随机性数据的建议:")
    print(f"   - 这个10000样本数据集具有很高的随机性质量")
    print(f"   - 传统的条件概率方法效果有限")
    print(f"   - 建议使用频率分析和位置模式相结合的方法")
    print(f"   - 可能需要寻找其他类型的模式（如时间相关、状态相关等）")
