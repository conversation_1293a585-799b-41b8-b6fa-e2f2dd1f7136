#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试投注日志重复统计修复
"""

import os
import sys
from datetime import datetime
from real_time_logger import RealTimeLogger

def test_log_fix():
    """测试日志重复统计修复"""
    
    print("🧪 测试投注日志重复统计修复")
    print("=" * 50)
    
    # 创建测试记录器
    logger = RealTimeLogger("test_fix_log")
    
    # 模拟多次投注，每次都有房间统计
    test_data = [
        {
            'issue': 130001,
            'predicted_room': 2,
            'confidence': 0.75,
            'rule_type': 'dynamic',
            'available_rooms': [1,3,4,5,6,7,8],
            'selected_room': 4,
            'strategy': '直接投注',
            'selection_details': {'mapping': '2->4', 'reason': 'LCG算法选择'},
            'bet_amount': 1.0,
            'bet_success': True,
            'actual_room': 1,
            'result': '获胜',
            'profit': 0.06,
            'room_stats': {
                1: {'total_medal': 227.0, 'share_medal': 45.4, 'total_buy_stroke': 23, 'total_stroke': 2293.0, 'user_count': 23},
                2: {'total_medal': 43.5, 'share_medal': 8.7, 'total_buy_stroke': 13, 'total_stroke': 448.0, 'user_count': 13},
                3: {'total_medal': 821.0, 'share_medal': 164.2, 'total_buy_stroke': 15, 'total_stroke': 8225.0, 'user_count': 15},
                4: {'total_medal': 807.0, 'share_medal': 161.4, 'total_buy_stroke': 25, 'total_stroke': 8095.0, 'user_count': 25},
                5: {'total_medal': 3015.0, 'share_medal': 603.0, 'total_buy_stroke': 15, 'total_stroke': 30165.0, 'user_count': 15},
                6: {'total_medal': 653.0, 'share_medal': 130.6, 'total_buy_stroke': 22, 'total_stroke': 6552.0, 'user_count': 22},
                7: {'total_medal': 468.0, 'share_medal': 93.6, 'total_buy_stroke': 23, 'total_stroke': 4703.0, 'user_count': 23},
                8: {'total_medal': 159.0, 'share_medal': 31.8, 'total_buy_stroke': 30, 'total_stroke': 1620.0, 'user_count': 30}
            }
        },
        {
            'issue': 130002,
            'predicted_room': 5,
            'confidence': 0.82,
            'rule_type': 'dynamic',
            'available_rooms': [1,2,3,4,6,7,8],
            'selected_room': 3,
            'strategy': '直接投注',
            'selection_details': {'mapping': '5->3', 'reason': 'LCG算法选择'},
            'bet_amount': 1.0,
            'bet_success': True,
            'actual_room': 7,
            'result': '获胜',
            'profit': 0.08,
            'room_stats': {
                1: {'total_medal': 126.0, 'share_medal': 0.0, 'total_buy_stroke': 4234, 'total_stroke': 5494.0, 'user_count': 19},
                2: {'total_medal': 197.0, 'share_medal': 163.8, 'total_buy_stroke': 2614, 'total_stroke': 4584.0, 'user_count': 16},
                3: {'total_medal': 193.0, 'share_medal': 160.4, 'total_buy_stroke': 11284, 'total_stroke': 13214.0, 'user_count': 20},
                4: {'total_medal': 110.0, 'share_medal': 91.4, 'total_buy_stroke': 2295, 'total_stroke': 3395.0, 'user_count': 14},
                5: {'total_medal': 542.0, 'share_medal': 450.6, 'total_buy_stroke': 2270, 'total_stroke': 7690.0, 'user_count': 24},
                6: {'total_medal': 309.0, 'share_medal': 256.9, 'total_buy_stroke': 5657, 'total_stroke': 8747.0, 'user_count': 17},
                7: {'total_medal': 697.0, 'share_medal': 579.5, 'total_buy_stroke': 1460, 'total_stroke': 8430.0, 'user_count': 16},
                8: {'total_medal': 505.0, 'share_medal': 419.9, 'total_buy_stroke': 2326, 'total_stroke': 7376.0, 'user_count': 25}
            }
        },
        {
            'issue': 130003,
            'predicted_room': 8,
            'confidence': 0.68,
            'rule_type': 'dynamic',
            'available_rooms': [1,2,3,4,5,6,7],
            'selected_room': 6,
            'strategy': '直接投注',
            'selection_details': {'mapping': '8->6', 'reason': 'LCG算法选择'},
            'bet_amount': 1.0,
            'bet_success': True,
            'actual_room': 2,
            'result': '获胜',
            'profit': 0.12,
            'room_stats': {
                1: {'total_medal': 909.0, 'share_medal': 1411.6, 'total_buy_stroke': 2325, 'total_stroke': 11415.0, 'user_count': 27},
                2: {'total_medal': 665.0, 'share_medal': 1032.7, 'total_buy_stroke': 4652, 'total_stroke': 11302.0, 'user_count': 23},
                3: {'total_medal': 156.0, 'share_medal': 242.2, 'total_buy_stroke': 9197, 'total_stroke': 10757.0, 'user_count': 6},
                4: {'total_medal': 425.0, 'share_medal': 659.9, 'total_buy_stroke': 1847, 'total_stroke': 6097.0, 'user_count': 11},
                5: {'total_medal': 26.1, 'share_medal': 40.5, 'total_buy_stroke': 4127, 'total_stroke': 4388.0, 'user_count': 24},
                6: {'total_medal': 813.0, 'share_medal': 1262.5, 'total_buy_stroke': 4937, 'total_stroke': 13067.0, 'user_count': 20},
                7: {'total_medal': 1013.0, 'share_medal': 1573.1, 'total_buy_stroke': 230, 'total_stroke': 10360.0, 'user_count': 19},
                8: {'total_medal': 462.0, 'share_medal': 0.0, 'total_buy_stroke': 7753, 'total_stroke': 12373.0, 'user_count': 22}
            }
        }
    ]
    
    # 模拟完整的投注流程
    for i, data in enumerate(test_data):
        print(f"\n📝 模拟第{i+1}次投注 - 期号{data['issue']}")
        
        # 记录预测
        logger.log_prediction(
            data['issue'], 
            data['predicted_room'], 
            data['confidence'], 
            data['rule_type']
        )
        
        # 记录房间选择
        logger.log_room_selection(
            data['issue'],
            data['available_rooms'],
            data['selected_room'],
            data['strategy'],
            data['selection_details']
        )
        
        # 记录投注
        logger.log_betting(
            data['issue'],
            data['selected_room'],
            data['bet_amount'],
            data['bet_success']
        )
        
        # 记录结果
        logger.log_result(
            data['issue'],
            data['actual_room'],
            data['result'],
            data['profit'],
            data['room_stats']
        )
        
        print(f"✅ 期号{data['issue']}投注记录完成")
    
    # 获取摘要
    summary = logger.get_summary()
    print(f"\n📊 测试完成摘要:")
    print(f"- 总投注次数: {summary['total_bets']}")
    print(f"- 获胜次数: {summary['total_wins']}")
    print(f"- 胜率: {summary['win_rate']:.1f}%")
    print(f"- 总盈亏: {summary['total_profit']:+.2f}元")
    
    # 检查生成的文件
    md_file = summary['files']['markdown']
    if os.path.exists(md_file):
        print(f"\n📄 生成的Markdown文件: {md_file}")
        
        # 检查文件内容是否有重复
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 统计"LCG算法选择详情"出现次数
        lcg_count = content.count("## 🎯 LCG算法选择详情")
        print(f"🔍 '## 🎯 LCG算法选择详情' 出现次数: {lcg_count}")
        
        # 统计每个期号的投注详情出现次数
        for data in test_data:
            issue = data['issue']
            pattern = f"### 期号{issue} - 第"
            count = content.count(pattern)
            print(f"🔍 期号{issue}投注详情出现次数: {count}")
        
        if lcg_count == 1:
            print("✅ 修复成功！LCG算法选择详情只出现一次")
        else:
            print("❌ 修复失败！LCG算法选择详情仍有重复")
            
        # 统计房间统计信息出现次数
        room_stats_count = content.count("## 🏠 房间统计信息")
        print(f"🔍 '## 🏠 房间统计信息' 出现次数: {room_stats_count}")
        
        if room_stats_count == 1:
            print("✅ 房间统计信息结构正常")
        else:
            print("❌ 房间统计信息可能有问题")
    
    print(f"\n🎯 测试完成！请查看生成的文件验证修复效果")
    print(f"📁 文件位置: {md_file}")

if __name__ == "__main__":
    test_log_fix()
