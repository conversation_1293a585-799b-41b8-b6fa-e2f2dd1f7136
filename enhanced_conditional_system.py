#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强条件概率系统
通过动态权重、规则融合和上下文感知提升预测准确率
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import math

class EnhancedConditionalSystem:
    def __init__(self, sequence: List[int]):
        """初始化增强条件概率系统"""
        self.sequence = sequence
        self.n = len(sequence)
        self.rules = {}
        self.rule_performance = {}
        self.context_weights = {}
        
    def extract_weighted_rules(self, max_length: int = 6) -> Dict:
        """提取带权重的条件规则"""
        print("=== 提取增强条件规则 ===")
        
        all_rules = {}
        
        for condition_length in range(1, max_length + 1):
            print(f"\n分析 {condition_length} 元条件:")
            
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            condition_positions = defaultdict(list)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
                condition_positions[condition].append(i)
            
            rules = []
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= 2:  # 降低最小支持度
                    
                    # 计算每个可能结果的概率
                    probabilities = {}
                    for next_val, count in next_counts.items():
                        probabilities[next_val] = count / total
                    
                    # 找出最可能的结果
                    best_next = max(probabilities.keys(), key=lambda k: probabilities[k])
                    confidence = probabilities[best_next]
                    
                    # 计算信息增益
                    entropy_before = -sum(p * math.log2(p) for p in [1/8] * 8)  # 均匀分布熵
                    entropy_after = -sum(p * math.log2(p) for p in probabilities.values() if p > 0)
                    information_gain = entropy_before - entropy_after
                    
                    # 计算位置方差（检查规则是否在整个序列中均匀分布）
                    positions = condition_positions[condition]
                    position_variance = np.var(positions) if len(positions) > 1 else 0
                    
                    # 综合权重计算
                    weight = (
                        confidence * 0.4 +  # 置信度权重
                        information_gain * 0.3 +  # 信息增益权重
                        min(total / 10, 1.0) * 0.2 +  # 支持度权重（归一化）
                        (1 - min(position_variance / 1000000, 1.0)) * 0.1  # 分布均匀性权重
                    )
                    
                    if confidence >= 0.4 or (confidence >= 0.3 and total >= 5):  # 更灵活的阈值
                        rules.append({
                            'condition': condition,
                            'next_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'information_gain': information_gain,
                            'weight': weight,
                            'probabilities': probabilities,
                            'positions': positions
                        })
            
            # 按权重排序
            rules.sort(key=lambda x: x['weight'], reverse=True)
            
            if rules:
                print(f"  发现 {len(rules)} 个加权规则 (前10个):")
                for rule in rules[:10]:
                    print(f"    {rule['condition']} -> {rule['next_value']} "
                          f"(权重: {rule['weight']:.3f}, 置信度: {rule['confidence']:.3f}, "
                          f"支持度: {rule['support']}, 信息增益: {rule['information_gain']:.3f})")
                
                all_rules[condition_length] = rules
        
        return all_rules
    
    def build_ensemble_predictor(self, rules: Dict) -> Dict:
        """构建集成预测器"""
        print(f"\n=== 构建集成预测器 ===")
        
        # 将所有规则按权重排序
        all_rules = []
        for length, rule_list in rules.items():
            for rule in rule_list:
                rule['length'] = length
                all_rules.append(rule)
        
        all_rules.sort(key=lambda x: x['weight'], reverse=True)
        
        # 测试集成预测
        correct_predictions = 0
        total_predictions = 0
        prediction_details = []
        
        for i in range(6, self.n):  # 从第7个数字开始预测
            predictions = []
            
            # 收集所有匹配的规则
            for rule in all_rules:
                condition_length = rule['length']
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predictions.append({
                            'value': rule['next_value'],
                            'weight': rule['weight'],
                            'confidence': rule['confidence'],
                            'rule': rule
                        })
            
            if predictions:
                # 方法1: 加权投票
                vote_scores = defaultdict(float)
                for pred in predictions:
                    vote_scores[pred['value']] += pred['weight']
                
                if vote_scores:
                    weighted_prediction = max(vote_scores.keys(), key=lambda k: vote_scores[k])
                    
                    # 方法2: 最高权重规则
                    highest_weight_prediction = max(predictions, key=lambda x: x['weight'])['value']
                    
                    # 方法3: 最高置信度规则
                    highest_conf_prediction = max(predictions, key=lambda x: x['confidence'])['value']
                    
                    # 选择最终预测（这里使用加权投票）
                    final_prediction = weighted_prediction
                    
                    actual = self.sequence[i]
                    is_correct = (final_prediction == actual)
                    
                    if is_correct:
                        correct_predictions += 1
                    
                    total_predictions += 1
                    
                    prediction_details.append({
                        'position': i,
                        'predicted': final_prediction,
                        'actual': actual,
                        'correct': is_correct,
                        'num_rules': len(predictions),
                        'vote_scores': dict(vote_scores),
                        'methods': {
                            'weighted': weighted_prediction,
                            'highest_weight': highest_weight_prediction,
                            'highest_conf': highest_conf_prediction
                        }
                    })
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        print(f"集成预测准确率: {accuracy:.4f} ({correct_predictions}/{total_predictions})")
        
        return {
            'rules': all_rules,
            'accuracy': accuracy,
            'prediction_details': prediction_details[-30:],  # 最后30个预测
            'total_predictions': total_predictions
        }
    
    def adaptive_learning(self, predictor: Dict, learning_rate: float = 0.1) -> Dict:
        """自适应学习 - 根据预测结果调整规则权重"""
        print(f"\n=== 自适应学习 ===")
        
        # 统计每个规则的表现
        rule_performance = defaultdict(lambda: {'correct': 0, 'total': 0, 'accuracy': 0.0})
        
        for detail in predictor['prediction_details']:
            # 找出参与预测的规则
            position = detail['position']
            actual = detail['actual']
            
            for rule in predictor['rules']:
                condition_length = rule['length']
                if position >= condition_length:
                    current_condition = tuple(self.sequence[position-condition_length:position])
                    if current_condition == rule['condition']:
                        rule_id = str(rule['condition'])
                        rule_performance[rule_id]['total'] += 1
                        if rule['next_value'] == actual:
                            rule_performance[rule_id]['correct'] += 1
        
        # 更新规则权重
        updated_rules = []
        for rule in predictor['rules']:
            rule_id = str(rule['condition'])
            if rule_performance[rule_id]['total'] > 0:
                performance_accuracy = rule_performance[rule_id]['correct'] / rule_performance[rule_id]['total']
                rule_performance[rule_id]['accuracy'] = performance_accuracy
                
                # 自适应调整权重
                if performance_accuracy > 0.7:
                    rule['weight'] *= (1 + learning_rate)  # 提升表现好的规则
                elif performance_accuracy < 0.3:
                    rule['weight'] *= (1 - learning_rate)  # 降低表现差的规则
            
            updated_rules.append(rule)
        
        # 重新排序
        updated_rules.sort(key=lambda x: x['weight'], reverse=True)
        
        print(f"更新了 {len(rule_performance)} 个规则的权重")
        
        # 显示表现最好的规则
        best_rules = sorted(rule_performance.items(), 
                           key=lambda x: x[1]['accuracy'], reverse=True)[:10]
        
        print("表现最佳的规则:")
        for rule_id, perf in best_rules:
            if perf['total'] >= 3:  # 至少使用过3次
                print(f"  {rule_id}: {perf['accuracy']:.3f} ({perf['correct']}/{perf['total']})")
        
        return {
            'updated_rules': updated_rules,
            'rule_performance': dict(rule_performance)
        }
    
    def predict_future_enhanced(self, predictor: Dict, count: int = 20) -> List[Dict]:
        """增强的未来预测"""
        print(f"\n=== 增强未来预测 (预测 {count} 个值) ===")
        
        predictions = []
        current_sequence = self.sequence.copy()
        confidence_threshold = 0.5  # 动态调整的置信度阈值
        
        for step in range(count):
            step_predictions = []
            
            # 收集所有匹配的规则
            for rule in predictor['rules']:
                condition_length = rule['length']
                if len(current_sequence) >= condition_length:
                    current_condition = tuple(current_sequence[-condition_length:])
                    if current_condition == rule['condition']:
                        step_predictions.append(rule)
            
            if step_predictions:
                # 使用加权投票
                vote_scores = defaultdict(float)
                for rule in step_predictions:
                    vote_scores[rule['next_value']] += rule['weight']
                
                # 选择得分最高的预测
                best_prediction = max(vote_scores.keys(), key=lambda k: vote_scores[k])
                best_score = vote_scores[best_prediction]
                
                # 计算预测置信度
                total_score = sum(vote_scores.values())
                prediction_confidence = best_score / total_score if total_score > 0 else 0
                
                # 获取最佳规则的详细信息
                best_rule = max(step_predictions, key=lambda x: x['weight'])
                
                prediction_info = {
                    'step': step + 1,
                    'predicted_value': best_prediction,
                    'confidence': prediction_confidence,
                    'num_supporting_rules': len(step_predictions),
                    'best_rule_condition': best_rule['condition'],
                    'best_rule_confidence': best_rule['confidence'],
                    'vote_scores': dict(vote_scores)
                }
                
                print(f"  步骤 {step+1}: 预测 {best_prediction} "
                      f"(置信度: {prediction_confidence:.3f}, "
                      f"支持规则: {len(step_predictions)}, "
                      f"最佳规则: {best_rule['condition']})")
                
            else:
                # 没有匹配规则时的回退策略
                # 使用最近几个数字的统计信息
                recent_window = current_sequence[-10:] if len(current_sequence) >= 10 else current_sequence
                counter = Counter(recent_window)
                fallback_prediction = counter.most_common(1)[0][0]
                
                prediction_info = {
                    'step': step + 1,
                    'predicted_value': fallback_prediction,
                    'confidence': 0.1,  # 低置信度
                    'num_supporting_rules': 0,
                    'best_rule_condition': 'fallback',
                    'best_rule_confidence': 0.0,
                    'vote_scores': {fallback_prediction: 1.0}
                }
                
                print(f"  步骤 {step+1}: 回退预测 {fallback_prediction} (无匹配规则)")
            
            predictions.append(prediction_info)
            current_sequence.append(prediction_info['predicted_value'])
        
        return predictions
    
    def run_enhanced_system(self) -> Dict:
        """运行增强系统"""
        print("开始增强条件概率系统...")
        print(f"序列长度: {self.n}")
        print()
        
        # 1. 提取加权规则
        weighted_rules = self.extract_weighted_rules()
        
        # 2. 构建集成预测器
        ensemble_predictor = self.build_ensemble_predictor(weighted_rules)
        
        # 3. 自适应学习
        adaptive_result = self.adaptive_learning(ensemble_predictor)
        
        # 4. 使用更新后的规则重新构建预测器
        updated_predictor = {
            'rules': adaptive_result['updated_rules'],
            'rule_performance': adaptive_result['rule_performance']
        }
        
        # 5. 增强未来预测
        future_predictions = self.predict_future_enhanced(updated_predictor, 25)
        
        return {
            'weighted_rules': weighted_rules,
            'ensemble_accuracy': ensemble_predictor['accuracy'],
            'adaptive_result': adaptive_result,
            'future_predictions': future_predictions,
            'updated_predictor': updated_predictor
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    system = EnhancedConditionalSystem(sequence)
    results = system.run_enhanced_system()
    
    print(f"\n=== 增强系统总结 ===")
    print(f"集成预测准确率: {results['ensemble_accuracy']:.4f}")
    
    # 显示高置信度的预测
    high_confidence_predictions = [p for p in results['future_predictions'] 
                                 if p['confidence'] > 0.6]
    
    print(f"\n高置信度预测 (置信度 > 0.6): {len(high_confidence_predictions)} 个")
    for pred in high_confidence_predictions:
        print(f"  步骤 {pred['step']}: {pred['predicted_value']} "
              f"(置信度: {pred['confidence']:.3f})")
    
    # 提取预测值列表
    predicted_values = [p['predicted_value'] for p in results['future_predictions']]
    print(f"\n预测的未来25个值: {predicted_values}")
    
    if results['ensemble_accuracy'] > 0.7:
        print(f"\n🎯 系统性能优秀！准确率超过70%")
    elif results['ensemble_accuracy'] > 0.65:
        print(f"\n✅ 系统性能良好！准确率超过65%")
    else:
        print(f"\n📈 系统仍有改进空间")
