#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试系统
用于快速验证API连接和基础功能
"""

import requests
import time
import json
from datetime import datetime
from typing import Dict, List, Optional

class SimpleGameAPIClient:
    """简化的游戏API客户端"""
    
    def __init__(self, base_url: str, headers: Dict[str, str]):
        """初始化API客户端"""
        self.base_url = base_url
        self.headers = headers
        self.session = requests.Session()
        self.session.headers.update(headers)
        
    def get_game_state(self) -> Optional[Dict]:
        """获取游戏状态"""
        try:
            # 更新时间戳
            self.headers['ts'] = str(int(time.time() * 1000))
            self.session.headers.update(self.headers)
            
            url = f"{self.base_url}/v11/api/stroke/data"
            params = {
                'uid': self.headers.get('userId', ''),
                'version': self.headers.get('version', '5.2.2')
            }
            
            print(f"正在请求: {url}")
            response = self.session.post(url, params=params, timeout=10)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                    return data
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    print(f"原始响应: {response.text[:200]}...")
                    return None
            else:
                print(f"HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"网络请求异常: {e}")
            return None
        except Exception as e:
            print(f"未知错误: {e}")
            return None
    
    def place_test_bet(self, room_number: int, cost_medal: float) -> bool:
        """测试投注功能"""
        try:
            # 更新时间戳
            self.headers['ts'] = str(int(time.time() * 1000))
            self.session.headers.update(self.headers)
            
            url = f"{self.base_url}/v11/api/stroke/buy"
            params = {
                'uid': self.headers.get('userId', ''),
                'version': self.headers.get('version', '5.2.2')
            }
            
            data = {
                'roomNumber': room_number,
                'costMedal': cost_medal
            }
            
            print(f"测试投注: 房间{room_number}, 金额{cost_medal}")
            response = self.session.post(url, params=params, data=data, timeout=10)
            
            print(f"投注响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"投注响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"投注响应JSON解析失败: {e}")
                    return False
            else:
                print(f"投注失败: {response.status_code}")
                print(f"错误内容: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"投注异常: {e}")
            return False

class SimpleTestSystem:
    """简化测试系统"""
    
    def __init__(self, api_config: Dict):
        """初始化测试系统"""
        self.api_client = SimpleGameAPIClient(
            base_url=api_config['base_url'],
            headers=api_config['headers']
        )
        
    def run_connection_test(self):
        """运行连接测试"""
        print("=== API连接测试 ===")
        
        # 测试游戏状态获取
        print("\n1. 测试游戏状态获取...")
        state = self.api_client.get_game_state()
        
        if state:
            print("✅ 游戏状态获取成功")
            
            # 尝试解析关键信息
            if isinstance(state, dict):
                issue = state.get('issue', '未知')
                kill_number = state.get('killNumber', '未知')
                my_room = state.get('myRoomNumber', '未知')
                
                print(f"   期号: {issue}")
                print(f"   开出房间: {kill_number}")
                print(f"   我的房间: {my_room}")
            
            return True
        else:
            print("❌ 游戏状态获取失败")
            return False
    
    def run_bet_test(self):
        """运行投注测试"""
        print("\n=== 投注功能测试 ===")
        
        # 询问是否进行投注测试
        response = input("是否进行小额投注测试？(yes/no): ").lower().strip()
        
        if response in ['yes', 'y', '是']:
            print("\n2. 测试投注功能...")
            
            # 使用最小金额测试
            test_room = 1
            test_amount = 0.01  # 最小测试金额
            
            success = self.api_client.place_test_bet(test_room, test_amount)
            
            if success:
                print("✅ 投注功能测试成功")
                return True
            else:
                print("❌ 投注功能测试失败")
                return False
        else:
            print("跳过投注测试")
            return True
    
    def run_full_test(self):
        """运行完整测试"""
        print("🧪 开始系统测试...")
        print("=" * 50)
        
        # 连接测试
        connection_ok = self.run_connection_test()
        
        if not connection_ok:
            print("\n❌ 连接测试失败，请检查API配置")
            return False
        
        # 投注测试
        bet_ok = self.run_bet_test()
        
        if connection_ok and bet_ok:
            print("\n🎉 所有测试通过！系统可以正常使用")
            return True
        else:
            print("\n⚠️  部分测试失败，请检查配置")
            return False

def create_test_config():
    """创建测试配置"""
    
    print("=== 配置API认证信息 ===")
    print("请填入您的API认证信息：")
    
    # 获取用户输入
    user_id = input("用户ID (userId): ").strip()
    if not user_id:
        user_id = "8607652"  # 默认值
    
    token = input("认证Token (token): ").strip()
    if not token:
        print("⚠️  使用示例token，可能无法正常工作")
        token = "your-token-here"
    
    android_id = input("设备ID (androidId) [可选]: ").strip()
    if not android_id:
        android_id = "e21953ffb86fa7a8"
    
    return {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': android_id,
            'userId': user_id,
            'token': token,
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'test-signature',  # 这里需要实际的签名算法
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }

def main():
    """主函数"""
    
    print("🧪 简化测试系统")
    print("=" * 50)
    print("用于快速验证API连接和基础功能")
    print()
    
    # 选择配置方式
    print("请选择配置方式:")
    print("1. 交互式输入API信息")
    print("2. 使用预设配置 (需要手动修改代码)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == '1':
        # 交互式配置
        api_config = create_test_config()
    else:
        # 使用预设配置
        api_config = {
            'base_url': 'https://fks-api.lucklyworld.com',
            'headers': {
                'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
                'packageId': 'com.caike.union',
                'version': '5.2.2',
                'channel': 'official',
                'androidId': 'e21953ffb86fa7a8',
                'userId': '8607652',  # 🔴 需要替换
                'token': 'your-token-here',  # 🔴 需要替换
                'IMEI': '',
                'ts': str(int(time.time() * 1000)),
                'sign': 'test-signature',  # 🔴 需要实际签名
                'Content-Type': 'application/x-www-form-urlencoded',
                'Host': 'fks-api.lucklyworld.com',
                'Connection': 'Keep-Alive',
                'Accept-Encoding': 'gzip'
            }
        }
        
        print("⚠️  使用预设配置，请确保已修改API认证信息")
    
    # 创建并运行测试系统
    test_system = SimpleTestSystem(api_config)
    
    print(f"\n开始测试...")
    success = test_system.run_full_test()
    
    if success:
        print(f"\n✅ 测试完成！您可以继续使用完整的投注系统")
    else:
        print(f"\n❌ 测试失败！请检查API配置和网络连接")

if __name__ == "__main__":
    main()
