#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能投注金额处理器
解决API只支持固定面额(1,10,100,500)的问题
将自定义金额转换为多次API调用
"""

import time
from typing import List, Dict, Tuple
from dataclasses import dataclass

@dataclass
class BetRequest:
    """投注请求"""
    room: int
    amount: float
    
@dataclass
class BetResult:
    """投注结果"""
    success: bool
    total_amount: float
    successful_bets: List[Dict]
    failed_bets: List[Dict]
    message: str

class SmartBettingHandler:
    """智能投注金额处理器"""
    
    def __init__(self, api_client):
        """初始化处理器"""
        self.api_client = api_client
        
        # API支持的固定面额 (从大到小排序)
        self.supported_amounts = [500, 100, 10, 1]
        
        # 投注间隔时间 (避免API限制)
        self.bet_interval = 0.1  # 100毫秒间隔
        
        # 最大重试次数
        self.max_retries = 3
        
        print("🎯 智能投注处理器已初始化")
        print(f"   支持面额: {self.supported_amounts}")
        print(f"   投注间隔: {self.bet_interval}秒")
    
    def calculate_bet_breakdown(self, target_amount: float) -> List[int]:
        """
        计算投注金额分解
        将自定义金额分解为API支持的固定面额组合
        
        Args:
            target_amount: 目标投注金额
            
        Returns:
            List[int]: 分解后的面额列表
        """
        
        if target_amount <= 0:
            return []
        
        # 四舍五入到小数点后2位
        target_amount = round(target_amount, 2)
        
        breakdown = []
        remaining = target_amount
        
        print(f"🔢 金额分解: 目标金额 {target_amount}元")
        
        # 贪心算法：优先使用大面额
        for amount in self.supported_amounts:
            count = int(remaining // amount)
            if count > 0:
                breakdown.extend([amount] * count)
                remaining = round(remaining - (amount * count), 2)
                print(f"   使用 {amount}元 × {count}次 = {amount * count}元")
        
        # 处理剩余的小数部分
        if remaining > 0:
            if remaining >= 0.5:
                # 剩余>=0.5元，补充1元
                breakdown.append(1)
                actual_total = sum(breakdown)
                print(f"   剩余 {remaining}元，补充1元投注")
                print(f"   实际投注: {actual_total}元 (超出 {actual_total - target_amount:.2f}元)")
            else:
                # 剩余<0.5元，忽略
                actual_total = sum(breakdown)
                print(f"   剩余 {remaining}元，忽略小数部分")
                print(f"   实际投注: {actual_total}元 (减少 {target_amount - actual_total:.2f}元)")
        else:
            actual_total = sum(breakdown)
            print(f"   完美匹配: {actual_total}元")
        
        return breakdown
    
    def execute_smart_bet(self, room: int, target_amount: float) -> BetResult:
        """
        执行智能投注
        
        Args:
            room: 投注房间号
            target_amount: 目标投注金额
            
        Returns:
            BetResult: 投注结果
        """
        
        print(f"\n💰 智能投注执行:")
        print(f"   房间: {room}")
        print(f"   目标金额: {target_amount:.2f}元")
        
        # 计算金额分解
        bet_breakdown = self.calculate_bet_breakdown(target_amount)
        
        if not bet_breakdown:
            return BetResult(
                success=False,
                total_amount=0,
                successful_bets=[],
                failed_bets=[],
                message="目标金额无效"
            )
        
        successful_bets = []
        failed_bets = []
        total_successful_amount = 0
        
        print(f"📋 执行投注序列: {bet_breakdown}")
        
        # 逐个执行投注
        for i, amount in enumerate(bet_breakdown):
            print(f"   第{i+1}/{len(bet_breakdown)}次: 房间{room}, 金额{amount}元")
            
            # 执行单次投注
            bet_result = self._execute_single_bet(room, amount)
            
            if bet_result.success:
                successful_bets.append({
                    'room': room,
                    'amount': amount,
                    'sequence': i + 1,
                    'response': bet_result.data if hasattr(bet_result, 'data') else None
                })
                total_successful_amount += amount
                print(f"   ✅ 成功: {amount}元")
            else:
                failed_bets.append({
                    'room': room,
                    'amount': amount,
                    'sequence': i + 1,
                    'error': bet_result.message
                })
                print(f"   ❌ 失败: {amount}元 - {bet_result.message}")
            
            # 投注间隔 (除了最后一次)
            if i < len(bet_breakdown) - 1:
                time.sleep(self.bet_interval)
        
        # 判断整体结果
        overall_success = len(successful_bets) > 0
        
        if overall_success:
            success_rate = len(successful_bets) / len(bet_breakdown) * 100
            message = f"投注完成: {len(successful_bets)}/{len(bet_breakdown)}次成功 ({success_rate:.1f}%)"
        else:
            message = "所有投注均失败"
        
        print(f"📊 投注结果: {message}")
        print(f"   成功金额: {total_successful_amount:.2f}元")
        print(f"   目标金额: {target_amount:.2f}元")
        
        return BetResult(
            success=overall_success,
            total_amount=total_successful_amount,
            successful_bets=successful_bets,
            failed_bets=failed_bets,
            message=message
        )
    
    def _execute_single_bet(self, room: int, amount: int) -> object:
        """
        执行单次投注 (带重试机制)
        
        Args:
            room: 房间号
            amount: 投注金额 (必须是API支持的面额)
            
        Returns:
            投注结果对象
        """
        
        if amount not in self.supported_amounts:
            # 创建失败结果对象
            class FailResult:
                def __init__(self, message):
                    self.success = False
                    self.message = message
            
            return FailResult(f"不支持的投注金额: {amount}")
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                result = self.api_client.place_bet(room, amount)
                
                if result.success:
                    return result
                else:
                    if attempt < self.max_retries - 1:
                        print(f"      重试 {attempt + 1}/{self.max_retries}: {result.message}")
                        time.sleep(0.5)  # 重试间隔
                    else:
                        return result
                        
            except Exception as e:
                if attempt < self.max_retries - 1:
                    print(f"      异常重试 {attempt + 1}/{self.max_retries}: {str(e)}")
                    time.sleep(0.5)
                else:
                    # 创建异常结果对象
                    class ExceptionResult:
                        def __init__(self, message):
                            self.success = False
                            self.message = message
                    
                    return ExceptionResult(f"投注异常: {str(e)}")
    
    def batch_smart_bet(self, bet_requests: List[BetRequest]) -> Dict[int, BetResult]:
        """
        批量智能投注
        
        Args:
            bet_requests: 投注请求列表
            
        Returns:
            Dict[int, BetResult]: 房间号到投注结果的映射
        """
        
        print(f"\n🎯 批量智能投注: {len(bet_requests)}个请求")
        
        results = {}
        
        for i, request in enumerate(bet_requests):
            print(f"\n--- 处理第{i+1}/{len(bet_requests)}个请求 ---")
            
            result = self.execute_smart_bet(request.room, request.amount)
            results[request.room] = result
            
            # 批量投注间隔
            if i < len(bet_requests) - 1:
                time.sleep(self.bet_interval * 2)  # 批量间隔更长
        
        # 汇总结果
        total_requests = len(bet_requests)
        successful_requests = sum(1 for r in results.values() if r.success)
        total_amount = sum(r.total_amount for r in results.values())
        
        print(f"\n📊 批量投注汇总:")
        print(f"   总请求: {total_requests}个")
        print(f"   成功请求: {successful_requests}个")
        print(f"   成功率: {successful_requests/total_requests*100:.1f}%")
        print(f"   总投注金额: {total_amount:.2f}元")
        
        return results
    
    def get_optimal_amount_adjustment(self, target_amount: float) -> Tuple[float, str]:
        """
        获取最优金额调整建议
        
        Args:
            target_amount: 目标金额
            
        Returns:
            Tuple[float, str]: (调整后金额, 调整说明)
        """
        
        breakdown = self.calculate_bet_breakdown(target_amount)
        actual_amount = sum(breakdown)
        
        difference = actual_amount - target_amount
        
        if abs(difference) < 0.01:
            return actual_amount, "完美匹配"
        elif difference > 0:
            return actual_amount, f"超出{difference:.2f}元"
        else:
            return actual_amount, f"减少{abs(difference):.2f}元"

def test_smart_betting_handler():
    """测试智能投注处理器"""
    
    # 模拟API客户端
    class MockAPIClient:
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 创建处理器
    handler = SmartBettingHandler(MockAPIClient())
    
    # 测试用例
    test_cases = [
        1.2,   # 需要分解为 1 + 1 (超出0.8元)
        5.7,   # 需要分解为 1×5 + 1 (超出0.3元)  
        15.3,  # 需要分解为 10 + 1×5 + 1 (超出0.7元)
        123.8, # 需要分解为 100 + 10×2 + 1×3 + 1 (超出0.2元)
        0.3,   # 小于0.5，忽略
        0.8,   # 大于0.5，补充为1元
    ]
    
    print("🧪 智能投注处理器测试")
    print("=" * 50)
    
    for amount in test_cases:
        print(f"\n测试金额: {amount}元")
        breakdown = handler.calculate_bet_breakdown(amount)
        actual_amount = sum(breakdown)
        print(f"分解结果: {breakdown}")
        print(f"实际金额: {actual_amount}元")
        print(f"差异: {actual_amount - amount:+.2f}元")

if __name__ == "__main__":
    test_smart_betting_handler()
