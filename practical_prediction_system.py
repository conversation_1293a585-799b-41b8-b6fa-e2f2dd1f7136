#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实战预测系统
基于23607样本的实用随机数预测系统
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import pickle
import json

class PracticalPredictionSystem:
    def __init__(self, sequence: List[int]):
        """初始化实战预测系统"""
        self.sequence = sequence
        self.n = len(sequence)
        self.optimized_rules = []
        self.backup_strategies = {}
        
    def extract_practical_rules(self) -> Dict:
        """提取实用规则"""
        print("=== 提取实用预测规则 ===")
        
        practical_rules = []
        
        # 重点关注3-5元条件，这些在实战中最有效
        for condition_length in [3, 4, 5]:
            print(f"\n分析 {condition_length} 元实用规则:")
            
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            # 提取实用规则（降低阈值以获得更多可用规则）
            min_support = 3 if condition_length <= 4 else 2
            min_confidence = 0.25 if condition_length == 3 else 0.35 if condition_length == 4 else 0.45
            
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    if confidence >= min_confidence:
                        # 计算实用性得分
                        frequency_bonus = min(total / 50, 1.0)  # 频率奖励
                        confidence_score = confidence
                        length_bonus = condition_length / 10  # 长度奖励
                        
                        practical_score = confidence_score * 0.6 + frequency_bonus * 0.3 + length_bonus * 0.1
                        
                        practical_rules.append({
                            'condition': condition,
                            'next_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length,
                            'practical_score': practical_score,
                            'all_outcomes': dict(next_counts)
                        })
            
            print(f"  发现 {len([r for r in practical_rules if r['length'] == condition_length])} 个实用规则")
        
        # 按实用性得分排序
        practical_rules.sort(key=lambda x: x['practical_score'], reverse=True)
        
        print(f"\n总共提取 {len(practical_rules)} 个实用规则")
        print("最佳实用规则 (前15个):")
        for i, rule in enumerate(practical_rules[:15], 1):
            print(f"  {i:2d}. {rule['condition']} -> {rule['next_value']} "
                  f"(置信度: {rule['confidence']:.3f}, 支持度: {rule['support']}, "
                  f"实用得分: {rule['practical_score']:.3f})")
        
        return {'rules': practical_rules}
    
    def build_backup_strategies(self) -> Dict:
        """构建备用策略"""
        print(f"\n=== 构建备用预测策略 ===")
        
        strategies = {}
        
        # 1. 频率策略
        global_counter = Counter(self.sequence)
        strategies['global_frequency'] = {
            'method': 'global_most_common',
            'value': global_counter.most_common(1)[0][0],
            'confidence': global_counter.most_common(1)[0][1] / self.n,
            'distribution': dict(global_counter)
        }
        
        # 2. 最近趋势策略
        recent_size = min(1000, self.n // 4)
        recent_sequence = self.sequence[-recent_size:]
        recent_counter = Counter(recent_sequence)
        strategies['recent_trend'] = {
            'method': 'recent_most_common',
            'value': recent_counter.most_common(1)[0][0],
            'confidence': recent_counter.most_common(1)[0][1] / len(recent_sequence),
            'window_size': recent_size
        }
        
        # 3. 位置模式策略
        position_patterns = {}
        for mod in range(8):
            position_values = []
            for i in range(mod, self.n, 8):
                position_values.append(self.sequence[i])
            
            if position_values:
                pos_counter = Counter(position_values)
                position_patterns[mod] = {
                    'most_common': pos_counter.most_common(1)[0][0],
                    'confidence': pos_counter.most_common(1)[0][1] / len(position_values),
                    'distribution': dict(pos_counter)
                }
        
        strategies['position_pattern'] = position_patterns
        
        # 4. 反向策略（避免最近出现的数字）
        avoid_recent = Counter(self.sequence[-50:])
        least_recent = avoid_recent.most_common()[-1][0]  # 最近最少出现的
        strategies['avoid_recent'] = {
            'method': 'least_recent',
            'value': least_recent,
            'confidence': 0.2
        }
        
        print("备用策略构建完成:")
        print(f"  全局频率: {strategies['global_frequency']['value']} (置信度: {strategies['global_frequency']['confidence']:.3f})")
        print(f"  最近趋势: {strategies['recent_trend']['value']} (置信度: {strategies['recent_trend']['confidence']:.3f})")
        print(f"  位置模式: 8种位置模式")
        print(f"  反向策略: {strategies['avoid_recent']['value']}")
        
        return strategies
    
    def comprehensive_validation(self, rules: Dict, strategies: Dict) -> Dict:
        """综合验证系统"""
        print(f"\n=== 综合验证系统 ===")
        
        # 使用最后20%数据进行验证
        test_size = int(self.n * 0.2)
        train_end = self.n - test_size
        
        print(f"训练集: {train_end} 个样本")
        print(f"验证集: {test_size} 个样本")
        
        # 验证统计
        rule_predictions = 0
        rule_correct = 0
        strategy_predictions = 0
        strategy_correct = 0
        total_predictions = 0
        
        prediction_log = []
        
        for i in range(train_end + 6, self.n):
            actual = self.sequence[i]
            predicted = None
            method = None
            confidence = 0
            
            # 1. 尝试规则预测
            for rule in rules['rules']:
                condition_length = rule['length']
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        confidence = rule['confidence']
                        method = f"rule_{condition_length}"
                        rule_predictions += 1
                        if predicted == actual:
                            rule_correct += 1
                        break
            
            # 2. 如果规则无法预测，使用备用策略
            if predicted is None:
                # 选择最佳备用策略
                position_mod = i % 8
                if position_mod in strategies['position_pattern']:
                    predicted = strategies['position_pattern'][position_mod]['most_common']
                    confidence = strategies['position_pattern'][position_mod]['confidence']
                    method = "position_pattern"
                else:
                    predicted = strategies['recent_trend']['value']
                    confidence = strategies['recent_trend']['confidence']
                    method = "recent_trend"
                
                strategy_predictions += 1
                if predicted == actual:
                    strategy_correct += 1
            
            total_predictions += 1
            
            prediction_log.append({
                'position': i,
                'predicted': predicted,
                'actual': actual,
                'correct': predicted == actual,
                'method': method,
                'confidence': confidence
            })
        
        # 计算准确率
        rule_accuracy = rule_correct / rule_predictions if rule_predictions > 0 else 0
        strategy_accuracy = strategy_correct / strategy_predictions if strategy_predictions > 0 else 0
        overall_accuracy = (rule_correct + strategy_correct) / total_predictions
        
        print(f"验证结果:")
        print(f"  规则预测: {rule_accuracy:.3f} ({rule_correct}/{rule_predictions})")
        print(f"  策略预测: {strategy_accuracy:.3f} ({strategy_correct}/{strategy_predictions})")
        print(f"  总体准确率: {overall_accuracy:.3f} ({rule_correct + strategy_correct}/{total_predictions})")
        
        return {
            'rule_accuracy': rule_accuracy,
            'strategy_accuracy': strategy_accuracy,
            'overall_accuracy': overall_accuracy,
            'rule_predictions': rule_predictions,
            'strategy_predictions': strategy_predictions,
            'total_predictions': total_predictions,
            'prediction_log': prediction_log[-50:]  # 最后50个预测记录
        }
    
    def real_time_prediction(self, rules: Dict, strategies: Dict, count: int = 30) -> List[Dict]:
        """实时预测系统"""
        print(f"\n=== 实时预测系统 (预测 {count} 个值) ===")
        
        predictions = []
        current_sequence = self.sequence.copy()
        
        for step in range(count):
            predicted = None
            confidence = 0
            method = None
            alternatives = []
            
            # 1. 尝试规则预测
            for rule in rules['rules']:
                condition_length = rule['length']
                if len(current_sequence) >= condition_length:
                    current_condition = tuple(current_sequence[-condition_length:])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        confidence = rule['confidence']
                        method = f"rule_{condition_length}元"
                        
                        # 记录备选方案
                        for next_val, count_val in rule['all_outcomes'].items():
                            if next_val != predicted:
                                alt_confidence = count_val / rule['support']
                                if alt_confidence >= 0.15:  # 备选置信度阈值
                                    alternatives.append({
                                        'value': next_val,
                                        'confidence': alt_confidence
                                    })
                        break
            
            # 2. 备用策略
            if predicted is None:
                position_mod = len(current_sequence) % 8
                if position_mod in strategies['position_pattern']:
                    predicted = strategies['position_pattern'][position_mod]['most_common']
                    confidence = strategies['position_pattern'][position_mod]['confidence']
                    method = "位置模式"
                else:
                    predicted = strategies['recent_trend']['value']
                    confidence = strategies['recent_trend']['confidence']
                    method = "最近趋势"
            
            # 3. 最终安全检查
            if predicted is None or predicted < 1 or predicted > 8:
                predicted = strategies['global_frequency']['value']
                confidence = strategies['global_frequency']['confidence']
                method = "全局频率"
            
            prediction_info = {
                'step': step + 1,
                'predicted_value': predicted,
                'confidence': confidence,
                'method': method,
                'alternatives': alternatives,
                'sequence_position': len(current_sequence)
            }
            
            predictions.append(prediction_info)
            current_sequence.append(predicted)
            
            # 显示预测详情
            if step < 20:
                alt_str = f", 备选: {len(alternatives)}个" if alternatives else ""
                print(f"  步骤 {step+1:2d}: 预测 {predicted} "
                      f"(置信度: {confidence:.3f}, 方法: {method}{alt_str})")
        
        return predictions
    
    def save_prediction_model(self, rules: Dict, strategies: Dict, filename: str = "prediction_model.json"):
        """保存预测模型"""
        model_data = {
            'rules': rules['rules'][:100],  # 保存前100个最佳规则
            'strategies': strategies,
            'model_info': {
                'training_samples': self.n,
                'total_rules': len(rules['rules']),
                'creation_time': time.time()
            }
        }
        
        # 转换为可序列化格式
        serializable_data = json.dumps(model_data, indent=2, default=str)
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(serializable_data)
        
        print(f"\n预测模型已保存到: {filename}")
    
    def run_practical_system(self) -> Dict:
        """运行实战系统"""
        print("开始构建实战预测系统...")
        print(f"基于 {self.n} 个样本")
        print()
        
        # 1. 提取实用规则
        rules = self.extract_practical_rules()
        
        # 2. 构建备用策略
        strategies = self.build_backup_strategies()
        
        # 3. 综合验证
        validation = self.comprehensive_validation(rules, strategies)
        
        # 4. 实时预测
        predictions = self.real_time_prediction(rules, strategies, 30)
        
        # 5. 保存模型
        self.save_prediction_model(rules, strategies)
        
        return {
            'rules': rules,
            'strategies': strategies,
            'validation': validation,
            'predictions': predictions
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    import time
    
    sequence = load_sequence("随机23607.txt")
    system = PracticalPredictionSystem(sequence)
    results = system.run_practical_system()
    
    print(f"\n=== 实战预测系统总结 ===")
    print(f"实用规则数量: {len(results['rules']['rules'])}")
    print(f"验证总体准确率: {results['validation']['overall_accuracy']:.3f}")
    print(f"规则预测准确率: {results['validation']['rule_accuracy']:.3f}")
    print(f"策略预测准确率: {results['validation']['strategy_accuracy']:.3f}")
    
    # 提取预测值
    predicted_values = [p['predicted_value'] for p in results['predictions']]
    print(f"\n实时预测的30个值: {predicted_values}")
    
    # 统计预测方法
    method_counts = Counter(p['method'] for p in results['predictions'])
    print(f"\n预测方法统计: {dict(method_counts)}")
    
    # 高置信度预测
    high_conf_predictions = [p for p in results['predictions'] if p['confidence'] >= 0.4]
    print(f"\n中高置信度预测 (≥40%): {len(high_conf_predictions)} 个")
    
    for pred in high_conf_predictions:
        print(f"  步骤 {pred['step']}: {pred['predicted_value']} "
              f"(置信度: {pred['confidence']:.3f}, 方法: {pred['method']})")
    
    if results['validation']['overall_accuracy'] > 0.4:
        print(f"\n🎯 实战系统构建成功！")
        print(f"   总体准确率: {results['validation']['overall_accuracy']:.1%}")
        print(f"   已具备实际应用价值！")
    else:
        print(f"\n📊 系统准确率: {results['validation']['overall_accuracy']:.1%}")
    
    print(f"\n💡 基于23607样本的实战预测系统已就绪！")
