# 🎯 LCG真实投注系统使用指南 v2.1

## 🚀 系统概述

**LCG真实投注系统 v2.1** 是在原有LCG随机投注系统基础上增强的版本，集成了真实游戏投注功能。系统结合了：

- 🧮 **LCG最优随机算法** - 88.21%历史验证避开率
- 🎯 **真实API投注功能** - 连接真实游戏API
- 🤖 **智能投注处理器** - 自动分解投注金额
- 📈 **盈利策略引擎** - 动态风险控制
- 📊 **实时记录系统** - 完整投注记录

## ⚠️ 重要警告

**使用真实投注功能前请务必阅读:**

1. **资金风险**: 真实投注将使用您的真实资金，存在损失风险
2. **测试建议**: 建议先使用小额资金进行测试
3. **风险控制**: 系统内置多层风险控制，但无法完全避免损失
4. **理性投注**: 请理性对待投注，不要超出您的承受能力
5. **法律合规**: 请确保在您所在地区的法律框架内使用

## 🎯 新增功能特性

### 1. 双模式支持
- **模拟投注模式**: 安全的演示和测试模式
- **真实投注模式**: 连接真实API进行投注

### 2. 智能投注处理
- **金额分解算法**: 自动将目标金额分解为API支持的面额
- **多次投注管理**: 智能处理多次API调用
- **投注结果验证**: 实时验证投注是否成功

### 3. 增强风险控制
- **连败保护**: 连续失败自动停止
- **时间段控制**: 高风险时段智能跳过
- **余额保护**: 余额不足自动停止
- **置信度控制**: 低置信度预测跳过投注

### 4. 实时监控系统
- **游戏状态监控**: 实时获取游戏状态
- **自动投注时机**: 智能判断最佳投注时机
- **开奖结果跟踪**: 自动获取开奖结果并分析

## 🚀 快速开始

### 1. 系统要求

```bash
Python 3.9+
依赖包: requests, numpy, json, datetime, collections
```

### 2. 启动系统

```bash
python lcg_betting_system_main.py
```

### 3. 选择投注模式

系统启动后会提示选择投注模式:

```
🎯 选择投注模式:
1. 模拟投注 - 安全的演示模式
2. 真实投注 - 连接真实API进行投注 ⚠️

请选择投注模式 (1-2): 
```

**建议新用户先选择模拟投注模式进行测试**

### 4. 选择运行模式

根据投注模式，系统提供不同的运行模式:

#### 模拟投注模式:
1. **交互模式** - 手动控制投注
2. **演示模式** - 自动运行20期展示功能
3. **快速测试** - 运行测试并生成报告
4. **长期运行模式** - 持续执行投注直到停止条件
5. **持续监控模式** - 等待开奖信号自动投注

#### 真实投注模式 (额外):
6. **真实投注监控模式** - 连接真实API自动投注

## 🎮 使用模式详解

### 模式1: 交互模式 (推荐新手)

手动控制每次投注，适合学习和测试:

```bash
[期号124000] 请输入命令: bet      # 执行单次投注
[期号124001] 请输入命令: auto 5   # 自动投注5期
[期号124006] 请输入命令: report  # 生成报告
[期号124006] 请输入命令: quit    # 退出系统
```

### 模式6: 真实投注监控模式 (高级用户)

**⚠️ 仅在真实投注模式下可用**

- 自动连接真实API
- 实时监控游戏状态
- 智能判断投注时机
- 自动执行投注和结果分析

**工作流程:**
1. 连接游戏API获取状态
2. 判断是否为投注时机 (倒计时15-30秒)
3. 使用LCG算法选择房间
4. 计算动态投注金额
5. 执行智能投注
6. 等待开奖结果
7. 分析盈亏并更新统计

## 🔧 系统配置

### API配置 (真实投注模式)

系统内置真实API配置，包括:
- API地址和认证信息
- 请求头和签名
- 超时和重试设置

### 投注配置

```python
default_config = {
    'base_bet_amount': 1.0,           # 基础投注金额
    'max_bet_amount': 10.0,           # 最大投注金额
    'max_consecutive_losses': 5,       # 最大连续失败次数
    'max_daily_loss': 20.0,           # 日最大损失
    'stop_loss_percentage': 0.3,       # 止损百分比
    'initial_balance': 100.0          # 初始余额
}
```

## 📊 真实投注示例

### 投注执行过程

```
🎯 真实投注模式 - 第124001期
📊 游戏状态: 期号124001, 状态1, 倒计时25秒
🎲 LCG选择房间: 3
💰 投注金额: 2.50元

💰 智能投注执行:
   房间: 3
   目标金额: 2.50元
   金额分解: [1元×2次, 0.5元×1次]

✅ 投注成功: 房间3, 金额2.50元
⏳ 等待第124001期开奖结果...
🎰 第124001期开奖: 房间7
🎉 投注获胜! 盈利0.25元
```

### 风险控制示例

```
⚠️ 风险控制触发:
   连续失败: 3次 (限制5次)
   当前余额: 85.50元 (初始100元)
   风险等级: 中等
   
💡 系统建议: 降低投注金额至1.0元
```

## 📈 性能监控

### 实时统计

系统提供实时统计信息:
- 总投注次数和获胜次数
- 实际胜率 vs 预期胜率 (88.21%)
- 总盈亏和连胜连败记录
- 风险事件和系统状态

### 报告生成

系统自动生成详细报告:
- **Markdown报告**: 包含完整分析和建议
- **JSON数据**: 原始投注和结果数据
- **实时日志**: 详细的投注过程记录

## 🛡️ 安全建议

### 资金管理
1. **小额测试**: 首次使用建议投注金额不超过10元
2. **设置止损**: 严格遵守止损线，避免过度损失
3. **分散风险**: 不要将所有资金用于单一策略

### 使用建议
1. **充分测试**: 先在模拟模式下充分测试
2. **理解风险**: 充分理解投注风险和系统限制
3. **定期检查**: 定期检查系统状态和投注记录
4. **及时止损**: 达到损失限制时及时停止

## 🔍 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证API配置信息
   - 确认账户状态

2. **投注失败**
   - 检查账户余额
   - 验证投注金额是否符合限制
   - 确认游戏状态是否正常

3. **系统异常**
   - 查看错误日志
   - 重启系统
   - 联系技术支持

### 技术支持

如遇技术问题，请提供:
- 错误信息和日志
- 系统配置信息
- 复现步骤

## 📚 更新日志

### v2.1 新增功能
- ✅ 真实API投注功能
- ✅ 智能投注处理器
- ✅ 增强风险控制
- ✅ 实时监控系统
- ✅ 双模式支持

### v2.0 基础功能
- ✅ LCG最优随机算法
- ✅ 动态金额管理
- ✅ 实时记录功能
- ✅ Markdown报告生成
- ✅ 多层风险控制

---

**🎯 LCG真实投注系统 v2.1**  
*集成真实投注功能的智能投注解决方案*  
*请谨慎使用真实投注功能，理性对待投注风险*
