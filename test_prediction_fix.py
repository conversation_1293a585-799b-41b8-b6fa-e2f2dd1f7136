#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预测修复
验证预测适配器是否正常工作
"""

from prediction_strategy_adapter import PredictionRuleAdapter

def test_prediction_adapter():
    """测试预测适配器"""
    
    print("🧪 测试预测适配器修复")
    print("=" * 50)
    
    # 创建适配器
    adapter = PredictionRuleAdapter()
    
    # 测试不同的历史数据
    test_cases = [
        [1, 2, 3, 4, 5, 6, 7, 8],
        [8, 7, 6, 5, 4, 3, 2, 1],
        [1, 1, 1, 2, 2, 2, 3, 3],
        [5, 7, 8, 4],  # 应该匹配100%置信度规则
        [6, 6, 5, 1],  # 应该匹配100%置信度规则
        [2, 7, 6],     # 应该匹配95%置信度规则
        [1, 4, 7],     # 应该匹配87%置信度规则
    ]
    
    successful_predictions = 0
    
    for i, history in enumerate(test_cases, 1):
        print(f"\n--- 测试用例{i} ---")
        print(f"历史数据: {history}")
        
        # 测试不同的置信度阈值
        for min_confidence in [0.6, 0.7, 0.8, 0.9]:
            prediction = adapter.predict_next_room(history, min_confidence)
            
            if prediction:
                print(f"✅ 置信度≥{min_confidence}: 预测房间{prediction['predicted_room']} (置信度{prediction['confidence']:.2f}, {prediction['rule_type']})")
                if min_confidence == 0.6:  # 只计算最低阈值的成功次数
                    successful_predictions += 1
                break
            else:
                print(f"❌ 置信度≥{min_confidence}: 无匹配规则")
    
    print(f"\n📊 测试结果汇总:")
    print(f"   总测试用例: {len(test_cases)}个")
    print(f"   成功预测: {successful_predictions}个")
    print(f"   成功率: {successful_predictions/len(test_cases)*100:.1f}%")
    
    if successful_predictions > 0:
        print(f"✅ 预测适配器修复成功！")
        return True
    else:
        print(f"❌ 预测适配器仍有问题")
        return False

def test_with_real_history():
    """使用真实历史数据测试"""
    
    print(f"\n🎯 使用真实历史数据测试")
    print("=" * 50)
    
    # 模拟真实的历史数据
    real_history = [
        3, 7, 2, 8, 1, 5, 4, 6, 3, 7, 2, 8, 1, 5, 4, 6,
        2, 4, 6, 8, 1, 3, 5, 7, 2, 4, 6, 8, 1, 3, 5, 7
    ]
    
    adapter = PredictionRuleAdapter()
    
    # 测试不同长度的历史数据
    for length in [10, 15, 20, 25, 30]:
        if length <= len(real_history):
            history_slice = real_history[-length:]
            print(f"\n使用最近{length}期数据: {history_slice[-8:]}")  # 只显示最后8期
            
            prediction = adapter.predict_next_room(history_slice, 0.6)
            
            if prediction:
                print(f"✅ 预测成功: 房间{prediction['predicted_room']} (置信度{prediction['confidence']:.2f})")
                print(f"   规则类型: {prediction['rule_type']}")
                print(f"   匹配条件: {prediction['condition']}")
                print(f"   支持度: {prediction['support']}")
            else:
                print(f"❌ 无匹配规则")

if __name__ == "__main__":
    # 运行测试
    success = test_prediction_adapter()
    
    if success:
        test_with_real_history()
        
        print(f"\n🎊 预测适配器修复验证完成！")
        print(f"💡 现在新盈利系统应该能正常进行投注了")
        print(f"🚀 建议重新启动新盈利系统:")
        print(f"   python new_profitable_system.py")
    else:
        print(f"\n⚠️ 预测适配器仍需进一步修复")
