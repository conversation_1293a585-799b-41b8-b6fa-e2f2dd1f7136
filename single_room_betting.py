#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单房间投注系统
每期只投注一个最优房间，避免重复投注问题
"""

import time
import json
from datetime import datetime
from collections import Counter
from api_framework import GameAPIClient

def get_best_room_recommendation():
    """获取最佳单房间投注建议"""
    
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
    except:
        print("❌ 无法加载历史数据")
        return None
    
    if len(history) < 10:
        print("📊 历史数据不足")
        return None
    
    # 频率分析
    recent_data = history[-20:] if len(history) >= 20 else history
    counter = Counter(recent_data)
    
    # 计算投注价值 (1 - 出现频率)
    betting_values = {}
    for room in range(1, 9):
        frequency = counter.get(room, 0)
        betting_values[room] = 1 - (frequency / len(recent_data))
    
    # 找出最佳投注房间
    best_room = max(betting_values.keys(), key=lambda k: betting_values[k])
    best_value = betting_values[best_room]
    best_frequency = counter.get(best_room, 0)
    
    # 计算理论胜率 (不投注这个房间的概率)
    theoretical_win_rate = 7/8  # 87.5%
    
    return {
        'best_room': best_room,
        'best_value': best_value,
        'best_frequency': best_frequency,
        'frequency_distribution': dict(counter),
        'total_samples': len(recent_data),
        'theoretical_win_rate': theoretical_win_rate,
        'all_values': betting_values
    }

def execute_single_room_betting():
    """执行单房间投注"""
    
    print("🎯 单房间投注系统")
    print("=" * 50)
    print("每期只投注一个最优房间")
    print()
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    # 创建API客户端
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    
    last_bet_issue = 0
    betting_records = []
    
    print("🚀 开始单房间投注监控...")
    print("按 Ctrl+C 停止")
    print()
    
    try:
        while True:
            # 获取游戏状态
            state = api_client.get_game_state()
            
            if state:
                current_time = datetime.now().strftime('%H:%M:%S')
                
                if state.state == 1:  # 等待开奖状态
                    print(f"📊 [{current_time}] 期号: {state.issue}, 倒计时: {state.countdown}秒")
                    
                    # 投注时机：倒计时在15-25秒之间，且未投注过此期
                    if 15 <= state.countdown <= 25 and state.issue > last_bet_issue:
                        print(f"🎯 单房间投注时机到达！期号{state.issue}")
                        
                        # 获取最佳房间建议
                        recommendation = get_best_room_recommendation()
                        
                        if recommendation:
                            best_room = recommendation['best_room']
                            best_value = recommendation['best_value']
                            best_frequency = recommendation['best_frequency']
                            win_rate = recommendation['theoretical_win_rate']
                            
                            print(f"💰 单房间投注策略:")
                            print(f"   最佳房间: {best_room}")
                            print(f"   投注价值: {best_value:.3f}")
                            print(f"   出现频率: {best_frequency}次/{recommendation['total_samples']}期")
                            print(f"   理论胜率: {win_rate*100:.1f}%")
                            print(f"   频率分布: {recommendation['frequency_distribution']}")
                            
                            bet_amount = 0.1
                            
                            # 执行单房间投注
                            print(f"🎯 投注房间{best_room}，金额{bet_amount:.2f}")
                            
                            bet_result = api_client.place_bet(best_room, bet_amount)
                            
                            if bet_result.success:
                                last_bet_issue = state.issue
                                
                                # 记录投注信息
                                bet_record = {
                                    'issue': state.issue,
                                    'room': best_room,
                                    'amount': bet_amount,
                                    'timestamp': current_time,
                                    'frequency': best_frequency,
                                    'value': best_value,
                                    'countdown': state.countdown
                                }
                                betting_records.append(bet_record)
                                
                                print(f"✅ 房间{best_room}投注成功")
                                print(f"📝 投注记录已保存")
                                
                                # 显示投注响应详情
                                if hasattr(bet_result, 'data') and bet_result.data:
                                    response_data = bet_result.data
                                    print(f"📊 投注响应详情:")
                                    print(f"   消耗金额: {response_data.get('costMedal', 'N/A')}")
                                    print(f"   剩余余额: {response_data.get('myMedal', 'N/A')}")
                                    print(f"   投注房间: {response_data.get('roomNumber', 'N/A')}")
                                    print(f"   总投注量: {response_data.get('totalBuyQuantity', 'N/A')}")
                            else:
                                print(f"❌ 房间{best_room}投注失败: {bet_result.message}")
                        else:
                            print("📊 无法获取投注建议")
                    
                elif state.state == 2:  # 已开奖状态
                    print(f"🟢 [{current_time}] 期号: {state.issue}, 开出房间: {state.kill_number}")
                    
                    # 检查最近的投注结果
                    if betting_records:
                        last_bet = betting_records[-1]
                        if last_bet['issue'] == state.issue and 'result' not in last_bet:
                            # 判断投注结果
                            bet_room = last_bet['room']
                            actual_room = state.kill_number
                            
                            if bet_room != actual_room:
                                result = "获胜"
                                profit = last_bet['amount'] * 0.1  # 假设10%收益
                                print(f"🎉 投注结果: {result}! 投注房间{bet_room}, 开出房间{actual_room}")
                                print(f"💰 预期收益: +{profit:.2f}")
                            else:
                                result = "失败"
                                loss = last_bet['amount']
                                print(f"😞 投注结果: {result}. 投注房间{bet_room}, 开出房间{actual_room}")
                                print(f"💸 损失: -{loss:.2f}")
                            
                            # 更新记录
                            last_bet['result'] = result
                            last_bet['actual_room'] = actual_room
                            last_bet['profit'] = profit if result == "获胜" else -loss
                
                # 等待3秒
                time.sleep(3)
            else:
                print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] 无法获取游戏状态")
                time.sleep(5)
                
    except KeyboardInterrupt:
        print("\n🛑 停止单房间投注监控")
    
    # 显示投注统计
    if betting_records:
        print(f"\n📊 投注统计:")
        total_bets = len(betting_records)
        completed_bets = [bet for bet in betting_records if 'result' in bet]
        wins = [bet for bet in completed_bets if bet['result'] == '获胜']
        
        print(f"   总投注次数: {total_bets}")
        print(f"   已完成: {len(completed_bets)}")
        print(f"   获胜次数: {len(wins)}")
        
        if completed_bets:
            win_rate = len(wins) / len(completed_bets)
            total_profit = sum(bet.get('profit', 0) for bet in completed_bets)
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   总盈亏: {total_profit:.2f}")
        
        # 保存投注记录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"single_room_betting_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(betting_records, f, indent=2, ensure_ascii=False)
            print(f"📝 投注记录已保存: {filename}")
        except Exception as e:
            print(f"❌ 保存记录失败: {e}")
    
    print("✅ 单房间投注系统已停止")

def main():
    """主函数"""
    
    print("🎯 单房间投注系统")
    print("=" * 50)
    print("解决多房间投注问题，每期只投注一个最优房间")
    print()
    
    # 检查历史数据
    try:
        with open("game_history.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            history = data.get('history', [])
        
        print(f"✅ 历史数据: {len(history)}期")
        if len(history) >= 10:
            print(f"📊 最近10期: {history[-10:]}")
            
            # 显示当前最佳投注建议
            recommendation = get_best_room_recommendation()
            if recommendation:
                best_room = recommendation['best_room']
                best_value = recommendation['best_value']
                best_frequency = recommendation['best_frequency']
                win_rate = recommendation['theoretical_win_rate']
                
                print(f"🎯 当前最佳投注房间: {best_room}")
                print(f"📊 投注价值: {best_value:.3f}")
                print(f"📈 出现频率: {best_frequency}次/{recommendation['total_samples']}期")
                print(f"🎲 理论胜率: {win_rate*100:.1f}%")
                print(f"📊 频率分布: {recommendation['frequency_distribution']}")
        else:
            print("⚠️  历史数据不足，无法生成投注建议")
            return
            
    except Exception as e:
        print(f"❌ 无法加载历史数据: {e}")
        return
    
    print(f"\n💡 单房间投注策略:")
    print("• 每期只投注一个房间")
    print("• 选择出现频率最低的房间")
    print("• 在倒计时15-25秒时投注")
    print("• 理论胜率87.5% (7/8)")
    print("• 避免重复投注问题")
    
    response = input("\n是否启动单房间投注系统？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        execute_single_room_betting()
    else:
        print("系统未启动")

if __name__ == "__main__":
    main()
