#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整的增强投注金额日志记录功能
"""

import os
import json
from datetime import datetime
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem
from real_time_logger import log_result

def test_complete_enhanced_logging():
    """测试完整的增强投注金额日志记录功能"""
    
    print("🧪 测试完整的增强投注金额日志记录功能")
    print("=" * 60)
    
    # 创建模拟API客户端
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return None
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 创建系统实例
    api_client = MockAPIClient()
    config = {
        'base_bet_amount': 2,
        'max_bet_amount': 20,
        'min_bet_amount': 1,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50,
        'initial_balance': 200,
        'auto_report_interval': 10,
        'risk_monitoring': True,
        'real_time_logging': True
    }
    
    print("🎯 创建系统实例...")
    system = OptimizedRandomBettingSystem(api_client, config)
    
    # 设置测试状态 - 连胜3次，高胜率
    print("\n📊 设置测试状态...")
    system.consecutive_wins = 3
    system.consecutive_losses = 0
    system.total_bets = 15
    system.total_wins = 14  # 93.3%胜率，触发算法奖励
    
    print(f"   连续获胜: {system.consecutive_wins}次")
    print(f"   连续失败: {system.consecutive_losses}次")
    print(f"   总投注: {system.total_bets}次")
    print(f"   总获胜: {system.total_wins}次")
    print(f"   胜率: {system.total_wins/system.total_bets*100:.1f}%")
    
    print("\n" + "=" * 60)
    
    # 执行完整的投注流程
    print("🎲 执行完整的投注流程...")
    
    current_issue = 130555
    
    # 1. 执行投注
    print(f"\n📍 第{current_issue}期投注...")
    bet_info = system.execute_optimized_bet(current_issue)
    
    if bet_info:
        print(f"✅ 投注成功")
        print(f"   期号: {bet_info['issue']}")
        print(f"   房间: {bet_info['room']}")
        print(f"   金额: {bet_info['amount']}元")
        
        # 2. 模拟开奖结果
        print(f"\n🎰 模拟开奖结果...")
        winning_room = 1  # 假设开奖房间1
        result = "获胜" if bet_info['room'] != winning_room else "失败"
        profit = bet_info['amount'] * 0.1 if result == "获胜" else -bet_info['amount']
        
        print(f"   开奖房间: {winning_room}")
        print(f"   投注结果: {result}")
        print(f"   盈亏: {profit:+.2f}元")
        
        # 3. 记录开奖结果 (这会将记录标记为completed)
        log_result(current_issue, winning_room, result, profit)
        
        # 4. 处理系统状态更新
        system.process_result(current_issue, winning_room)
        
    else:
        print("❌ 投注失败")
        return
    
    print("\n" + "=" * 60)
    
    # 检查生成的日志文件
    print("📂 检查生成的日志文件...")
    
    # 查找最新的投注日志文件
    log_files = []
    for filename in os.listdir('.'):
        if filename.startswith('betting_log_') and filename.endswith('.md'):
            log_files.append(filename)
    
    if log_files:
        latest_log = sorted(log_files)[-1]
        print(f"✅ 找到日志文件: {latest_log}")
        
        # 读取并显示相关内容
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 日志文件内容:")
            print("=" * 40)
            print(content)
            print("=" * 40)
            
            # 查找投注金额详情
            if "动态计算" in content:
                print(f"\n✅ 找到动态金额计算详情!")
                
                # 提取动态计算相关的行
                lines = content.split('\n')
                in_amount_section = False
                amount_details = []
                
                for line in lines:
                    if "投注金额" in line and "动态计算" in line:
                        in_amount_section = True
                        amount_details.append(line)
                    elif in_amount_section and (line.strip().startswith('  - ') or line.strip().startswith('- **')):
                        amount_details.append(line)
                    elif in_amount_section and line.strip() == '':
                        continue
                    elif in_amount_section:
                        break
                
                if amount_details:
                    print(f"\n💰 投注金额详细计算:")
                    for detail in amount_details:
                        print(f"   {detail.strip()}")
                else:
                    print(f"⚠️ 未找到详细计算信息")
            else:
                print(f"⚠️ 日志中未包含动态计算详情")
                
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("❌ 未找到日志文件")
    
    print("\n" + "=" * 60)
    
    # 测试第二次投注 - 连败场景
    print("🔄 测试第二次投注 - 连败场景...")
    
    # 设置连败状态
    system.consecutive_wins = 0
    system.consecutive_losses = 2
    
    current_issue = 130556
    
    print(f"\n📍 第{current_issue}期投注 (连败2次)...")
    bet_info = system.execute_optimized_bet(current_issue)
    
    if bet_info:
        print(f"✅ 投注成功")
        print(f"   期号: {bet_info['issue']}")
        print(f"   房间: {bet_info['room']}")
        print(f"   金额: {bet_info['amount']}元 (应该包含马丁格尔调整)")
        
        # 模拟开奖 - 这次获胜
        winning_room = bet_info['room'] + 1 if bet_info['room'] < 8 else 1  # 确保不同房间
        result = "获胜"
        profit = bet_info['amount'] * 0.1
        
        print(f"   开奖房间: {winning_room}")
        print(f"   投注结果: {result}")
        print(f"   盈亏: {profit:+.2f}元")
        
        # 记录结果
        log_result(current_issue, winning_room, result, profit)
        system.process_result(current_issue, winning_room)
    
    print("\n" + "=" * 60)
    
    # 最终检查日志文件
    print("📋 最终检查日志文件...")
    
    if log_files:
        latest_log = sorted([f for f in os.listdir('.') if f.startswith('betting_log_') and f.endswith('.md')])[-1]
        
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 统计动态计算的出现次数
            dynamic_count = content.count("动态计算")
            martingale_count = content.count("马丁格尔")
            win_bonus_count = content.count("连胜奖励")
            algo_bonus_count = content.count("算法奖励")
            risk_adjustment_count = content.count("风险调整")
            
            print(f"📊 日志统计:")
            print(f"   动态计算记录: {dynamic_count}次")
            print(f"   马丁格尔调整: {martingale_count}次")
            print(f"   连胜奖励: {win_bonus_count}次")
            print(f"   算法奖励: {algo_bonus_count}次")
            print(f"   风险调整: {risk_adjustment_count}次")
            
            if dynamic_count >= 2:
                print(f"✅ 成功记录了多次动态金额计算!")
            else:
                print(f"⚠️ 动态金额计算记录可能不完整")
                
        except Exception as e:
            print(f"❌ 读取最终日志失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成!")
    print("✅ 增强的投注金额日志记录功能测试完成")
    print("✅ 支持马丁格尔、连胜奖励、算法奖励、风险调整等详细显示")
    print("✅ 实时投注记录中包含完整的动态金额计算过程")
    print(f"✅ 日志文件: {latest_log if 'latest_log' in locals() else '未生成'}")

if __name__ == "__main__":
    test_complete_enhanced_logging()
