时间,期号,预测房间,预测置信度,规则类型,可选房间,投注房间,选择策略,投注金额,开奖房间,投注结果,盈亏,累计盈亏,胜率
07:32:57,125452,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",4,频率最低,1.00,8,获胜,+0.10,+0.10,100.0%
07:34:33,125453,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",4,频率相同，选择中位数,1.00,2,获胜,+0.10,+0.20,100.0%
07:55:23,125466,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,8,获胜,+0.10,+0.30,100.0%
08:09:04,125475,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,8,获胜,+0.10,+0.40,100.0%
08:15:12,125479,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率最低,1.00,8,获胜,+0.10,+0.50,100.0%
08:16:41,125480,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,2,获胜,+0.10,+0.60,100.0%
08:28:53,125488,1,1.000,dynamic,"[2, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,8,获胜,+0.10,+0.70,100.0%
08:32:00,125490,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",3,频率最低,1.00,8,获胜,+0.10,+0.80,100.0%
08:42:52,125497,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",3,频率相同，选择中位数,1.00,6,获胜,+0.10,+0.90,100.0%
08:50:31,125502,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,6,获胜,+0.10,+1.00,100.0%
08:52:00,125503,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",7,频率最低,1.00,8,获胜,+0.10,+1.10,100.0%
09:01:14,125509,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,7,失败,-1.00,+0.10,91.7%
09:25:37,125525,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,6,失败,-1.00,-0.90,84.6%
09:31:38,125529,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",3,频率最低,1.00,8,获胜,+0.10,-0.80,85.7%
09:33:07,125530,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.70,86.7%
09:50:06,125541,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",5,频率最低,1.00,1,获胜,+0.10,-0.60,87.5%
10:14:09,125557,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.50,88.2%
10:47:42,125579,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",3,频率相同，选择中位数,1.00,1,获胜,+0.10,-0.40,88.9%
10:55:30,125584,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",5,频率最低,1.00,6,获胜,+0.10,-0.30,89.5%
11:01:34,125588,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",6,频率相同，选择中位数,1.00,1,获胜,+0.10,-0.20,90.0%
11:03:08,125589,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率最低,1.00,7,获胜,+0.10,-0.10,90.5%
11:16:39,125598,7,0.833,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.00,90.9%
11:25:45,125604,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,+0.10,91.3%
11:42:27,125615,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,+0.20,91.7%
11:49:52,125620,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,4,获胜,+0.10,+0.30,92.0%
12:12:56,125635,7,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率相同，选择中位数,1.00,7,获胜,+0.10,+0.40,92.3%
12:14:26,125636,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,1,获胜,+0.10,+0.50,92.6%
12:17:34,125638,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,6,获胜,+0.10,+0.60,92.9%
12:33:59,125649,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",3,频率相同，选择中位数,1.00,2,获胜,+0.10,+0.70,93.1%
12:35:33,125650,4,0.750,dynamic,"[1, 2, 3, 5, 6, 7, 8]",3,频率最低,1.00,2,获胜,+0.10,+0.80,93.3%
12:39:58,125653,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",4,频率最低,1.00,2,获胜,+0.10,+0.90,93.5%
12:53:28,125662,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率最低,1.00,5,获胜,+0.10,+1.00,93.8%
13:11:18,125674,4,0.667,dynamic,"[1, 2, 3, 5, 6, 7, 8]",6,频率最低,1.00,5,获胜,+0.10,+1.10,93.9%
13:15:46,125677,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",1,频率最低,1.00,8,获胜,+0.10,+1.20,94.1%
13:17:15,125678,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",1,频率最低,1.00,6,获胜,+0.10,+1.30,94.3%
13:18:47,125679,7,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 8]",4,频率相同，选择中位数,1.00,4,失败,-1.00,+0.30,91.7%
13:24:50,125683,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",2,频率相同，选择中位数,1.00,1,获胜,+0.10,+0.40,91.9%
13:26:24,125684,4,0.750,static,"[1, 2, 3, 5, 6, 7, 8]",2,频率最低,1.00,2,失败,-1.00,-0.60,89.5%
13:27:52,125685,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",4,频率相同，选择中位数,1.00,5,获胜,+0.10,-0.50,89.7%
13:38:23,125692,4,0.667,dynamic,"[1, 2, 3, 5, 6, 7, 8]",1,频率最低,1.00,6,获胜,+0.10,-0.40,90.0%
13:44:22,125696,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",4,频率最低,1.00,8,获胜,+0.10,-0.30,90.2%
14:00:55,125707,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",6,频率相同，选择中位数,1.00,8,获胜,+0.10,-0.20,90.5%
14:08:33,125712,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",7,频率最低,1.00,2,获胜,+0.10,-0.10,90.7%
14:13:09,125715,7,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 8]",6,频率最低,1.00,5,获胜,+0.10,-0.00,90.9%
14:31:09,125727,2,0.750,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率最低,1.00,5,获胜,+0.10,+0.10,91.1%
14:37:01,125731,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",6,频率相同，选择中位数,1.00,7,获胜,+0.10,+0.20,91.3%
14:47:31,125738,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",6,频率最低,1.00,2,获胜,+0.10,+0.30,91.5%
14:50:38,125740,8,0.750,dynamic,"[1, 2, 3, 4, 5, 6, 7]",6,频率最低,1.00,5,获胜,+0.10,+0.40,91.7%
15:11:47,125754,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,8,获胜,+0.10,+0.50,91.8%
15:36:01,125770,2,0.600,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率最低,1.00,2,获胜,+0.10,+0.60,92.0%
15:37:30,125771,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,6,获胜,+0.10,+0.70,92.2%
16:07:33,125791,8,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 7]",4,频率最低,1.00,1,获胜,+0.10,+0.80,92.3%
16:12:04,125794,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,4,失败,-1.00,-0.20,90.6%
16:13:35,125795,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.10,90.7%
16:15:04,125796,4,0.600,dynamic,"[1, 2, 3, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,3,获胜,+0.10,-0.00,90.9%
16:40:50,125813,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",8,频率最低,1.00,6,获胜,+0.10,+0.10,91.1%
16:42:20,125814,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",8,频率最低,1.00,3,获胜,+0.10,+0.20,91.2%
16:49:52,125819,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",7,频率最低,1.00,8,获胜,+0.10,+0.30,91.4%
16:52:55,125821,6,0.600,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,2,获胜,+0.10,+0.40,91.5%
16:54:24,125822,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,2,获胜,+0.10,+0.50,91.7%
17:09:35,125832,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",7,频率相同，选择中位数,1.00,6,获胜,+0.10,+0.60,91.8%
17:14:05,125835,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,5,获胜,+0.10,+0.70,91.9%
17:26:00,125843,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,7,获胜,+0.10,+0.80,92.1%
17:30:35,125846,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,4,获胜,+0.10,+0.90,92.2%
17:39:35,125852,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",3,频率相同，选择中位数,1.00,4,获胜,+0.10,+1.00,92.3%
18:21:35,125880,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",4,频率相同，选择中位数,1.00,3,获胜,+0.10,+1.10,92.4%
18:35:07,125889,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,1,获胜,+0.10,+1.20,92.5%
18:36:40,125890,7,0.750,dynamic,"[1, 2, 3, 4, 5, 6, 8]",4,频率相同，选择中位数,1.00,8,获胜,+0.10,+1.30,92.6%
18:47:09,125897,5,0.600,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率相同，选择中位数,1.00,1,获胜,+0.10,+1.40,92.8%
18:56:18,125903,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率相同，选择中位数,1.00,5,获胜,+0.10,+1.50,92.9%
19:00:52,125906,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",3,频率相同，选择中位数,1.00,2,获胜,+0.10,+1.60,93.0%
19:05:31,125909,7,0.720,static,"[1, 2, 3, 4, 5, 6, 8]",8,频率相同，选择中位数,1.00,3,获胜,+0.10,+1.70,93.1%
19:14:30,125915,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,7,获胜,+0.10,+1.80,93.2%
19:25:01,125922,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,7,失败,-1.00,+0.80,91.9%
19:26:29,125923,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",2,频率最低,1.00,7,获胜,+0.10,+0.90,92.0%
19:37:03,125930,7,0.810,static,"[1, 2, 3, 4, 5, 6, 8]",1,频率最低,1.00,5,获胜,+0.10,+1.00,92.1%
19:40:00,125932,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,7,获胜,+0.10,+1.10,92.2%
19:41:28,125933,3,0.710,static,"[1, 2, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,3,获胜,+0.10,+1.20,92.3%
19:50:28,125939,2,1.000,dynamic,"[1, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,5,获胜,+0.10,+1.30,92.4%
20:09:58,125952,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率最低,1.00,3,获胜,+0.10,+1.40,92.5%
20:14:28,125955,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,7,获胜,+0.10,+1.50,92.6%
20:31:01,125966,3,0.710,static,"[1, 2, 4, 5, 6, 7, 8]",4,频率最低,1.00,5,获胜,+0.10,+1.60,92.7%
20:51:45,125980,7,0.750,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率相同，选择中位数,1.00,7,获胜,+0.10,+1.70,92.8%
20:53:13,125981,3,0.710,static,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,8,获胜,+0.10,+1.80,92.9%
20:54:47,125982,1,0.730,static,"[2, 3, 4, 5, 6, 7, 8]",2,频率最低,1.00,1,获胜,+0.10,+1.90,92.9%
20:56:20,125983,2,0.600,dynamic,"[1, 3, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,2,获胜,+0.10,+2.00,93.0%
20:57:48,125984,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",3,频率相同，选择中位数,1.00,1,获胜,+0.10,+2.10,93.1%
21:17:28,125997,5,0.667,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率最低,1.00,6,获胜,+0.10,+2.20,93.2%
21:44:19,126015,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",1,频率最低,1.00,8,获胜,+0.10,+2.30,93.3%
21:50:12,126019,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",1,频率最低,1.00,2,获胜,+0.10,+2.40,93.3%
21:57:41,126024,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",6,频率最低,1.00,4,获胜,+0.10,+2.50,93.4%
22:00:35,126026,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",1,频率最低,1.00,4,获胜,+0.10,+2.60,93.5%
22:02:03,126027,4,0.667,dynamic,"[1, 2, 3, 5, 6, 7, 8]",6,频率最低,1.00,5,获胜,+0.10,+2.70,93.5%
22:04:56,126029,2,0.760,static,"[1, 3, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,3,获胜,+0.10,+2.80,93.6%
22:33:03,126048,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",8,频率相同，选择中位数,1.00,7,获胜,+0.10,+2.90,93.7%
22:44:54,126056,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",4,频率最低,1.00,8,获胜,+0.10,+3.00,93.8%
23:15:06,126076,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",7,频率最低,1.00,2,获胜,+0.10,+3.10,93.8%
23:49:05,126099,6,1.000,dynamic,"[1, 2, 3, 4, 5, 7, 8]",8,频率最低,1.00,7,获胜,+0.10,+3.20,93.9%
23:56:34,126104,5,1.000,dynamic,"[1, 2, 3, 4, 6, 7, 8]",8,频率最低,1.00,8,失败,-1.00,+2.20,92.9%
00:07:07,126111,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,4,获胜,+0.10,+2.30,93.0%
00:10:06,126113,2,0.870,static,"[1, 3, 4, 5, 6, 7, 8]",5,频率最低,1.00,5,失败,-1.00,+1.30,92.1%
00:14:36,126116,3,0.710,static,"[1, 2, 4, 5, 6, 7, 8]",2,频率相同，选择中位数,1.00,1,获胜,+0.10,+1.40,92.2%
00:25:06,126123,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",4,频率相同，选择中位数,1.00,6,获胜,+0.10,+1.50,92.2%
00:50:32,126140,8,0.750,dynamic,"[1, 2, 3, 4, 5, 6, 7]",2,频率最低,1.00,4,获胜,+0.10,+1.60,92.3%
00:59:28,126146,6,0.750,dynamic,"[1, 2, 3, 4, 5, 7, 8]",2,频率最低,1.00,4,获胜,+0.10,+1.70,92.4%
01:14:40,126156,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",5,频率相同，选择中位数,1.00,1,获胜,+0.10,+1.80,92.5%
01:19:14,126159,8,1.000,static,"[1, 2, 3, 4, 5, 6, 7]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,+1.90,92.5%
01:34:17,126169,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",3,频率最低,1.00,5,获胜,+0.10,+2.00,92.6%
01:35:48,126170,7,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 8]",3,频率最低,1.00,3,失败,-1.00,+1.00,91.7%
01:43:34,126175,6,0.750,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,4,获胜,+0.10,+1.10,91.8%
01:54:21,126182,7,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 8]",1,频率最低,1.00,1,失败,-1.00,+0.10,91.0%
02:00:26,126186,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,1,获胜,+0.10,+0.20,91.1%
02:03:37,126188,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",7,频率相同，选择中位数,1.00,5,获胜,+0.10,+0.30,91.2%
02:14:19,126195,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",2,频率最低,1.00,6,获胜,+0.10,+0.40,91.2%
02:17:19,126197,1,0.600,dynamic,"[2, 3, 4, 5, 6, 7, 8]",2,频率最低,1.00,7,获胜,+0.10,+0.50,91.3%
02:40:23,126212,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,2,获胜,+0.10,+0.60,91.4%
02:48:13,126217,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,7,获胜,+0.10,+0.70,91.5%
03:11:37,126232,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",7,频率相同，选择中位数,1.00,7,失败,-1.00,-0.30,90.7%
03:16:23,126235,4,1.000,dynamic,"[1, 2, 3, 5, 6, 7, 8]",2,频率最低,1.00,2,失败,-1.00,-1.30,89.9%
03:35:19,126247,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",6,频率相同，选择中位数,1.00,3,获胜,+0.10,-1.20,90.0%
04:02:50,126263,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",7,频率相同，选择中位数,1.00,4,获胜,+0.10,-1.10,90.1%
04:10:52,126267,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",5,频率最低,1.00,1,获胜,+0.10,-1.00,90.2%
04:20:35,126272,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",7,频率最低,1.00,3,获胜,+0.10,-0.90,90.2%
04:22:22,126273,1,0.667,dynamic,"[2, 3, 4, 5, 6, 7, 8]",7,频率最低,1.00,8,获胜,+0.10,-0.80,90.3%
04:42:00,126283,7,0.750,dynamic,"[1, 2, 3, 4, 5, 6, 8]",2,频率最低,1.00,4,获胜,+0.10,-0.70,90.4%
04:52:10,126288,1,0.750,dynamic,"[2, 3, 4, 5, 6, 7, 8]",7,频率最低,1.00,5,获胜,+0.10,-0.60,90.5%
05:04:45,126295,6,0.700,static,"[1, 2, 3, 4, 5, 7, 8]",7,频率最低,1.00,6,获胜,+0.10,-0.50,90.6%
05:17:03,126301,4,0.830,static,"[1, 2, 3, 5, 6, 7, 8]",8,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.40,90.6%
05:23:00,126304,8,1.000,dynamic,"[1, 2, 3, 4, 5, 6, 7]",7,频率相同，选择中位数,1.00,4,获胜,+0.10,-0.30,90.7%
05:35:47,126310,8,0.850,static,"[1, 2, 3, 4, 5, 6, 7]",7,频率相同，选择中位数,1.00,8,获胜,+0.10,-0.20,90.8%
05:40:04,126312,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.10,90.8%
05:42:07,126313,5,0.750,dynamic,"[1, 2, 3, 4, 6, 7, 8]",3,频率最低,1.00,3,失败,-1.00,-1.10,90.2%
06:11:30,126330,3,0.750,dynamic,"[1, 2, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,3,获胜,+0.10,-1.00,90.2%
06:18:16,126334,3,0.820,static,"[1, 2, 4, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,2,获胜,+0.10,-0.90,90.3%
06:26:45,126339,3,0.667,dynamic,"[1, 2, 4, 5, 6, 7, 8]",1,频率最低,1.00,6,获胜,+0.10,-0.80,90.4%
06:41:37,126348,3,1.000,dynamic,"[1, 2, 4, 5, 6, 7, 8]",1,频率最低,1.00,7,获胜,+0.10,-0.70,90.4%
06:46:39,126351,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",1,频率最低,1.00,8,获胜,+0.10,-0.60,90.5%
06:58:12,126358,7,0.600,dynamic,"[1, 2, 3, 4, 5, 6, 8]",2,频率相同，选择中位数,1.00,1,获胜,+0.10,-0.50,90.6%
07:02:54,126361,2,0.870,static,"[1, 3, 4, 5, 6, 7, 8]",6,频率最低,1.00,7,获胜,+0.10,-0.40,90.6%
07:22:05,126373,6,0.667,dynamic,"[1, 2, 3, 4, 5, 7, 8]",2,频率最低,1.00,6,获胜,+0.10,-0.30,90.7%
07:25:16,126375,2,0.750,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率最低,1.00,2,获胜,+0.10,-0.20,90.8%
07:48:26,126390,1,0.910,static,"[2, 3, 4, 5, 6, 7, 8]",7,频率最低,1.00,7,失败,-1.00,-1.20,90.1%
07:49:59,126391,4,0.667,dynamic,"[1, 2, 3, 5, 6, 7, 8]",5,频率相同，选择中位数,1.00,3,获胜,+0.10,-1.10,90.2%
08:02:24,126399,2,0.667,dynamic,"[1, 3, 4, 5, 6, 7, 8]",6,频率相同，选择中位数,1.00,8,获胜,+0.10,-1.00,90.3%
08:03:52,126400,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",5,频率相同，选择中位数,1.00,1,获胜,+0.10,-0.90,90.3%
08:05:30,126401,5,0.600,dynamic,"[1, 2, 3, 4, 6, 7, 8]",6,频率最低,1.00,3,获胜,+0.10,-0.80,90.4%
08:06:59,126402,6,0.800,static,"[1, 2, 3, 4, 5, 7, 8]",5,频率相同，选择中位数,1.00,5,失败,-1.00,-1.80,89.8%
08:13:06,126406,8,0.667,dynamic,"[1, 2, 3, 4, 5, 6, 7]",6,频率最低,1.00,6,失败,-1.00,-2.80,89.2%
