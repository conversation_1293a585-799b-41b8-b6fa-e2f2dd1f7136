#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法验证工具
基于之前的分析结果，验证最有希望的算法候选
"""

import numpy as np
from typing import List, Tuple, Dict
import random

class AlgorithmValidator:
    def __init__(self, target_sequence: List[int]):
        """初始化验证器"""
        self.target_sequence = target_sequence
        self.n = len(target_sequence)
        
    def validate_best_lcg_candidates(self) -> List[Dict]:
        """验证最佳LCG候选"""
        print("=== 验证最佳LCG候选 ===")
        
        # 基于之前分析的最佳候选
        candidates = [
            (1664525, 1013904223, 2**32, "(x % 8) + 1"),
            (17, 5, 256, "(x % 8) + 1"),
            (214013, 2531011, 2**32, "(x % 8) + 1"),
            (37, 3, 256, "(x % 8) + 1"),
            (13, 11, 64, "(x % 8) + 1"),
            (29, 3, 128, "(x % 8) + 1"),
        ]
        
        results = []
        
        for a, c, m, mapping in candidates:
            print(f"\n验证 LCG({a}, {c}, {m}) with {mapping}")
            
            best_match = 0
            best_seed = 0
            best_sequence = []
            
            # 更细致的种子搜索
            seed_range = min(10000, m // 10) if m > 10000 else min(1000, m)
            seed_step = max(1, seed_range // 200)
            
            for seed in range(1, seed_range, seed_step):
                # 生成序列
                generated = self.generate_lcg_sequence(seed, a, c, m, self.n)
                
                # 计算匹配率
                matches = sum(1 for i in range(self.n) 
                            if generated[i] == self.target_sequence[i])
                match_rate = matches / self.n
                
                if match_rate > best_match:
                    best_match = match_rate
                    best_seed = seed
                    best_sequence = generated.copy()
            
            if best_match > 0.15:  # 降低阈值以获得更多候选
                result = {
                    'algorithm': 'LCG',
                    'parameters': (a, c, m),
                    'seed': best_seed,
                    'mapping': mapping,
                    'match_rate': best_match,
                    'generated_sequence': best_sequence
                }
                results.append(result)
                print(f"  最佳匹配: seed={best_seed}, 匹配率={best_match:.3f}")
                
                # 显示详细比较
                self.show_detailed_comparison(best_sequence, f"LCG({a}, {c}, {m}), seed={best_seed}")
        
        return results
    
    def generate_lcg_sequence(self, seed: int, a: int, c: int, m: int, count: int) -> List[int]:
        """生成LCG序列"""
        sequence = []
        x = seed
        for _ in range(count):
            x = (a * x + c) % m
            mapped = (x % 8) + 1
            sequence.append(mapped)
        return sequence
    
    def show_detailed_comparison(self, generated: List[int], algorithm_name: str, show_count: int = 20):
        """显示详细的序列比较"""
        print(f"\n  详细比较 ({algorithm_name}):")
        print("  位置  目标  生成  匹配")
        
        matches = 0
        for i in range(min(show_count, self.n)):
            target = self.target_sequence[i]
            gen = generated[i]
            match = "✓" if target == gen else "✗"
            if target == gen:
                matches += 1
            print(f"  {i:4d}  {target:4d}  {gen:4d}   {match}")
        
        if self.n > show_count:
            print(f"  ... (显示前{show_count}个，总共{self.n}个)")
        
        print(f"  前{min(show_count, self.n)}个匹配: {matches}/{min(show_count, self.n)} ({matches/min(show_count, self.n):.3f})")
    
    def test_manual_patterns(self) -> List[Dict]:
        """手动测试一些观察到的模式"""
        print("\n=== 测试手动观察的模式 ===")
        
        results = []
        
        # 观察序列，尝试找到一些规律
        print("原始序列:", self.target_sequence)
        
        # 测试是否存在简单的周期性
        for period in range(2, min(15, self.n // 3)):
            matches = 0
            total = 0
            for i in range(self.n - period):
                if self.target_sequence[i] == self.target_sequence[i + period]:
                    matches += 1
                total += 1
            
            if total > 0:
                match_rate = matches / total
                if match_rate > 0.3:
                    print(f"发现周期性模式: 周期={period}, 匹配率={match_rate:.3f}")
                    
                    # 构造周期性序列进行验证
                    generated = []
                    for i in range(self.n):
                        generated.append(self.target_sequence[i % period])
                    
                    full_matches = sum(1 for i in range(self.n) 
                                     if generated[i] == self.target_sequence[i])
                    full_match_rate = full_matches / self.n
                    
                    if full_match_rate > 0.5:
                        result = {
                            'algorithm': 'Periodic',
                            'parameters': {'period': period},
                            'match_rate': full_match_rate,
                            'generated_sequence': generated
                        }
                        results.append(result)
                        self.show_detailed_comparison(generated, f"周期性(周期={period})")
        
        # 测试基于差值的模式
        differences = []
        for i in range(1, self.n):
            diff = self.target_sequence[i] - self.target_sequence[i-1]
            differences.append(diff)
        
        print(f"\n差值序列: {differences[:20]}...")
        
        # 检查差值是否有周期性
        for period in range(2, min(10, len(differences) // 2)):
            matches = 0
            total = 0
            for i in range(len(differences) - period):
                if differences[i] == differences[i + period]:
                    matches += 1
                total += 1
            
            if total > 0 and matches / total > 0.4:
                print(f"差值周期性: 周期={period}, 匹配率={matches/total:.3f}")
        
        return results
    
    def run_validation(self) -> Dict:
        """运行完整验证"""
        print("开始算法验证...")
        print(f"目标序列长度: {self.n}")
        print(f"目标序列: {self.target_sequence}")
        
        # 验证LCG候选
        lcg_results = self.validate_best_lcg_candidates()
        
        # 测试手动模式
        manual_results = self.test_manual_patterns()
        
        # 合并结果
        all_results = lcg_results + manual_results
        all_results.sort(key=lambda x: x['match_rate'], reverse=True)
        
        return {
            'lcg_results': lcg_results,
            'manual_results': manual_results,
            'all_results': all_results
        }
    
    def predict_next_values(self, algorithm_info: Dict, count: int = 10) -> List[int]:
        """基于最佳算法预测后续值"""
        if algorithm_info['algorithm'] == 'LCG':
            a, c, m = algorithm_info['parameters']
            seed = algorithm_info['seed']
            
            # 找到当前状态
            x = seed
            for _ in range(self.n):
                x = (a * x + c) % m
            
            # 生成后续值
            predictions = []
            for _ in range(count):
                x = (a * x + c) % m
                mapped = (x % 8) + 1
                predictions.append(mapped)
            
            return predictions
        
        elif algorithm_info['algorithm'] == 'Periodic':
            period = algorithm_info['parameters']['period']
            predictions = []
            for i in range(count):
                idx = (self.n + i) % period
                predictions.append(self.target_sequence[idx])
            return predictions
        
        return []

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机生成1-8.txt")
    validator = AlgorithmValidator(sequence)
    results = validator.run_validation()
    
    print("\n=== 验证总结 ===")
    
    if results['all_results']:
        print("最佳算法候选:")
        for i, result in enumerate(results['all_results'][:3], 1):
            print(f"\n{i}. {result['algorithm']}")
            if result['algorithm'] == 'LCG':
                a, c, m = result['parameters']
                print(f"   参数: LCG({a}, {c}, {m})")
                print(f"   种子: {result['seed']}")
                print(f"   映射: {result['mapping']}")
            elif result['algorithm'] == 'Periodic':
                print(f"   周期: {result['parameters']['period']}")
            print(f"   匹配率: {result['match_rate']:.3f}")
            
            # 预测后续值
            predictions = validator.predict_next_values(result, 10)
            if predictions:
                print(f"   预测后续10个值: {predictions}")
    else:
        print("未找到满意的算法匹配")
