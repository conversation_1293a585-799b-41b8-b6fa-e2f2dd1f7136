#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复重复处理开奖结果的问题

问题分析：
1. wait_for_result 方法中调用了 process_result
2. handle_real_lottery_result 方法中也调用了 process_result
3. 同一个开奖结果被处理了两次，导致连续失败次数重复计算

解决方案：
1. 在系统中添加已处理期号的跟踪
2. 避免重复处理同一期号的开奖结果
3. 修复当前状态文件中的错误数据
"""

import json
import os
from datetime import datetime

def fix_state_file():
    """修复状态文件中的连续失败次数"""
    
    state_file = f"system_state_{datetime.now().strftime('%Y%m%d')}.json"
    
    print(f"🔧 修复状态文件: {state_file}")
    
    if not os.path.exists(state_file):
        print(f"❌ 状态文件不存在: {state_file}")
        return False
    
    try:
        # 读取当前状态
        with open(state_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        print(f"📊 当前状态:")
        print(f"   连续失败: {state_data.get('consecutive_losses', 0)}次")
        print(f"   连续获胜: {state_data.get('consecutive_wins', 0)}次")
        print(f"   总投注: {state_data.get('total_bets', 0)}次")
        print(f"   总获胜: {state_data.get('total_wins', 0)}次")
        
        # 根据投注记录分析，实际连续失败次数应该是3次
        # 期号131890、131891、131892连续失败
        correct_consecutive_losses = 3
        
        # 修复连续失败次数
        state_data['consecutive_losses'] = correct_consecutive_losses
        state_data['consecutive_wins'] = 0
        state_data['last_update'] = datetime.now().isoformat()
        
        # 备份原文件
        backup_file = f"{state_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, ensure_ascii=False)
        print(f"💾 原文件已备份到: {backup_file}")
        
        # 保存修复后的状态
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 状态文件修复完成:")
        print(f"   连续失败: {correct_consecutive_losses}次 (修复前: {state_data.get('consecutive_losses', 0)}次)")
        print(f"   连续获胜: 0次")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复状态文件失败: {e}")
        return False

def analyze_betting_log():
    """分析投注记录，确认实际连续失败次数"""
    
    log_file = "betting_log_20250804_083153.md"
    
    if not os.path.exists(log_file):
        print(f"❌ 投注记录文件不存在: {log_file}")
        return
    
    print(f"📋 分析投注记录: {log_file}")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找投注记录表格
        lines = content.split('\n')
        betting_records = []
        
        for line in lines:
            if '|' in line and '期' in line and ('获胜' in line or '失败' in line):
                parts = [p.strip() for p in line.split('|')]
                if len(parts) >= 7:
                    try:
                        issue = parts[2]
                        result = parts[5]
                        if issue.isdigit():
                            betting_records.append({
                                'issue': int(issue),
                                'result': result
                            })
                    except:
                        continue
        
        # 按期号排序
        betting_records.sort(key=lambda x: x['issue'])
        
        print(f"📊 最近10期投注结果:")
        recent_records = betting_records[-10:]
        consecutive_losses = 0
        
        for i, record in enumerate(recent_records):
            print(f"   期号{record['issue']}: {record['result']}")
        
        # 计算连续失败次数
        for record in reversed(recent_records):
            if record['result'] == '失败':
                consecutive_losses += 1
            else:
                break
        
        print(f"\n🎯 分析结果:")
        print(f"   实际连续失败次数: {consecutive_losses}次")
        print(f"   最近失败期号: {[r['issue'] for r in recent_records if r['result'] == '失败'][-consecutive_losses:]}")
        
        return consecutive_losses
        
    except Exception as e:
        print(f"❌ 分析投注记录失败: {e}")
        return None

def create_duplicate_prevention_patch():
    """创建防止重复处理的补丁代码"""
    
    patch_code = '''
# 在 OptimizedRandomBettingSystem 类中添加已处理期号跟踪
class OptimizedRandomBettingSystem:
    def __init__(self, ...):
        # ... 其他初始化代码 ...
        
        # 添加已处理期号跟踪
        self.processed_issues = set()
    
    def process_result(self, issue: int, winning_room: int, actual_profit: float = None):
        """处理开奖结果 - 防止重复处理"""
        
        # 检查是否已经处理过这个期号
        if issue in self.processed_issues:
            print(f"⚠️ 期号{issue}已处理过，跳过重复处理")
            return
        
        # 标记为已处理
        self.processed_issues.add(issue)
        
        # 保持最近100期的处理记录
        if len(self.processed_issues) > 100:
            # 移除最旧的期号
            min_issue = min(self.processed_issues)
            self.processed_issues.remove(min_issue)
        
        # 继续原有的处理逻辑
        # ... 原有代码 ...
    '''
    
    print("🔧 防重复处理补丁代码:")
    print(patch_code)
    
    return patch_code

def main():
    """主函数"""
    
    print("🔍 投注系统重复统计问题修复工具")
    print("=" * 60)
    
    # 1. 分析投注记录
    print("\n1️⃣ 分析投注记录...")
    actual_losses = analyze_betting_log()
    
    # 2. 修复状态文件
    print("\n2️⃣ 修复状态文件...")
    fix_success = fix_state_file()
    
    # 3. 提供代码修复建议
    print("\n3️⃣ 代码修复建议...")
    create_duplicate_prevention_patch()
    
    print("\n" + "=" * 60)
    print("🎯 修复总结:")
    
    if actual_losses is not None:
        print(f"   ✅ 实际连续失败次数: {actual_losses}次")
    else:
        print(f"   ❌ 无法分析投注记录")
    
    if fix_success:
        print(f"   ✅ 状态文件已修复")
    else:
        print(f"   ❌ 状态文件修复失败")
    
    print(f"   📝 建议: 在系统代码中添加防重复处理机制")
    
    print("\n🚀 修复完成！重新启动投注系统后应该显示正确的连续失败次数。")

if __name__ == "__main__":
    main()
