#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合暴力搜索工具
测试各种可能的随机数生成算法和参数组合
"""

import numpy as np
from typing import List, Tuple, Dict, Callable
import itertools
import random

class ComprehensiveSearcher:
    def __init__(self, target_sequence: List[int]):
        """初始化综合搜索器"""
        self.target_sequence = target_sequence
        self.n = len(target_sequence)
        self.best_matches = []
        
    def test_lcg_with_various_mappings(self) -> List[Tuple]:
        """测试LCG配合各种映射函数"""
        print("=== 测试LCG + 各种映射函数 ===")
        
        # LCG参数组合
        lcg_params = [
            # 常见的小参数LCG
            (17, 5, 256), (25, 7, 256), (37, 3, 256), (41, 7, 256),
            (13, 11, 64), (21, 5, 64), (29, 3, 128), (45, 21, 128),
            (7, 5, 32), (11, 7, 32), (15, 1, 32), (23, 9, 64),
            # 更大的参数
            (1103515245, 12345, 2**31), (214013, 2531011, 2**32),
            (1664525, 1013904223, 2**32), (16807, 0, 2**31 - 1),
        ]
        
        # 各种映射函数
        mappings = [
            ("(x % 8) + 1", lambda x: (x % 8) + 1),
            ("((x >> 3) % 8) + 1", lambda x: ((x >> 3) % 8) + 1),
            ("((x >> 8) % 8) + 1", lambda x: ((x >> 8) % 8) + 1),
            ("((x >> 16) % 8) + 1", lambda x: ((x >> 16) % 8) + 1),
            ("((x // 8) % 8) + 1", lambda x: ((x // 8) % 8) + 1),
            ("((x * 8) // m) + 1", None),  # 特殊处理
            ("(x % 7) + 1", lambda x: min((x % 7) + 1, 8)),
            ("((x ^ (x >> 8)) % 8) + 1", lambda x: ((x ^ (x >> 8)) % 8) + 1),
        ]
        
        results = []
        
        for a, c, m in lcg_params:
            print(f"  测试LCG({a}, {c}, {m})")
            
            # 测试不同的种子
            seed_range = min(1000, m // 100) if m > 1000 else min(100, m)
            seed_step = max(1, seed_range // 50)
            
            for seed in range(1, seed_range, seed_step):
                # 生成LCG序列
                lcg_sequence = []
                x = seed
                for _ in range(self.n):
                    x = (a * x + c) % m
                    lcg_sequence.append(x)
                
                # 测试各种映射
                for map_name, map_func in mappings:
                    try:
                        if map_func is None:  # 特殊处理 ((x * 8) // m) + 1
                            mapped_sequence = [min(((val * 8) // m) + 1, 8) for val in lcg_sequence]
                        else:
                            mapped_sequence = [map_func(val) for val in lcg_sequence]
                        
                        # 计算匹配率
                        matches = sum(1 for i in range(self.n) 
                                    if mapped_sequence[i] == self.target_sequence[i])
                        match_rate = matches / self.n
                        
                        if match_rate > 0.4:  # 匹配率超过40%
                            results.append((a, c, m, seed, map_name, match_rate, mapped_sequence))
                            print(f"    找到高匹配: seed={seed}, {map_name}, 匹配率={match_rate:.3f}")
                            
                    except (ZeroDivisionError, OverflowError):
                        continue
        
        return sorted(results, key=lambda x: x[5], reverse=True)
    
    def test_xorshift_algorithms(self) -> List[Tuple]:
        """测试Xorshift算法"""
        print("\n=== 测试Xorshift算法 ===")
        
        results = []
        
        # Xorshift32参数组合
        xorshift_params = [
            (13, 17, 5), (17, 13, 5), (5, 17, 13), (7, 13, 17),
            (1, 3, 10), (3, 1, 14), (1, 1, 18), (13, 19, 12),
        ]
        
        for a, b, c in xorshift_params:
            print(f"  测试Xorshift({a}, {b}, {c})")
            
            for seed in range(1, 1000, 50):
                if seed == 0:  # Xorshift不能用0作为种子
                    continue
                    
                # 生成Xorshift序列
                xor_sequence = []
                x = seed
                for _ in range(self.n):
                    x ^= x << a
                    x ^= x >> b
                    x ^= x << c
                    x &= 0xFFFFFFFF  # 保持32位
                    xor_sequence.append(x)
                
                # 测试映射
                mapped_sequence = [(val % 8) + 1 for val in xor_sequence]
                matches = sum(1 for i in range(self.n) 
                            if mapped_sequence[i] == self.target_sequence[i])
                match_rate = matches / self.n
                
                if match_rate > 0.4:
                    results.append((a, b, c, seed, match_rate, mapped_sequence))
                    print(f"    找到匹配: seed={seed}, 匹配率={match_rate:.3f}")
        
        return sorted(results, key=lambda x: x[4], reverse=True)
    
    def test_custom_algorithms(self) -> List[Tuple]:
        """测试自定义算法"""
        print("\n=== 测试自定义算法 ===")
        
        results = []
        
        # 测试基于位置的算法
        print("  测试基于位置的算法...")
        for a in range(1, 20):
            for b in range(0, 20):
                for c in range(1, 10):
                    # 算法: ((a * i + b) * c) % 8 + 1
                    generated = [((a * i + b) * c % 8) + 1 for i in range(self.n)]
                    matches = sum(1 for i in range(self.n) 
                                if generated[i] == self.target_sequence[i])
                    match_rate = matches / self.n
                    
                    if match_rate > 0.5:
                        results.append(("Position", (a, b, c), match_rate, generated))
                        print(f"    位置算法 ((a*i+b)*c)%8+1, a={a}, b={b}, c={c}: {match_rate:.3f}")
        
        # 测试基于前值的递推算法
        print("  测试递推算法...")
        for mult in range(1, 10):
            for add in range(0, 10):
                for mod_val in [8, 16, 32]:
                    # 算法: next = (prev * mult + add) % mod_val % 8 + 1
                    generated = [self.target_sequence[0]]  # 第一个值保持不变
                    for i in range(1, self.n):
                        next_val = ((generated[i-1] * mult + add) % mod_val % 8) + 1
                        generated.append(next_val)
                    
                    matches = sum(1 for i in range(self.n) 
                                if generated[i] == self.target_sequence[i])
                    match_rate = matches / self.n
                    
                    if match_rate > 0.5:
                        results.append(("Recursive", (mult, add, mod_val), match_rate, generated))
                        print(f"    递推算法 (prev*{mult}+{add})%{mod_val}%8+1: {match_rate:.3f}")
        
        return sorted(results, key=lambda x: x[2], reverse=True)
    
    def run_comprehensive_search(self) -> Dict:
        """运行综合搜索"""
        print("开始综合搜索...")
        print(f"目标序列: {self.target_sequence}")
        print()
        
        # 测试各种算法
        lcg_results = self.test_lcg_with_various_mappings()
        xorshift_results = self.test_xorshift_algorithms()
        custom_results = self.test_custom_algorithms()
        
        # 合并所有结果
        all_results = []
        
        for result in lcg_results:
            all_results.append(("LCG", result))
        
        for result in xorshift_results:
            all_results.append(("Xorshift", result))
            
        for result in custom_results:
            all_results.append(("Custom", result))
        
        # 按匹配率排序
        if lcg_results:
            all_results.sort(key=lambda x: x[1][5] if x[0] == "LCG" else 
                           (x[1][4] if x[0] == "Xorshift" else x[1][2]), reverse=True)
        
        return {
            'lcg_results': lcg_results,
            'xorshift_results': xorshift_results,
            'custom_results': custom_results,
            'all_results': all_results
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机生成1-8.txt")
    searcher = ComprehensiveSearcher(sequence)
    results = searcher.run_comprehensive_search()
    
    print("\n=== 最终结果 ===")
    
    # 显示最佳匹配
    best_results = []
    
    if results['lcg_results']:
        best_results.extend([("LCG", r) for r in results['lcg_results'][:3]])
    
    if results['xorshift_results']:
        best_results.extend([("Xorshift", r) for r in results['xorshift_results'][:3]])
        
    if results['custom_results']:
        best_results.extend([("Custom", r) for r in results['custom_results'][:3]])
    
    if best_results:
        print("最佳匹配结果:")
        for i, (algo_type, result) in enumerate(best_results[:5], 1):
            if algo_type == "LCG":
                a, c, m, seed, map_name, match_rate, sequence = result
                print(f"{i}. LCG({a}, {c}, {m}), seed={seed}, 映射={map_name}, 匹配率={match_rate:.3f}")
            elif algo_type == "Xorshift":
                a, b, c, seed, match_rate, sequence = result
                print(f"{i}. Xorshift({a}, {b}, {c}), seed={seed}, 匹配率={match_rate:.3f}")
            elif algo_type == "Custom":
                name, params, match_rate, sequence = result
                print(f"{i}. {name} {params}, 匹配率={match_rate:.3f}")
    else:
        print("未找到高匹配率的算法")
