#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强动态金额计算
"""

import sys
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem

def test_enhanced_dynamic_amount():
    """测试增强动态金额计算"""
    
    print("🧪 测试增强动态金额计算")
    print("=" * 50)
    
    # 创建模拟API客户端
    api_client = GameAPIClient("http://mock", {})
    
    # 测试不同的配置
    test_configs = [
        {
            'name': '默认配置',
            'config': {
                'base_bet_amount': 0.1,           # 基础投注金额
                'max_bet_amount': 10.0,           # 最大投注金额
                'min_bet_amount': 1.0,            # 最小投注金额 (API限制)
                'max_consecutive_losses': 5,       # 最大连续失败次数
                'max_daily_loss': 20.0,           # 日最大损失
                'stop_loss_percentage': 0.3,       # 止损百分比
                'initial_balance': 100.0,         # 初始余额
            }
        },
        {
            'name': '高基础金额配置',
            'config': {
                'base_bet_amount': 2.0,           # 基础投注金额
                'max_bet_amount': 20.0,           # 最大投注金额
                'min_bet_amount': 1.0,            # 最小投注金额 (API限制)
                'max_consecutive_losses': 5,       # 最大连续失败次数
                'max_daily_loss': 50.0,           # 日最大损失
                'stop_loss_percentage': 0.3,       # 止损百分比
                'initial_balance': 200.0,         # 初始余额
            }
        },
        {
            'name': '低基础金额配置',
            'config': {
                'base_bet_amount': 1.5,           # 基础投注金额
                'max_bet_amount': 15.0,           # 最大投注金额
                'min_bet_amount': 1.0,            # 最小投注金额 (API限制)
                'max_consecutive_losses': 5,       # 最大连续失败次数
                'max_daily_loss': 30.0,           # 日最大损失
                'stop_loss_percentage': 0.3,       # 止损百分比
                'initial_balance': 150.0,         # 初始余额
            }
        }
    ]
    
    for test_case in test_configs:
        print(f"\n📊 测试场景: {test_case['name']}")
        print("-" * 30)
        
        # 创建系统
        system = OptimizedRandomBettingSystem(api_client, test_case['config'])
        
        # 测试不同状态下的金额计算
        test_scenarios = [
            {'name': '初始状态', 'losses': 0, 'wins': 0},
            {'name': '连败1次', 'losses': 1, 'wins': 0},
            {'name': '连败2次', 'losses': 2, 'wins': 0},
            {'name': '连败3次', 'losses': 3, 'wins': 0},
            {'name': '连胜3次', 'losses': 0, 'wins': 3},
            {'name': '连胜5次', 'losses': 0, 'wins': 5},
            {'name': '余额减半', 'losses': 0, 'wins': 0, 'balance_ratio': 0.4},
        ]
        
        for scenario in test_scenarios:
            print(f"\n🎯 场景: {scenario['name']}")
            
            # 设置状态
            system.consecutive_losses = scenario['losses']
            system.consecutive_wins = scenario['wins']
            
            if 'balance_ratio' in scenario:
                system.current_balance = system.initial_balance * scenario['balance_ratio']
            else:
                system.current_balance = system.initial_balance
            
            # 计算增强动态金额
            amount = system.calculate_enhanced_dynamic_amount()
            
            print(f"   结果: {amount:.2f}元")
            print()

def test_smart_betting_handler_integration():
    """测试智能投注处理器集成"""
    
    print("\n🤖 测试智能投注处理器集成")
    print("=" * 50)
    
    try:
        from smart_betting_handler import SmartBettingHandler
        
        # 创建模拟API客户端
        api_client = GameAPIClient("http://mock", {})
        
        # 创建智能投注处理器
        handler = SmartBettingHandler(api_client)
        
        # 测试不同金额的分解
        test_amounts = [0.1, 0.5, 1.0, 1.5, 2.3, 3.7, 5.2, 8.9, 12.6]
        
        print("\n💰 金额分解测试:")
        for amount in test_amounts:
            print(f"\n目标金额: {amount:.2f}元")
            breakdown = handler.calculate_bet_breakdown(amount)
            actual_amount = sum(breakdown)
            print(f"分解结果: {breakdown}")
            print(f"实际金额: {actual_amount:.2f}元")
            print(f"差异: {actual_amount - amount:+.2f}元")
            
    except ImportError as e:
        print(f"❌ 智能投注处理器导入失败: {e}")

def test_real_system_behavior():
    """测试真实系统行为"""
    
    print("\n🎯 测试真实系统行为")
    print("=" * 50)
    
    # 创建模拟API客户端
    api_client = GameAPIClient("http://mock", {})
    
    # 使用默认配置
    config = {
        'base_bet_amount': 0.1,           # 基础投注金额
        'max_bet_amount': 10.0,           # 最大投注金额
        'min_bet_amount': 1.0,            # 最小投注金额 (API限制)
        'max_consecutive_losses': 5,       # 最大连续失败次数
        'max_daily_loss': 20.0,           # 日最大损失
        'stop_loss_percentage': 0.3,       # 止损百分比
        'initial_balance': 100.0,         # 初始余额
    }
    
    system = OptimizedRandomBettingSystem(api_client, config)
    
    print(f"📊 系统配置:")
    print(f"   基础金额: {system.base_bet_amount}元")
    print(f"   最小金额: {system.min_bet_amount}元")
    print(f"   最大金额: {system.max_bet_amount}元")
    print(f"   初始余额: {system.initial_balance}元")
    print(f"   当前余额: {system.current_balance}元")
    
    # 模拟几次投注
    print(f"\n🎲 模拟投注序列:")
    
    for i in range(5):
        print(f"\n--- 第{i+1}次投注 ---")
        
        # 计算金额
        amount = system.calculate_enhanced_dynamic_amount()
        print(f"计算结果: {amount:.2f}元")
        
        # 模拟投注结果
        if i % 3 == 0:  # 模拟失败
            system.consecutive_losses += 1
            system.consecutive_wins = 0
            system.current_balance -= amount
            print(f"模拟结果: 失败 (余额: {system.current_balance:.2f}元)")
        else:  # 模拟成功
            system.consecutive_wins += 1
            system.consecutive_losses = 0
            system.current_balance += amount * 0.1  # 模拟小幅盈利
            print(f"模拟结果: 成功 (余额: {system.current_balance:.2f}元)")

if __name__ == "__main__":
    test_enhanced_dynamic_amount()
    test_smart_betting_handler_integration()
    test_real_system_behavior()
    
    print(f"\n🎉 测试完成!")
    print(f"💡 如果您在实际运行中看不到增强动态金额计算信息，")
    print(f"   可能是因为:")
    print(f"   1. 基础金额太小 (0.1元)")
    print(f"   2. 智能投注处理器将小金额调整为1元")
    print(f"   3. API限制最小投注为1元")
    print(f"   4. 系统状态导致最终金额被限制")
