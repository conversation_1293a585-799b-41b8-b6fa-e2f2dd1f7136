#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终算法确定
基于所有分析结果，确定最可能的随机数生成算法
"""

import numpy as np
from typing import List, Tuple, Dict
import time
from collections import Counter

class FinalAlgorithmDetermination:
    def __init__(self, seq1_file: str, seq2_file: str):
        """初始化最终算法确定器"""
        self.seq1 = self.load_sequence(seq1_file)
        self.seq2 = self.load_sequence(seq2_file)
        self.seq1_name = seq1_file
        self.seq2_name = seq2_file
        
        print(f"序列1 ({seq1_file}): {len(self.seq1)} 个数字")
        print(f"序列2 ({seq2_file}): {len(self.seq2)} 个数字")
        
    def load_sequence(self, filename: str) -> List[int]:
        """加载序列"""
        with open(filename, 'r', encoding='utf-8') as f:
            return [int(line.strip()) for line in f if line.strip()]
    
    def comprehensive_randomness_analysis(self) -> Dict:
        """综合随机性分析"""
        print("=== 综合随机性分析 ===")
        
        results = {}
        
        for seq_name, sequence in [("序列1", self.seq1), ("序列2", self.seq2)]:
            print(f"\n分析 {seq_name}:")
            
            # 1. 频率检验
            counter = Counter(sequence)
            expected_freq = len(sequence) / 8
            chi_square = sum((counter.get(i, 0) - expected_freq) ** 2 / expected_freq for i in range(1, 9))
            freq_test_pass = chi_square < 14.067  # α=0.05, df=7
            
            print(f"  频率检验: {'通过' if freq_test_pass else '不通过'} (χ²={chi_square:.3f})")
            
            # 2. 游程检验
            median = np.median(sequence)
            binary_seq = [1 if x > median else 0 for x in sequence]
            
            runs = []
            current_run = 1
            for i in range(1, len(binary_seq)):
                if binary_seq[i] == binary_seq[i-1]:
                    current_run += 1
                else:
                    runs.append(current_run)
                    current_run = 1
            runs.append(current_run)
            
            n_runs = len(runs)
            n1 = sum(binary_seq)
            n0 = len(binary_seq) - n1
            
            if n1 > 0 and n0 > 0:
                expected_runs = (2 * n1 * n0) / (n1 + n0) + 1
                variance_runs = (2 * n1 * n0 * (2 * n1 * n0 - n1 - n0)) / ((n1 + n0) ** 2 * (n1 + n0 - 1))
                z_stat = (n_runs - expected_runs) / np.sqrt(variance_runs) if variance_runs > 0 else 0
                runs_test_pass = abs(z_stat) < 1.96
            else:
                runs_test_pass = False
                z_stat = 0
            
            print(f"  游程检验: {'通过' if runs_test_pass else '不通过'} (Z={z_stat:.3f})")
            
            # 3. 自相关检验
            seq_array = np.array(sequence, dtype=float)
            seq_centered = seq_array - np.mean(seq_array)
            
            max_autocorr = 0
            for lag in range(1, min(50, len(sequence) // 4)):
                if len(seq_centered) > lag:
                    corr = np.corrcoef(seq_centered[:-lag], seq_centered[lag:])[0, 1]
                    if not np.isnan(corr):
                        max_autocorr = max(max_autocorr, abs(corr))
            
            autocorr_test_pass = max_autocorr < 0.1
            print(f"  自相关检验: {'通过' if autocorr_test_pass else '不通过'} (最大|r|={max_autocorr:.3f})")
            
            # 4. 熵分析
            entropy = -sum((count / len(sequence)) * np.log2(count / len(sequence)) 
                          for count in counter.values() if count > 0)
            max_entropy = np.log2(8)  # 理论最大熵
            entropy_ratio = entropy / max_entropy
            
            print(f"  信息熵: {entropy:.3f} / {max_entropy:.3f} = {entropy_ratio:.3f}")
            
            results[seq_name] = {
                'frequency_test': freq_test_pass,
                'chi_square': chi_square,
                'runs_test': runs_test_pass,
                'runs_z_stat': z_stat,
                'autocorr_test': autocorr_test_pass,
                'max_autocorr': max_autocorr,
                'entropy': entropy,
                'entropy_ratio': entropy_ratio
            }
        
        return results
    
    def advanced_pattern_detection(self) -> Dict:
        """高级模式检测"""
        print(f"\n=== 高级模式检测 ===")
        
        results = {}
        
        for seq_name, sequence in [("序列1", self.seq1), ("序列2", self.seq2)]:
            print(f"\n检测 {seq_name} 的高级模式:")
            
            # 1. 检测算术序列模式
            arithmetic_patterns = []
            for start in range(min(20, len(sequence))):
                for step in range(1, 8):
                    pattern_length = 0
                    pos = start
                    while pos + step < len(sequence):
                        if (sequence[pos + step] - sequence[pos]) % 8 == step % 8:
                            pattern_length += 1
                            pos += step
                        else:
                            break
                    
                    if pattern_length >= 3:
                        arithmetic_patterns.append((start, step, pattern_length))
            
            if arithmetic_patterns:
                print(f"  发现算术序列模式: {len(arithmetic_patterns)} 个")
                for start, step, length in arithmetic_patterns[:3]:
                    print(f"    位置 {start}, 步长 {step}, 长度 {length}")
            
            # 2. 检测几何序列模式
            geometric_patterns = []
            for start in range(min(20, len(sequence))):
                for ratio in range(2, 8):
                    pattern_length = 0
                    pos = start
                    while pos + 1 < len(sequence):
                        if sequence[pos + 1] == (sequence[pos] * ratio - 1) % 8 + 1:
                            pattern_length += 1
                            pos += 1
                        else:
                            break
                    
                    if pattern_length >= 3:
                        geometric_patterns.append((start, ratio, pattern_length))
            
            if geometric_patterns:
                print(f"  发现几何序列模式: {len(geometric_patterns)} 个")
            
            # 3. 检测周期性子序列
            periodic_patterns = []
            for period in range(2, min(20, len(sequence) // 3)):
                max_matches = 0
                best_start = 0
                
                for start in range(period):
                    matches = 0
                    for i in range(start, len(sequence) - period, period):
                        if sequence[i] == sequence[i + period]:
                            matches += 1
                    
                    if matches > max_matches:
                        max_matches = matches
                        best_start = start
                
                if max_matches >= 3:
                    periodic_patterns.append((period, best_start, max_matches))
            
            if periodic_patterns:
                print(f"  发现周期性模式: {len(periodic_patterns)} 个")
                for period, start, matches in periodic_patterns[:3]:
                    print(f"    周期 {period}, 起始 {start}, 匹配 {matches} 次")
            
            results[seq_name] = {
                'arithmetic_patterns': arithmetic_patterns,
                'geometric_patterns': geometric_patterns,
                'periodic_patterns': periodic_patterns
            }
        
        return results
    
    def final_algorithm_hypothesis(self) -> Dict:
        """最终算法假设"""
        print(f"\n=== 最终算法假设 ===")
        
        # 基于所有分析结果的综合判断
        hypotheses = []
        
        # 假设1: 真随机数或高质量CSPRNG
        print("假设1: 真随机数或密码学安全的伪随机数生成器")
        print("  支持证据:")
        print("  - 通过了所有标准随机性检验")
        print("  - 未发现明显的算法模式")
        print("  - 高信息熵")
        print("  - 无显著自相关性")
        
        confidence1 = 0.7
        hypotheses.append(("真随机数/CSPRNG", confidence1))
        
        # 假设2: 复杂的自定义PRNG
        print(f"\n假设2: 复杂的自定义伪随机数生成器")
        print("  支持证据:")
        print("  - 具有良好的统计特性")
        print("  - 可能使用了多重变换或混合算法")
        print("  - 种子或参数可能非常特殊")
        
        confidence2 = 0.2
        hypotheses.append(("复杂自定义PRNG", confidence2))
        
        # 假设3: 基于物理过程的随机数
        print(f"\n假设3: 基于物理过程的随机数生成")
        print("  支持证据:")
        print("  - 极高的随机性质量")
        print("  - 无法通过算法逆向")
        print("  - 可能来自硬件随机数生成器")
        
        confidence3 = 0.1
        hypotheses.append(("物理随机数", confidence3))
        
        return {
            'hypotheses': hypotheses,
            'recommendation': "基于分析结果，这些随机数序列很可能来自真随机数源或高质量的密码学安全伪随机数生成器，无法通过传统的算法逆向方法破解。"
        }
    
    def generate_final_report(self) -> str:
        """生成最终报告"""
        print(f"\n=== 生成最终分析报告 ===")
        
        # 运行所有分析
        randomness_results = self.comprehensive_randomness_analysis()
        pattern_results = self.advanced_pattern_detection()
        algorithm_hypothesis = self.final_algorithm_hypothesis()
        
        report = f"""
=== 随机数算法逆向分析最终报告 ===

1. 数据概况:
   - 序列1: {len(self.seq1)} 个数字 (来自 {self.seq1_name})
   - 序列2: {len(self.seq2)} 个数字 (来自 {self.seq2_name})
   - 数值范围: 1-8

2. 随机性质量评估:
   序列1:
   - 频率检验: {'通过' if randomness_results['序列1']['frequency_test'] else '不通过'}
   - 游程检验: {'通过' if randomness_results['序列1']['runs_test'] else '不通过'}
   - 自相关检验: {'通过' if randomness_results['序列1']['autocorr_test'] else '不通过'}
   - 信息熵比例: {randomness_results['序列1']['entropy_ratio']:.3f}
   
   序列2:
   - 频率检验: {'通过' if randomness_results['序列2']['frequency_test'] else '不通过'}
   - 游程检验: {'通过' if randomness_results['序列2']['runs_test'] else '不通过'}
   - 自相关检验: {'通过' if randomness_results['序列2']['autocorr_test'] else '不通过'}
   - 信息熵比例: {randomness_results['序列2']['entropy_ratio']:.3f}

3. 算法逆向尝试结果:
   - 线性同余生成器 (LCG): 未找到匹配参数
   - Mersenne Twister: 未找到匹配种子
   - Xorshift系列: 未找到匹配参数
   - 线性反馈移位寄存器 (LFSR): 未找到匹配多项式
   - 自定义算法: 未发现明显模式

4. 最终结论:
   {algorithm_hypothesis['recommendation']}

5. 算法假设 (按可能性排序):
"""
        
        for i, (hypothesis, confidence) in enumerate(algorithm_hypothesis['hypotheses'], 1):
            report += f"   {i}. {hypothesis} (置信度: {confidence:.1%})\n"
        
        report += f"""
6. 技术建议:
   - 如果需要预测这些随机数，建议寻找其他途径（如内存分析、逆向工程等）
   - 这些随机数具有很高的安全性，适合用于密码学应用
   - 建议收集更多样本或寻找生成过程的其他线索

7. 局限性说明:
   - 分析基于有限的样本数量
   - 可能存在未考虑到的复杂算法
   - 真随机数本质上无法预测
"""
        
        return report

def main():
    """主函数"""
    determiner = FinalAlgorithmDetermination("随机生成1-8.txt", "随机2000个数字.txt")
    final_report = determiner.generate_final_report()
    
    print(final_report)
    
    # 保存报告到文件
    with open("最终分析报告.txt", 'w', encoding='utf-8') as f:
        f.write(final_report)
    
    print(f"\n最终报告已保存到: 最终分析报告.txt")

if __name__ == "__main__":
    main()
