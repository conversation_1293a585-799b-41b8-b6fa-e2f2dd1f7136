#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
投注数据分析脚本
分析每期开奖房间与房间投入数据的关系
"""

import re
from collections import defaultdict

def parse_betting_log(file_path):
    """解析投注日志文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取投注记录
    betting_records = []
    betting_pattern = r'\| (\d{2}:\d{2}:\d{2}) \| (\d+) \| (\d+) \| ([\d.]+)元 \| (\d+) \| (获胜|失败) \| ([+-][\d.]+)元 \|'
    
    matches = re.findall(betting_pattern, content)
    for match in matches:
        time, period, bet_room, bet_amount, win_room, result, profit = match
        betting_records.append({
            'time': time,
            'period': int(period),
            'bet_room': int(bet_room),
            'bet_amount': float(bet_amount),
            'win_room': int(win_room),
            'result': result,
            'profit': float(profit.replace('+', ''))
        })
    
    # 提取房间统计信息
    room_stats = {}
    period_pattern = r'### 期号 (\d+)\n\n\| 房间号 \| 投入金额 \| 分享金额 \| 投入道具 \| 金额\+道具累计 \| 房间人数 \| 人均投入 \|\n\|.*?\n((?:\| 房间\d+ \| .*?\n)+)'
    
    period_matches = re.findall(period_pattern, content, re.MULTILINE | re.DOTALL)
    
    for period_match in period_matches:
        period_num = int(period_match[0])
        room_data_text = period_match[1]
        
        room_pattern = r'\| 房间(\d+) \| ([\d.]+)元 \| ([\d.]+)元 \| (\d+)道具 \| ([\d.]+)元✓ \| (\d+)人 \| ([\d.]+)元 \|'
        room_matches = re.findall(room_pattern, room_data_text)
        
        period_rooms = {}
        for room_match in room_matches:
            room_num, invest_amount, share_amount, props, total_invest, people_count, avg_invest = room_match
            period_rooms[int(room_num)] = {
                'invest_amount': float(invest_amount),
                'share_amount': float(share_amount),
                'props': int(props),
                'total_invest': float(total_invest),
                'people_count': int(people_count),
                'avg_invest': float(avg_invest)
            }
        
        room_stats[period_num] = period_rooms
    
    return betting_records, room_stats

def analyze_winning_patterns(betting_records, room_stats):
    """分析开奖房间与投入数据的关系"""
    analysis_results = []

    # 按期号分组分析
    periods_with_stats = set(room_stats.keys())

    for record in betting_records:
        period = record['period']
        if period not in periods_with_stats:
            continue

        win_room = record['win_room']
        period_room_data = room_stats[period]

        if win_room not in period_room_data:
            continue

        # 获取开奖房间的数据
        win_room_data = period_room_data[win_room]

        # 计算该房间在所有房间中的排名
        all_rooms_data = list(period_room_data.values())

        # 按投入金额排名
        invest_amounts = [room['invest_amount'] for room in all_rooms_data]
        invest_rank = sorted(invest_amounts, reverse=True).index(win_room_data['invest_amount']) + 1

        # 按总投入排名
        total_invests = [room['total_invest'] for room in all_rooms_data]
        total_rank = sorted(total_invests, reverse=True).index(win_room_data['total_invest']) + 1

        # 按人数排名
        people_counts = [room['people_count'] for room in all_rooms_data]
        people_rank = sorted(people_counts, reverse=True).index(win_room_data['people_count']) + 1

        analysis_results.append({
            'period': period,
            'win_room': win_room,
            'invest_amount': win_room_data['invest_amount'],
            'total_invest': win_room_data['total_invest'],
            'people_count': win_room_data['people_count'],
            'invest_rank': invest_rank,
            'total_rank': total_rank,
            'people_rank': people_rank,
            'total_rooms': len(period_room_data)
        })

    return analysis_results

def generate_statistics(analysis_results):
    """生成统计报告"""
    print("=" * 60)
    print("🎯 开奖房间与投入数据关系分析报告")
    print("=" * 60)

    total_periods = len(analysis_results)
    print(f"📊 分析期数: {total_periods}")
    print()

    # 统计各排名的出现次数
    invest_rank_counts = defaultdict(int)
    total_rank_counts = defaultdict(int)
    people_rank_counts = defaultdict(int)

    invest_ranks = []
    total_ranks = []
    people_ranks = []

    for result in analysis_results:
        invest_rank_counts[result['invest_rank']] += 1
        total_rank_counts[result['total_rank']] += 1
        people_rank_counts[result['people_rank']] += 1

        invest_ranks.append(result['invest_rank'])
        total_ranks.append(result['total_rank'])
        people_ranks.append(result['people_rank'])

    # 投入金额排名分析
    print("💰 按投入金额排名分析:")
    for rank in range(1, 9):
        count = invest_rank_counts.get(rank, 0)
        percentage = (count / total_periods) * 100
        print(f"  第{rank}名: {count}次 ({percentage:.1f}%)")

    print()

    # 总投入排名分析
    print("🏆 按总投入排名分析:")
    for rank in range(1, 9):
        count = total_rank_counts.get(rank, 0)
        percentage = (count / total_periods) * 100
        print(f"  第{rank}名: {count}次 ({percentage:.1f}%)")

    print()

    # 人数排名分析
    print("👥 按人数排名分析:")
    for rank in range(1, 9):
        count = people_rank_counts.get(rank, 0)
        percentage = (count / total_periods) * 100
        print(f"  第{rank}名: {count}次 ({percentage:.1f}%)")

    print()

    # 关键统计指标
    print("📈 关键统计指标:")
    invest_avg = sum(invest_ranks) / len(invest_ranks)
    total_avg = sum(total_ranks) / len(total_ranks)
    people_avg = sum(people_ranks) / len(people_ranks)

    print(f"  投入金额排名平均值: {invest_avg:.2f}")
    print(f"  总投入排名平均值: {total_avg:.2f}")
    print(f"  人数排名平均值: {people_avg:.2f}")
    print()

    # 趋势分析
    print("🔍 趋势分析:")

    # 投入最多的房间开奖频率
    top_invest_wins = invest_rank_counts[1]
    print(f"  投入金额最多的房间开奖: {top_invest_wins}次 ({(top_invest_wins/total_periods)*100:.1f}%)")

    # 投入最少的房间开奖频率
    bottom_invest_wins = invest_rank_counts[8]
    print(f"  投入金额最少的房间开奖: {bottom_invest_wins}次 ({(bottom_invest_wins/total_periods)*100:.1f}%)")

    # 总投入最多的房间开奖频率
    top_total_wins = total_rank_counts[1]
    print(f"  总投入最多的房间开奖: {top_total_wins}次 ({(top_total_wins/total_periods)*100:.1f}%)")

    print()

    # 结论
    print("📋 分析结论:")
    avg_rank = 4.5  # 8个房间的平均排名

    if invest_avg < avg_rank:
        print("  ✅ 开奖房间倾向于投入金额较高的房间")
    else:
        print("  ❌ 开奖房间与投入金额高低无明显关系")

    if total_avg < avg_rank:
        print("  ✅ 开奖房间倾向于总投入较高的房间")
    else:
        print("  ❌ 开奖房间与总投入高低无明显关系")

    if people_avg < avg_rank:
        print("  ✅ 开奖房间倾向于人数较多的房间")
    else:
        print("  ❌ 开奖房间与人数多少无明显关系")

def main():
    """主函数"""
    file_path = "betting_log_20250803_081244.md"
    
    print("🔄 正在解析投注日志文件...")
    betting_records, room_stats = parse_betting_log(file_path)
    
    print(f"✅ 解析完成! 共找到 {len(betting_records)} 条投注记录")
    print(f"✅ 共找到 {len(room_stats)} 期房间统计数据")
    
    print("\n🔄 正在分析开奖房间与投入数据的关系...")
    analysis_results = analyze_winning_patterns(betting_records, room_stats)

    print(f"✅ 分析完成! 共分析 {len(analysis_results)} 期数据")

    # 生成统计报告
    generate_statistics(analysis_results)

    # 保存分析结果到CSV文件
    with open('betting_analysis_results.csv', 'w', encoding='utf-8') as f:
        f.write('period,win_room,invest_amount,total_invest,people_count,invest_rank,total_rank,people_rank,total_rooms\n')
        for result in analysis_results:
            f.write(f"{result['period']},{result['win_room']},{result['invest_amount']},{result['total_invest']},{result['people_count']},{result['invest_rank']},{result['total_rank']},{result['people_rank']},{result['total_rooms']}\n")

    print(f"\n💾 分析结果已保存到 betting_analysis_results.csv")

if __name__ == "__main__":
    main()
