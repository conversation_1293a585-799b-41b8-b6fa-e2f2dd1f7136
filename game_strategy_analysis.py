#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏策略分析
基于实际游戏规则的最优策略
"""

import numpy as np
from collections import Counter

def load_sequence(filename):
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

def analyze_game_strategy():
    """分析游戏策略"""
    print("=== 游戏规则分析 ===")
    print("游戏规则：")
    print("- 系统从1-8随机开出一个数字")
    print("- 玩家选择一个数字投注")
    print("- 玩家数字 ≠ 开出数字 → 获胜")
    print("- 赔率：1:1.1 (投入100，获胜得110)")
    print()
    
    # 理论分析
    print("=== 理论分析 ===")
    win_prob = 7/8  # 获胜概率
    lose_prob = 1/8  # 失败概率
    payout = 1.1    # 赔率
    
    expected_return = win_prob * payout - lose_prob * 1.0
    print(f"理论获胜概率: {win_prob:.4f} ({win_prob*100:.2f}%)")
    print(f"理论失败概率: {lose_prob:.4f} ({lose_prob*100:.2f}%)")
    print(f"理论期望收益: {expected_return:.4f} ({expected_return*100:.2f}%)")
    print(f"结论: 这是一个对庄家有利的游戏 (期望收益-16.25%)")
    print()
    
    # 加载实际数据
    try:
        sequence = load_sequence("随机23607.txt")
        print(f"=== 基于实际数据分析 ({len(sequence)}个样本) ===")
        
        # 频率分析
        counter = Counter(sequence)
        print("各数字出现频率:")
        for i in range(1, 9):
            count = counter.get(i, 0)
            freq = count / len(sequence)
            print(f"  数字 {i}: {count:4d} 次 ({freq:.4f})")
        
        print()
        
        # 计算最优策略
        print("=== 最优投注策略 ===")
        
        # 找出出现频率最高的数字（最可能开出）
        most_frequent = counter.most_common(1)[0]
        most_frequent_num = most_frequent[0]
        most_frequent_prob = most_frequent[1] / len(sequence)
        
        print(f"最频繁数字: {most_frequent_num} (概率: {most_frequent_prob:.4f})")
        
        # 计算每个数字的投注价值
        print("\n各数字投注价值分析:")
        best_bet = None
        best_value = -1
        
        for bet_num in range(1, 9):
            # 如果投注这个数字，获胜概率 = 1 - 这个数字出现的概率
            lose_prob_actual = counter.get(bet_num, 0) / len(sequence)
            win_prob_actual = 1 - lose_prob_actual
            
            # 期望收益
            expected_value = win_prob_actual * 1.1 - lose_prob_actual * 1.0
            
            print(f"  投注数字 {bet_num}: 获胜概率 {win_prob_actual:.4f}, 期望收益 {expected_value:.4f}")
            
            if expected_value > best_value:
                best_value = expected_value
                best_bet = bet_num
        
        print(f"\n最优投注建议:")
        print(f"  建议投注数字: {best_bet}")
        print(f"  期望收益: {best_value:.4f}")
        print(f"  建议理由: 避开出现频率最高的数字 {most_frequent_num}")
        
        # 模拟投注效果
        print(f"\n=== 模拟投注效果 ===")
        
        # 策略1：总是投注最优数字
        wins_optimal = sum(1 for x in sequence if x != best_bet)
        win_rate_optimal = wins_optimal / len(sequence)
        total_return_optimal = wins_optimal * 1.1 - (len(sequence) - wins_optimal) * 1.0
        roi_optimal = (total_return_optimal / len(sequence)) * 100
        
        print(f"策略1 - 总是投注数字{best_bet}:")
        print(f"  获胜次数: {wins_optimal}/{len(sequence)}")
        print(f"  胜率: {win_rate_optimal:.4f} ({win_rate_optimal*100:.2f}%)")
        print(f"  总收益: {total_return_optimal:.2f}")
        print(f"  投资回报率: {roi_optimal:.2f}%")
        
        # 策略2：随机投注
        np.random.seed(42)
        random_bets = np.random.randint(1, 9, len(sequence))
        wins_random = sum(1 for i, x in enumerate(sequence) if x != random_bets[i])
        win_rate_random = wins_random / len(sequence)
        total_return_random = wins_random * 1.1 - (len(sequence) - wins_random) * 1.0
        roi_random = (total_return_random / len(sequence)) * 100
        
        print(f"\n策略2 - 随机投注:")
        print(f"  获胜次数: {wins_random}/{len(sequence)}")
        print(f"  胜率: {win_rate_random:.4f} ({win_rate_random*100:.2f}%)")
        print(f"  总收益: {total_return_random:.2f}")
        print(f"  投资回报率: {roi_random:.2f}%")
        
        # 策略3：避开最频繁数字
        avoid_most_frequent = [x for x in range(1, 9) if x != most_frequent_num][0]
        wins_avoid = sum(1 for x in sequence if x != avoid_most_frequent)
        win_rate_avoid = wins_avoid / len(sequence)
        total_return_avoid = wins_avoid * 1.1 - (len(sequence) - wins_avoid) * 1.0
        roi_avoid = (total_return_avoid / len(sequence)) * 100
        
        print(f"\n策略3 - 避开最频繁数字{most_frequent_num}，投注数字{avoid_most_frequent}:")
        print(f"  获胜次数: {wins_avoid}/{len(sequence)}")
        print(f"  胜率: {win_rate_avoid:.4f} ({win_rate_avoid*100:.2f}%)")
        print(f"  总收益: {total_return_avoid:.2f}")
        print(f"  投资回报率: {roi_avoid:.2f}%")
        
        print(f"\n=== 最终建议 ===")
        if best_value > 0:
            print(f"✅ 基于历史数据，投注数字{best_bet}有正期望收益")
            print(f"   期望收益: {best_value:.4f} ({best_value*100:.2f}%)")
            print(f"   历史模拟ROI: {roi_optimal:.2f}%")
        else:
            print(f"⚠️  所有投注选择都是负期望收益")
            print(f"   最佳选择仍是数字{best_bet}，但期望收益为{best_value:.4f}")
        
        print(f"\n💡 重要提醒:")
        print(f"   1. 这仍然是一个对庄家有利的游戏")
        print(f"   2. 历史数据不能保证未来结果")
        print(f"   3. 请合理控制投注金额和风险")
        print(f"   4. 建议设置止损点，避免过度投注")
        
    except FileNotFoundError:
        print("数据文件未找到，无法进行实际数据分析")

if __name__ == "__main__":
    analyze_game_strategy()
