#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试房间统计功能
验证API房间数据的提取和Markdown记录
"""

import json
from datetime import datetime
from api_framework import GameState, GameAPIClient
from real_time_logger import RealTimeLogger, log_result

def test_room_stats_extraction():
    """测试房间统计数据提取"""
    
    print("🧪 测试房间统计数据提取")
    print("=" * 50)
    
    # 模拟真实API响应数据 - 根据正确的计算公式: totalStroke = totalMedal*10 + totalBuyStroke
    mock_api_data = {
        "issue": 130396,
        "state": 2,
        "killNumber": 1,
        "myRoomNumber": 5,
        "myIsWin": 1,
        "myWinStroke": "0.11352",
        "countdown": 0,
        "room": [
            {"roomNumber": 1, "totalMedal": "227", "shareMedal": "45.4", "totalBuyStroke": 23, "totalStroke": str(227*10+23), "userCount": 23},      # 2293
            {"roomNumber": 2, "totalMedal": "43.5", "shareMedal": "8.7", "totalBuyStroke": 13, "totalStroke": str(43.5*10+13), "userCount": 13},    # 448
            {"roomNumber": 3, "totalMedal": "821", "shareMedal": "164.2", "totalBuyStroke": 15, "totalStroke": str(821*10+15), "userCount": 15},     # 8225
            {"roomNumber": 4, "totalMedal": "807", "shareMedal": "161.4", "totalBuyStroke": 25, "totalStroke": str(807*10+25), "userCount": 25},     # 8095
            {"roomNumber": 5, "totalMedal": "3015", "shareMedal": "603.0", "totalBuyStroke": 15, "totalStroke": str(3015*10+15), "userCount": 15},   # 30165
            {"roomNumber": 6, "totalMedal": "653", "shareMedal": "130.6", "totalBuyStroke": 22, "totalStroke": str(653*10+22), "userCount": 22},     # 6552
            {"roomNumber": 7, "totalMedal": "468", "shareMedal": "93.6", "totalBuyStroke": 23, "totalStroke": str(468*10+23), "userCount": 23},      # 4703
            {"roomNumber": 8, "totalMedal": "159", "shareMedal": "31.8", "totalBuyStroke": 30, "totalStroke": str(159*10+30), "userCount": 30}       # 1620
        ]
    }
    
    # 模拟GameState创建过程
    data = mock_api_data
    kill_number = data.get('killNumber', 0)
    
    # 提取收益数据
    my_win_stroke = 0.0
    if 'myWinStroke' in data:
        try:
            my_win_stroke = float(data['myWinStroke'])
        except (ValueError, TypeError):
            my_win_stroke = 0.0
    
    # 提取房间统计数据
    room_stats = {}
    if 'room' in data and isinstance(data['room'], list):
        for room_data in data['room']:
            room_number = room_data.get('roomNumber', 0)
            if room_number > 0:
                # 提取投入金额 (totalMedal)
                total_medal = 0.0
                if 'totalMedal' in room_data:
                    try:
                        total_medal = float(room_data['totalMedal'])
                    except (ValueError, TypeError):
                        total_medal = 0.0

                # 提取分享金额 (shareMedal)
                share_medal = 0.0
                if 'shareMedal' in room_data:
                    try:
                        share_medal = float(room_data['shareMedal'])
                    except (ValueError, TypeError):
                        share_medal = 0.0

                # 提取购买笔数 (totalBuyStroke)
                total_buy_stroke = 0
                if 'totalBuyStroke' in room_data:
                    try:
                        total_buy_stroke = int(room_data['totalBuyStroke'])
                    except (ValueError, TypeError):
                        total_buy_stroke = 0

                # 提取总笔数 (totalStroke)
                total_stroke = 0.0
                if 'totalStroke' in room_data:
                    try:
                        total_stroke = float(room_data['totalStroke'])
                    except (ValueError, TypeError):
                        total_stroke = 0.0

                # 提取用户数量 (userCount)
                user_count = room_data.get('userCount', 0)

                room_stats[room_number] = {
                    'total_medal': total_medal,
                    'share_medal': share_medal,
                    'total_buy_stroke': total_buy_stroke,
                    'total_stroke': total_stroke,
                    'user_count': user_count
                }
    
    # 创建GameState对象
    game_state = GameState(
        issue=data.get('issue', 0),
        state=data.get('state', 0),
        kill_number=kill_number,
        my_room_number=data.get('myRoomNumber', 0),
        my_is_win=data.get('myIsWin', 0),
        my_win_stroke=my_win_stroke,
        room_stats=room_stats,
        timestamp=datetime.now().isoformat(),
        countdown=data.get('countdown', 0)
    )
    
    # 显示提取结果
    print(f"📊 期号: {game_state.issue}")
    print(f"🎰 开奖房间: {game_state.kill_number}")
    print(f"💰 我的收益: {game_state.my_win_stroke:.5f}元")
    print(f"🏠 房间统计数据:")
    print(f"   {'房间':<4} {'投入金额':<8} {'分享金额':<8} {'投入道具':<6} {'金额+道具':<10} {'人数':<4} {'人均投入':<8}")
    print(f"   {'-'*4} {'-'*8} {'-'*8} {'-'*6} {'-'*10} {'-'*4} {'-'*8}")

    total_medal = 0.0
    total_share = 0.0
    total_buy_stroke = 0
    total_stroke = 0.0
    total_users = 0

    for room_num in range(1, 9):
        if room_num in game_state.room_stats:
            stats = game_state.room_stats[room_num]
            medal = stats['total_medal']
            share = stats['share_medal']
            buy_stroke = stats['total_buy_stroke']
            stroke = stats['total_stroke']
            users = stats['user_count']
            avg_per_user = medal / users if users > 0 else 0.0

            total_medal += medal
            total_share += share
            total_buy_stroke += buy_stroke
            total_stroke += stroke
            total_users += users

            # 验证计算公式: 投入金额*10 + 道具投入 = 总投入
            expected_stroke = medal * 10 + buy_stroke
            stroke_match = "✓" if abs(stroke - expected_stroke) < 0.1 else "✗"

            print(f"   房间{room_num:<2} {medal:<8.1f} {share:<8.1f} {buy_stroke:<6} {stroke:<8.1f}{stroke_match:<2} {users:<4} {avg_per_user:<8.2f}")
        else:
            print(f"   房间{room_num:<2} {'0.0':<8} {'0.0':<8} {'0':<6} {'0.0':<10} {'0':<4} {'0.00':<8}")

    print(f"   {'-'*4} {'-'*8} {'-'*8} {'-'*6} {'-'*10} {'-'*4} {'-'*8}")
    avg_total = total_medal / total_users if total_users > 0 else 0
    # 验证总计算公式
    expected_total_stroke = total_medal * 10 + total_buy_stroke
    total_match = "✓" if abs(total_stroke - expected_total_stroke) < 0.1 else "✗"
    print(f"   总计   {total_medal:<8.1f} {total_share:<8.1f} {total_buy_stroke:<6} {total_stroke:<8.1f}{total_match:<2} {total_users:<4} {avg_total:<8.2f}")
    
    return game_state

def test_markdown_room_statistics():
    """测试Markdown房间统计记录"""
    
    print(f"\n📝 测试Markdown房间统计记录")
    print("=" * 50)
    
    # 创建测试记录器
    logger = RealTimeLogger("test_room_stats")
    
    # 模拟投注记录
    logger.log_betting(130396, 5, 2.0, True)
    
    # 模拟房间统计数据 - 根据正确的计算公式
    room_stats = {
        1: {'total_medal': 227.0, 'share_medal': 45.4, 'total_buy_stroke': 23, 'total_stroke': 2293.0, 'user_count': 23},    # 227*10+23=2293
        2: {'total_medal': 43.5, 'share_medal': 8.7, 'total_buy_stroke': 13, 'total_stroke': 448.0, 'user_count': 13},      # 43.5*10+13=448
        3: {'total_medal': 821.0, 'share_medal': 164.2, 'total_buy_stroke': 15, 'total_stroke': 8225.0, 'user_count': 15},  # 821*10+15=8225
        4: {'total_medal': 807.0, 'share_medal': 161.4, 'total_buy_stroke': 25, 'total_stroke': 8095.0, 'user_count': 25},  # 807*10+25=8095
        5: {'total_medal': 3015.0, 'share_medal': 603.0, 'total_buy_stroke': 15, 'total_stroke': 30165.0, 'user_count': 15}, # 3015*10+15=30165
        6: {'total_medal': 653.0, 'share_medal': 130.6, 'total_buy_stroke': 22, 'total_stroke': 6552.0, 'user_count': 22},  # 653*10+22=6552
        7: {'total_medal': 468.0, 'share_medal': 93.6, 'total_buy_stroke': 23, 'total_stroke': 4703.0, 'user_count': 23},   # 468*10+23=4703
        8: {'total_medal': 159.0, 'share_medal': 31.8, 'total_buy_stroke': 30, 'total_stroke': 1620.0, 'user_count': 30}    # 159*10+30=1620
    }
    
    # 记录开奖结果和房间统计
    logger.log_result(130396, 1, "获胜", 0.11352, room_stats)
    
    print(f"✅ 测试记录已写入文件:")
    print(f"   📄 JSON: {logger.json_file}")
    print(f"   📊 CSV: {logger.csv_file}")
    print(f"   📝 Markdown: {logger.md_file}")
    
    return logger

def test_room_analysis():
    """测试房间数据分析"""
    
    print(f"\n📊 测试房间数据分析")
    print("=" * 50)
    
    # 使用真实数据进行分析
    room_stats = {
        1: {'total_medal': 227.0, 'user_count': 23},
        2: {'total_medal': 43.5, 'user_count': 13},
        3: {'total_medal': 821.0, 'user_count': 15},
        4: {'total_medal': 807.0, 'user_count': 25},
        5: {'total_medal': 3015.0, 'user_count': 15},
        6: {'total_medal': 653.0, 'user_count': 22},
        7: {'total_medal': 468.0, 'user_count': 23},
        8: {'total_medal': 159.0, 'user_count': 30}
    }
    
    # 分析投入金额
    sorted_by_medal = sorted(room_stats.items(), key=lambda x: x[1]['total_medal'], reverse=True)
    print(f"💰 按投入金额排序:")
    for i, (room, stats) in enumerate(sorted_by_medal, 1):
        medal = stats['total_medal']
        users = stats['user_count']
        avg = medal / users if users > 0 else 0
        print(f"   {i}. 房间{room}: {medal:.1f}元 ({users}人) 人均{avg:.2f}元")
    
    # 分析人数分布
    sorted_by_users = sorted(room_stats.items(), key=lambda x: x[1]['user_count'], reverse=True)
    print(f"\n👥 按人数排序:")
    for i, (room, stats) in enumerate(sorted_by_users, 1):
        medal = stats['total_medal']
        users = stats['user_count']
        avg = medal / users if users > 0 else 0
        print(f"   {i}. 房间{room}: {users}人 ({medal:.1f}元) 人均{avg:.2f}元")
    
    # 分析人均投入
    room_avg = [(room, stats['total_medal'] / stats['user_count'] if stats['user_count'] > 0 else 0, stats) 
                for room, stats in room_stats.items()]
    sorted_by_avg = sorted(room_avg, key=lambda x: x[1], reverse=True)
    
    print(f"\n💵 按人均投入排序:")
    for i, (room, avg, stats) in enumerate(sorted_by_avg, 1):
        medal = stats['total_medal']
        users = stats['user_count']
        print(f"   {i}. 房间{room}: 人均{avg:.2f}元 ({medal:.1f}元/{users}人)")
    
    # 统计摘要
    total_medal = sum(stats['total_medal'] for stats in room_stats.values())
    total_users = sum(stats['user_count'] for stats in room_stats.values())
    overall_avg = total_medal / total_users if total_users > 0 else 0
    
    print(f"\n📈 统计摘要:")
    print(f"   总投入: {total_medal:.1f}元")
    print(f"   总人数: {total_users}人")
    print(f"   整体人均: {overall_avg:.2f}元")
    print(f"   最高投入: 房间{sorted_by_medal[0][0]} ({sorted_by_medal[0][1]['total_medal']:.1f}元)")
    print(f"   最低投入: 房间{sorted_by_medal[-1][0]} ({sorted_by_medal[-1][1]['total_medal']:.1f}元)")
    print(f"   投入差异: {sorted_by_medal[0][1]['total_medal'] - sorted_by_medal[-1][1]['total_medal']:.1f}元")

def main():
    """主测试函数"""
    
    print("🔬 房间统计功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 执行各项测试
        game_state = test_room_stats_extraction()
        logger = test_markdown_room_statistics()
        test_room_analysis()
        
        print("\n" + "=" * 60)
        print("📊 测试总结:")
        print("   ✅ 房间统计数据提取 - 正常")
        print("   ✅ Markdown记录写入 - 正常")
        print("   ✅ 房间数据分析 - 正常")
        
        print(f"\n🎉 所有测试通过!")
        print(f"\n💡 现在系统将:")
        print(f"   1. 自动提取每个房间的投入金额和人数")
        print(f"   2. 在Markdown文件中生成详细的房间统计表格")
        print(f"   3. 提供房间投入分析和排序")
        print(f"   4. 显示人均投入和投入差异分析")
        
        print(f"\n📁 测试文件已生成:")
        print(f"   📝 {logger.md_file}")
        print(f"   📊 {logger.csv_file}")
        print(f"   📄 {logger.json_file}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
