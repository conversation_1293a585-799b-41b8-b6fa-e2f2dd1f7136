#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新盈利系统修复
验证动态规则生成是否正常工作
"""

import json
from new_profitable_system import NewProfitableSystem

def create_mock_api_client():
    """创建模拟API客户端"""
    
    class MockAPIClient:
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    return MockAPIClient()

def create_test_history():
    """创建测试历史数据"""
    
    # 创建一些有规律的测试数据
    test_history = [
        1, 2, 3, 4, 5, 6, 7, 8,  # 第1-8期
        2, 3, 4, 5, 6, 7, 8, 1,  # 第9-16期
        3, 4, 5, 6, 7, 8, 1, 2,  # 第17-24期
        4, 5, 6, 7, 8, 1, 2, 3,  # 第25-32期
        5, 6, 7, 8, 1, 2, 3, 4,  # 第33-40期
        # 添加一些重复模式以生成高置信度规则
        1, 2, 3, 4,  # 模式1
        1, 2, 3, 4,  # 模式1重复
        1, 2, 3, 4,  # 模式1再次重复
        5, 6, 7, 8,  # 模式2
        5, 6, 7, 8,  # 模式2重复
    ]
    
    return test_history

def test_dynamic_rule_generation():
    """测试动态规则生成"""
    
    print("🧪 测试新盈利系统动态规则生成")
    print("=" * 60)
    
    # 创建测试历史数据
    test_history = create_test_history()
    
    # 保存测试历史数据
    test_data = {
        'history': test_history,
        'last_updated': '2025-01-01T00:00:00',
        'total_periods': len(test_history)
    }
    
    with open("game_history.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 创建测试历史数据: {len(test_history)}期")
    print(f"   数据内容: {test_history}")
    
    # 创建系统
    api_client = create_mock_api_client()
    config = {
        'base_bet_amount': 1.0,
        'max_bet_amount': 10.0,
        'profit_target': 50.0,
        'stop_loss': -20.0
    }
    
    try:
        system = NewProfitableSystem(api_client, config)
        
        print(f"\n📊 系统初始化结果:")
        print(f"   历史数据: {len(system.history)}期")
        print(f"   动态规则: {len(system.dynamic_rules)}个")
        print(f"   静态规则: {len(system.prediction_adapter.high_confidence_rules)}个高置信度规则")
        
        # 测试预测功能
        print(f"\n🎯 测试预测功能:")
        
        test_cases = [
            [1, 2, 3],      # 应该匹配动态规则
            [5, 6, 7],      # 应该匹配动态规则
            [2, 3, 4],      # 可能匹配规则
            [8, 1, 2],      # 测试其他模式
        ]
        
        for i, recent_history in enumerate(test_cases, 1):
            print(f"\n--- 测试用例{i}: {recent_history} ---")
            
            # 模拟系统历史数据
            system.history = test_history + recent_history[:-1]  # 添加到历史中
            
            prediction = system.get_prediction()
            
            if prediction:
                print(f"✅ 预测成功:")
                print(f"   预测房间: {prediction['room']}")
                print(f"   置信度: {prediction['confidence']:.3f}")
                print(f"   规则类型: {prediction['rule_type']}")
                if 'condition' in prediction:
                    print(f"   匹配条件: {prediction['condition']}")
                    print(f"   支持度: {prediction['support']}")
            else:
                print(f"❌ 无匹配规则")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rule_regeneration():
    """测试规则重新生成"""
    
    print(f"\n🔄 测试动态规则重新生成")
    print("=" * 60)
    
    try:
        api_client = create_mock_api_client()
        config = {'base_bet_amount': 1.0}
        
        system = NewProfitableSystem(api_client, config)
        
        initial_rule_count = len(system.dynamic_rules)
        print(f"初始动态规则数量: {initial_rule_count}")
        
        # 添加新的历史数据
        new_data = [1, 1, 1, 2, 2, 2, 3, 3, 3, 4]  # 新的模式
        
        for room in new_data:
            system.history.append(room)
        
        # 手动触发规则重新生成
        system.generate_dynamic_rules()
        
        new_rule_count = len(system.dynamic_rules)
        print(f"重新生成后规则数量: {new_rule_count}")
        
        if new_rule_count > 0:
            print("✅ 动态规则重新生成成功")
            return True
        else:
            print("❌ 动态规则重新生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 规则重新生成测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行测试
    print("🚀 开始测试新盈利系统修复")
    
    success1 = test_dynamic_rule_generation()
    success2 = test_rule_regeneration()
    
    if success1 and success2:
        print(f"\n🎊 新盈利系统修复验证成功！")
        print(f"💡 现在系统应该能够:")
        print(f"   1. ✅ 自动生成动态规则")
        print(f"   2. ✅ 优先使用动态规则进行预测")
        print(f"   3. ✅ 动态规则无效时回退到静态规则")
        print(f"   4. ✅ 每10期自动重新生成规则")
        print(f"   5. ✅ 实时更新历史数据")
        print(f"\n🚀 建议重新启动新盈利系统:")
        print(f"   python new_profitable_system.py")
    else:
        print(f"\n⚠️ 新盈利系统仍需进一步修复")
