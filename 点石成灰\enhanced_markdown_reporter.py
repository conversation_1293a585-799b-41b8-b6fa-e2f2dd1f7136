#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Markdown报告生成器
为LCG随机投注系统提供完整的实时记录功能和美观的报告生成
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import deque, Counter
import statistics


class EnhancedMarkdownReporter:
    """增强版Markdown报告生成器"""
    
    def __init__(self, system_name: str = "LCG随机投注系统"):
        self.system_name = system_name
        self.session_start_time = datetime.now()
        self.report_dir = "reports"
        self.session_id = self.session_start_time.strftime("%Y%m%d_%H%M%S")
        
        # 创建报告目录
        os.makedirs(self.report_dir, exist_ok=True)
        
        # 数据存储
        self.betting_records = []
        self.result_records = []
        self.performance_metrics = {}
        self.risk_events = []
        self.algorithm_stats = {}
        
        print(f"📝 增强版Markdown报告生成器已初始化")
        print(f"   系统名称: {self.system_name}")
        print(f"   会话ID: {self.session_id}")
        print(f"   报告目录: {self.report_dir}")
    
    def record_betting(self, bet_info: Dict):
        """记录投注信息"""
        bet_record = {
            **bet_info,
            'timestamp': datetime.now().isoformat(),
            'session_id': self.session_id
        }
        self.betting_records.append(bet_record)
        
        # 实时更新算法统计
        self._update_algorithm_stats(bet_record)
        
        print(f"📝 记录投注: 期号{bet_info.get('issue')}, 房间{bet_info.get('room')}, 金额{bet_info.get('amount'):.2f}元")
    
    def record_result(self, result_info: Dict):
        """记录开奖结果"""
        result_record = {
            **result_info,
            'timestamp': datetime.now().isoformat(),
            'session_id': self.session_id
        }
        self.result_records.append(result_record)
        
        # 更新性能指标
        self._update_performance_metrics(result_record)
        
        # 检查风险事件
        self._check_risk_events(result_record)
        
        print(f"📊 记录结果: 期号{result_info.get('issue')}, {'获胜' if result_info.get('is_win') else '失败'}, 盈亏{result_info.get('profit'):+.2f}元")
    
    def record_risk_event(self, event_type: str, description: str, severity: str = "medium"):
        """记录风险事件"""
        risk_event = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'description': description,
            'severity': severity,
            'session_id': self.session_id
        }
        self.risk_events.append(risk_event)
        
        print(f"⚠️ 风险事件: {event_type} - {description} (严重程度: {severity})")
    
    def _update_algorithm_stats(self, bet_record: Dict):
        """更新算法统计信息"""
        algorithm = bet_record.get('algorithm', 'unknown')
        
        if algorithm not in self.algorithm_stats:
            self.algorithm_stats[algorithm] = {
                'total_bets': 0,
                'rooms_selected': Counter(),
                'generator_states': [],
                'amounts_used': [],
                'risk_levels': Counter()
            }
        
        stats = self.algorithm_stats[algorithm]
        stats['total_bets'] += 1
        stats['rooms_selected'][bet_record.get('room')] += 1
        stats['generator_states'].append(bet_record.get('generator_state'))
        stats['amounts_used'].append(bet_record.get('amount', 0))
        stats['risk_levels'][bet_record.get('risk_level', 'unknown')] += 1
    
    def _update_performance_metrics(self, result_record: Dict):
        """更新性能指标"""
        if not self.result_records:
            return
        
        total_bets = len(self.result_records)
        wins = sum(1 for r in self.result_records if r.get('is_win', False))
        losses = total_bets - wins
        
        total_profit = sum(r.get('profit', 0) for r in self.result_records)
        win_amounts = [r.get('profit', 0) for r in self.result_records if r.get('is_win', False)]
        loss_amounts = [abs(r.get('profit', 0)) for r in self.result_records if not r.get('is_win', False)]
        
        # 连胜连败统计
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_streak = 0
        current_streak_type = None
        
        for record in self.result_records:
            if record.get('is_win', False):
                if current_streak_type == 'win':
                    current_streak += 1
                else:
                    current_streak = 1
                    current_streak_type = 'win'
                max_consecutive_wins = max(max_consecutive_wins, current_streak)
                consecutive_wins = current_streak if current_streak_type == 'win' else 0
            else:
                if current_streak_type == 'loss':
                    current_streak += 1
                else:
                    current_streak = 1
                    current_streak_type = 'loss'
                max_consecutive_losses = max(max_consecutive_losses, current_streak)
                consecutive_losses = current_streak if current_streak_type == 'loss' else 0
        
        self.performance_metrics = {
            'total_bets': total_bets,
            'wins': wins,
            'losses': losses,
            'win_rate': wins / total_bets if total_bets > 0 else 0,
            'total_profit': total_profit,
            'average_profit_per_bet': total_profit / total_bets if total_bets > 0 else 0,
            'average_win_amount': statistics.mean(win_amounts) if win_amounts else 0,
            'average_loss_amount': statistics.mean(loss_amounts) if loss_amounts else 0,
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'current_consecutive_wins': consecutive_wins,
            'current_consecutive_losses': consecutive_losses,
            'current_balance': result_record.get('current_balance', 0),
            'roi': (total_profit / 100.0) * 100 if total_bets > 0 else 0  # 假设初始余额100
        }
    
    def _check_risk_events(self, result_record: Dict):
        """检查并记录风险事件"""
        consecutive_losses = result_record.get('consecutive_losses', 0)
        current_balance = result_record.get('current_balance', 0)
        total_profit = result_record.get('total_profit', 0)
        
        # 连续失败风险
        if consecutive_losses >= 3:
            severity = "high" if consecutive_losses >= 5 else "medium"
            self.record_risk_event(
                "连续失败",
                f"连续失败{consecutive_losses}次",
                severity
            )
        
        # 余额风险
        if current_balance < 80:  # 假设初始余额100
            severity = "critical" if current_balance < 50 else "high"
            self.record_risk_event(
                "余额不足",
                f"当前余额{current_balance:.2f}元，低于安全线",
                severity
            )
        
        # 亏损风险
        if total_profit < -20:
            severity = "critical" if total_profit < -50 else "high"
            self.record_risk_event(
                "累计亏损",
                f"累计亏损{abs(total_profit):.2f}元",
                severity
            )
    
    def generate_session_report(self) -> str:
        """生成会话报告"""
        report_time = datetime.now()
        session_duration = report_time - self.session_start_time
        
        report = f"""# 🎲 {self.system_name} - 会话报告

## 📋 会话信息
- **会话ID**: {self.session_id}
- **开始时间**: {self.session_start_time.strftime('%Y-%m-%d %H:%M:%S')}
- **报告时间**: {report_time.strftime('%Y-%m-%d %H:%M:%S')}
- **会话时长**: {str(session_duration).split('.')[0]}
- **系统版本**: LCG随机投注系统 v2.0

## 📊 核心性能指标

### 🎯 投注统计
- **总投注次数**: {self.performance_metrics.get('total_bets', 0)}次
- **获胜次数**: {self.performance_metrics.get('wins', 0)}次
- **失败次数**: {self.performance_metrics.get('losses', 0)}次
- **胜率**: {self.performance_metrics.get('win_rate', 0)*100:.2f}%
- **LCG算法预期胜率**: 88.21%

### 💰 盈亏分析
- **总盈亏**: {self.performance_metrics.get('total_profit', 0):+.2f}元
- **当前余额**: {self.performance_metrics.get('current_balance', 0):.2f}元
- **投资回报率**: {self.performance_metrics.get('roi', 0):+.2f}%
- **平均每注盈亏**: {self.performance_metrics.get('average_profit_per_bet', 0):+.4f}元
- **平均获胜金额**: {self.performance_metrics.get('average_win_amount', 0):.2f}元
- **平均失败金额**: {self.performance_metrics.get('average_loss_amount', 0):.2f}元

### 🔥 连胜连败记录
- **最大连胜**: {self.performance_metrics.get('max_consecutive_wins', 0)}次
- **最大连败**: {self.performance_metrics.get('max_consecutive_losses', 0)}次
- **当前连胜**: {self.performance_metrics.get('current_consecutive_wins', 0)}次
- **当前连败**: {self.performance_metrics.get('current_consecutive_losses', 0)}次

## 🧮 LCG算法分析

"""
        
        # 添加算法统计
        for algorithm, stats in self.algorithm_stats.items():
            report += f"""### {algorithm}算法统计
- **使用次数**: {stats['total_bets']}次
- **房间选择分布**:
"""
            for room in range(1, 9):
                count = stats['rooms_selected'].get(room, 0)
                percentage = (count / stats['total_bets'] * 100) if stats['total_bets'] > 0 else 0
                report += f"  - 房间{room}: {count}次 ({percentage:.1f}%)\n"
            
            if stats['amounts_used']:
                avg_amount = statistics.mean(stats['amounts_used'])
                min_amount = min(stats['amounts_used'])
                max_amount = max(stats['amounts_used'])
                report += f"- **投注金额**: 平均{avg_amount:.2f}元, 范围{min_amount:.2f}-{max_amount:.2f}元\n"
            
            report += f"- **风险等级分布**: {dict(stats['risk_levels'])}\n\n"
        
        # 添加详细投注记录
        if self.betting_records:
            report += """## 📝 详细投注记录

| 期号 | 时间 | LCG房间 | 金额 | 生成器状态 | 风险等级 | 开奖房间 | 结果 | 盈亏 | 累计盈亏 |
|------|------|---------|------|------------|----------|----------|------|------|----------|
"""
            
            for i, bet in enumerate(self.betting_records):
                result = self.result_records[i] if i < len(self.result_records) else {}
                
                bet_time = datetime.fromisoformat(bet['timestamp']).strftime('%H:%M:%S')
                issue = bet.get('issue', 'N/A')
                room = bet.get('room', 'N/A')
                amount = bet.get('amount', 0)
                generator_state = bet.get('generator_state', 'N/A')
                risk_level = bet.get('risk_level', 'N/A')
                
                winning_room = result.get('winning_room', 'N/A')
                is_win = result.get('is_win', False)
                profit = result.get('profit', 0)
                total_profit = result.get('total_profit', 0)
                
                result_emoji = "✅" if is_win else "❌"
                
                report += f"| {issue} | {bet_time} | {room} | {amount:.2f} | {generator_state} | {risk_level} | {winning_room} | {result_emoji} | {profit:+.2f} | {total_profit:+.2f} |\n"
        
        # 添加风险事件
        if self.risk_events:
            report += """
## ⚠️ 风险事件记录

| 时间 | 事件类型 | 描述 | 严重程度 |
|------|----------|------|----------|
"""
            for event in self.risk_events:
                event_time = datetime.fromisoformat(event['timestamp']).strftime('%H:%M:%S')
                severity_emoji = {"low": "🟢", "medium": "🟡", "high": "🟠", "critical": "🔴"}.get(event['severity'], "⚪")
                report += f"| {event_time} | {event['event_type']} | {event['description']} | {severity_emoji} {event['severity']} |\n"
        
        # 添加算法性能分析
        if self.performance_metrics.get('total_bets', 0) > 0:
            actual_win_rate = self.performance_metrics.get('win_rate', 0)
            expected_win_rate = 0.8821  # LCG算法预期胜率
            performance_vs_expected = (actual_win_rate - expected_win_rate) / expected_win_rate * 100
            
            report += f"""
## 🔬 算法性能分析

### LCG算法表现评估
- **实际胜率**: {actual_win_rate*100:.2f}%
- **预期胜率**: {expected_win_rate*100:.2f}%
- **性能差异**: {performance_vs_expected:+.2f}%
- **算法状态**: {'✅ 表现优于预期' if performance_vs_expected > 0 else '⚠️ 表现低于预期' if performance_vs_expected > -5 else '❌ 表现明显低于预期'}

### 数学期望分析
- **理论单注期望**: -0.0297元 (88.21% × 0.1 - 11.79% × 1)
- **实际单注期望**: {self.performance_metrics.get('average_profit_per_bet', 0):.4f}元
- **期望偏差**: {(self.performance_metrics.get('average_profit_per_bet', 0) + 0.0297)*100:.2f}%

### 风险控制效果
- **最大回撤**: {min(0, min([r.get('total_profit', 0) for r in self.result_records] or [0])):.2f}元
- **风险事件数量**: {len(self.risk_events)}次
- **高风险事件**: {len([e for e in self.risk_events if e['severity'] in ['high', 'critical']])}次
"""
        
        # 添加建议和总结
        report += """
## 💡 智能建议

### 基于当前表现的建议
"""
        
        if self.performance_metrics.get('win_rate', 0) > 0.85:
            report += "- ✅ **算法表现良好**: 当前胜率符合预期，可以继续使用当前策略\n"
        elif self.performance_metrics.get('win_rate', 0) > 0.80:
            report += "- ⚠️ **算法表现一般**: 胜率略低于预期，建议观察更多数据\n"
        else:
            report += "- ❌ **算法表现不佳**: 胜率明显低于预期，建议暂停使用或调整参数\n"
        
        if self.performance_metrics.get('total_profit', 0) > 0:
            report += "- 💰 **盈利状态**: 当前处于盈利状态，建议适当提高投注金额\n"
        elif self.performance_metrics.get('total_profit', 0) > -10:
            report += "- 📊 **平衡状态**: 盈亏基本平衡，建议保持当前策略\n"
        else:
            report += "- 🚨 **亏损状态**: 当前处于亏损状态，建议降低投注金额或暂停投注\n"
        
        consecutive_losses = self.performance_metrics.get('current_consecutive_losses', 0)
        if consecutive_losses >= 3:
            report += f"- ⚠️ **连败风险**: 当前连败{consecutive_losses}次，建议谨慎投注\n"
        
        report += f"""
### 下一步行动建议
1. **继续监控**: 保持详细的投注记录和结果分析
2. **参数调整**: 根据实际表现微调投注金额和风险控制参数
3. **数据积累**: 收集更多数据以验证LCG算法的长期有效性
4. **风险管理**: 严格执行止损规则，避免过度投注

## 📈 会话总结

本次会话共进行了{self.performance_metrics.get('total_bets', 0)}次投注，使用LCG随机算法选择房间。
实际胜率为{self.performance_metrics.get('win_rate', 0)*100:.2f}%，总盈亏为{self.performance_metrics.get('total_profit', 0):+.2f}元。

**系统状态**: {'🟢 正常运行' if len([e for e in self.risk_events if e['severity'] == 'critical']) == 0 else '🔴 需要关注'}

---
*报告生成时间: {report_time.strftime('%Y-%m-%d %H:%M:%S')}*  
*系统版本: LCG随机投注系统 v2.0*
"""
        
        return report
    
    def save_session_report(self) -> str:
        """保存会话报告到文件"""
        report_content = self.generate_session_report()
        
        filename = f"{self.report_dir}/LCG投注系统报告_{self.session_id}.md"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📝 会话报告已保存: {filename}")
        return filename
    
    def save_raw_data(self) -> str:
        """保存原始数据到JSON文件"""
        raw_data = {
            'session_info': {
                'session_id': self.session_id,
                'system_name': self.system_name,
                'start_time': self.session_start_time.isoformat(),
                'end_time': datetime.now().isoformat()
            },
            'betting_records': self.betting_records,
            'result_records': self.result_records,
            'performance_metrics': self.performance_metrics,
            'algorithm_stats': {k: {**v, 'rooms_selected': dict(v['rooms_selected']), 'risk_levels': dict(v['risk_levels'])} for k, v in self.algorithm_stats.items()},
            'risk_events': self.risk_events
        }
        
        filename = f"{self.report_dir}/LCG投注系统数据_{self.session_id}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(raw_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 原始数据已保存: {filename}")
        return filename
    
    def generate_real_time_summary(self) -> str:
        """生成实时摘要"""
        if not self.performance_metrics:
            return "📊 暂无投注数据"
        
        total_bets = self.performance_metrics.get('total_bets', 0)
        win_rate = self.performance_metrics.get('win_rate', 0)
        total_profit = self.performance_metrics.get('total_profit', 0)
        consecutive_wins = self.performance_metrics.get('current_consecutive_wins', 0)
        consecutive_losses = self.performance_metrics.get('current_consecutive_losses', 0)
        
        summary = f"""📊 LCG投注系统实时摘要:
   投注次数: {total_bets}次
   胜率: {win_rate*100:.1f}% (预期88.21%)
   总盈亏: {total_profit:+.2f}元
   连胜: {consecutive_wins}次 | 连败: {consecutive_losses}次
   风险事件: {len(self.risk_events)}次"""
        
        return summary
