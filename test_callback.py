#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回调机制
验证监控器的回调函数是否正常工作
"""

import time
from api_framework import GameAPIClient, GameMonitor, GameState

def test_callback_function(state: GameState):
    """测试回调函数"""
    print(f"\n🎉 回调函数被调用!")
    print(f"   期号: {state.issue}")
    print(f"   状态: {state.state}")
    print(f"   开出房间: {state.kill_number}")
    print(f"   倒计时: {state.countdown}")
    
    # 模拟数据保存
    print(f"💾 模拟保存数据: 房间{state.kill_number}")

def main():
    """主函数"""
    
    print("🧪 测试回调机制")
    print("=" * 50)
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    # 创建API客户端和监控器
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    monitor = GameMonitor(api_client)
    
    print("🔄 开始监控，等待开奖...")
    print("按 Ctrl+C 停止")
    
    try:
        # 启动监控，传入回调函数
        monitor.start_monitoring(callback=test_callback_function)
    except KeyboardInterrupt:
        print("\n🛑 测试停止")
    finally:
        monitor.stop_monitoring()
        print("✅ 测试完成")

if __name__ == "__main__":
    main()
