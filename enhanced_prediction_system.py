#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强预测系统
结合历史数据文件和动态规则生成的完整预测系统
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Optional
from collections import defaultdict, Counter

from api_framework import GameAPIClient, GameMonitor, GameState
from prediction_strategy_adapter import PredictionRuleAdapter
from real_time_logger import log_prediction, log_room_selection, log_betting, log_result, get_summary

class EnhancedPredictionSystem:
    """增强预测系统"""
    
    def __init__(self, api_client: GameAPIClient, config: Dict):
        """初始化增强预测系统"""
        self.api_client = api_client
        self.config = config
        self.monitor = GameMonitor(api_client)
        
        # 数据文件
        self.history_file = "game_history.json"
        
        # 预测器
        self.static_adapter = PredictionRuleAdapter()
        self.load_static_rules()
        
        # 历史数据和动态规则
        self.history = []
        self.dynamic_rules = []
        
        # 系统状态
        self.is_running = False
        self.betting_enabled = True
        self.total_bets = 0
        self.total_wins = 0
        self.total_profit = 0.0
        self.last_bet_issue = 0  # 最后投注的期号

        # 投注记录跟踪
        self.current_bet = None  # 当前期的投注信息
        
        # 加载历史数据
        self.load_history_data()
        
        # 生成动态规则
        if len(self.history) >= 20:
            self.generate_dynamic_rules()
        
        print("🚀 增强预测系统已初始化")
    
    def load_static_rules(self):
        """加载静态预测规则"""
        rules = self.static_adapter.load_prediction_rules_from_analysis()
        self.static_adapter.categorize_rules(rules)
        print(f"📚 已加载静态规则: {len(rules)}个")
    
    def load_history_data(self):
        """加载历史数据"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.history = data.get('history', [])
                self.total_bets = data.get('total_bets', 0)
                self.total_wins = data.get('total_wins', 0)
                self.total_profit = data.get('total_profit', 0.0)
                
                print(f"✅ 成功加载历史数据: {len(self.history)}期")
                
                if len(self.history) < 20:
                    print(f"⚠️  历史数据不足，建议先使用频率策略收集数据")
                    
        except FileNotFoundError:
            print("📝 未找到历史数据文件")
            print("建议先运行 frequency_based_betting.py 收集数据")
            self.history = []
        except Exception as e:
            print(f"❌ 加载历史数据失败: {e}")
            self.history = []
    
    def generate_dynamic_rules(self):
        """基于历史数据生成动态规则"""
        
        if len(self.history) < 20:
            print("📊 历史数据不足，无法生成动态规则")
            return
        
        print(f"🔄 基于{len(self.history)}期历史数据生成动态规则...")
        
        rules = []
        
        # 生成3-5元条件规则
        for condition_length in [3, 4, 5]:
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, len(self.history)):
                condition = tuple(self.history[i-condition_length:i])
                next_val = self.history[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            # 提取规则
            min_support = 2
            min_confidence = 0.6 if condition_length == 3 else 0.7 if condition_length == 4 else 0.8
            
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    if confidence >= min_confidence:
                        rules.append({
                            'condition': condition,
                            'predicted_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length,
                            'source': 'dynamic'
                        })
        
        # 按置信度排序
        rules.sort(key=lambda x: x['confidence'], reverse=True)
        self.dynamic_rules = rules
        
        print(f"✅ 生成动态规则: {len(rules)}个")
        if rules:
            print("最佳动态规则:")
            for i, rule in enumerate(rules[:5], 1):
                print(f"  {i}. {rule['condition']} -> {rule['predicted_value']} "
                      f"(置信度: {rule['confidence']:.3f})")
    
    def predict_next_room(self, recent_history: List[int], min_confidence: float = 0.6) -> Optional[Dict]:
        """预测下一个房间（优先动态规则）"""
        
        # 1. 首先尝试动态规则
        for rule in self.dynamic_rules:
            if rule['confidence'] < min_confidence:
                continue
                
            condition = rule['condition']
            condition_length = len(condition)
            
            if len(recent_history) >= condition_length:
                recent_sequence = tuple(recent_history[-condition_length:])
                
                if recent_sequence == condition:
                    return {
                        'predicted_room': rule['predicted_value'],
                        'confidence': rule['confidence'],
                        'rule_type': 'dynamic',
                        'condition': condition,
                        'support': rule['support']
                    }
        
        # 2. 尝试静态规则
        static_prediction = self.static_adapter.predict_next_room(recent_history, min_confidence)
        if static_prediction:
            static_prediction['rule_type'] = 'static'
            return static_prediction
        
        return None

    def select_best_bet_room(self, available_rooms: List[int], predicted_room: int, confidence: float) -> int:
        """智能选择最佳投注房间"""

        if not available_rooms:
            return 1  # 默认房间

        print(f"🎯 智能房间选择:")
        print(f"   预测房间: {predicted_room} (避开)")
        print(f"   可选房间: {available_rooms}")

        # 初始化选择详情
        selection_details = {
            'predicted_room': predicted_room,
            'available_rooms': available_rooms,
            'confidence': confidence
        }

        # 策略1: 基于历史频率选择出现频率最低的房间
        if len(self.history) >= 20:
            recent_history = self.history[-20:]  # 最近20期
            from collections import Counter
            counter = Counter(recent_history)

            # 计算可选房间的出现频率
            room_frequencies = {}
            for room in available_rooms:
                room_frequencies[room] = counter.get(room, 0)

            # 选择出现频率最低的房间
            min_frequency = min(room_frequencies.values())
            best_rooms = [room for room, freq in room_frequencies.items() if freq == min_frequency]

            print(f"   频率分析: {room_frequencies}")
            print(f"   最低频率: {min_frequency}次")
            print(f"   最佳房间: {best_rooms}")

            # 如果有多个最佳房间，选择中间的
            if len(best_rooms) == 1:
                selected_room = best_rooms[0]
                strategy = "频率最低"
                print(f"   选择策略: {strategy} → 房间{selected_room}")

                # 记录选择详情
                selection_details.update({
                    'strategy': strategy,
                    'room_frequencies': room_frequencies,
                    'min_frequency': min_frequency,
                    'best_rooms': best_rooms
                })
                log_room_selection(getattr(self, 'current_issue', 0), available_rooms, selected_room, strategy, selection_details)
                return selected_room
            else:
                # 多个房间频率相同，选择中位数
                best_rooms.sort()
                selected_room = best_rooms[len(best_rooms) // 2]
                strategy = "频率相同，选择中位数"
                print(f"   选择策略: {strategy} → 房间{selected_room}")

                # 记录选择详情
                selection_details.update({
                    'strategy': strategy,
                    'room_frequencies': room_frequencies,
                    'min_frequency': min_frequency,
                    'best_rooms': best_rooms
                })
                log_room_selection(getattr(self, 'current_issue', 0), available_rooms, selected_room, strategy, selection_details)
                return selected_room

        # 策略2: 基于置信度选择
        if confidence >= 0.9:
            # 高置信度：选择中间房间（更保守）
            available_rooms.sort()
            selected_room = available_rooms[len(available_rooms) // 2]
            strategy = "高置信度，选择中位数"
            print(f"   选择策略: {strategy} → 房间{selected_room}")

            # 记录选择详情
            selection_details.update({
                'strategy': strategy,
                'confidence_level': 'high'
            })
            log_room_selection(getattr(self, 'current_issue', 0), available_rooms, selected_room, strategy, selection_details)
            return selected_room
        elif confidence >= 0.7:
            # 中等置信度：选择频率较低的房间
            if len(self.history) >= 10:
                recent_history = self.history[-10:]
                from collections import Counter
                counter = Counter(recent_history)

                # 选择频率最低的前3个房间中的中间一个
                room_frequencies = [(room, counter.get(room, 0)) for room in available_rooms]
                room_frequencies.sort(key=lambda x: x[1])  # 按频率排序

                top_3 = room_frequencies[:3] if len(room_frequencies) >= 3 else room_frequencies
                selected_room = top_3[len(top_3) // 2][0]
                strategy = "中等置信度，频率较低"
                print(f"   选择策略: {strategy} → 房间{selected_room}")

                # 记录选择详情
                selection_details.update({
                    'strategy': strategy,
                    'confidence_level': 'medium',
                    'room_frequencies': dict(room_frequencies)
                })
                log_room_selection(getattr(self, 'current_issue', 0), available_rooms, selected_room, strategy, selection_details)
                return selected_room

        # 策略3: 默认策略 - 避免总是选择房间1
        # 根据预测房间选择对应的投注房间
        room_mapping = {
            1: 5,  # 预测1 → 投注5 (中间房间)
            2: 6,  # 预测2 → 投注6
            3: 7,  # 预测3 → 投注7
            4: 8,  # 预测4 → 投注8
            5: 1,  # 预测5 → 投注1
            6: 2,  # 预测6 → 投注2
            7: 3,  # 预测7 → 投注3
            8: 4,  # 预测8 → 投注4
        }

        preferred_room = room_mapping.get(predicted_room, 5)

        # 如果首选房间可用，选择它
        if preferred_room in available_rooms:
            selected_room = preferred_room
            strategy = "映射策略"
            print(f"   选择策略: {strategy} → 房间{selected_room}")

            # 记录选择详情
            selection_details.update({
                'strategy': strategy,
                'room_mapping': f"{predicted_room} → {preferred_room}",
                'preferred_room': preferred_room
            })
            log_room_selection(getattr(self, 'current_issue', 0), available_rooms, selected_room, strategy, selection_details)
            return selected_room

        # 否则选择中间房间
        available_rooms.sort()
        selected_room = available_rooms[len(available_rooms) // 2]
        strategy = "默认中位数"
        print(f"   选择策略: {strategy} → 房间{selected_room}")

        # 记录选择详情
        selection_details.update({
            'strategy': strategy,
            'fallback_reason': f"首选房间{preferred_room}不可用"
        })
        log_room_selection(getattr(self, 'current_issue', 0), available_rooms, selected_room, strategy, selection_details)
        return selected_room

    def should_place_bet(self) -> bool:
        """判断是否应该投注"""
        
        # 检查历史数据是否充足
        if len(self.history) < 10:
            print(f"📊 历史数据不足({len(self.history)}/10)")
            return False
        
        # 其他风险控制检查...
        return True
    
    def execute_prediction_bet(self, prediction: Dict):
        """执行基于预测的投注"""
        
        predicted_room = prediction['predicted_room']
        confidence = prediction['confidence']
        rule_type = prediction['rule_type']
        
        print(f"🎯 预测投注:")
        print(f"   预测房间: {predicted_room}")
        print(f"   置信度: {confidence:.3f}")
        print(f"   规则类型: {rule_type}")

        # 记录预测信息
        log_prediction(self.current_issue, predicted_room, confidence, rule_type)
        
        # 智能单房间投注策略（避开预测房间）
        all_rooms = list(range(1, 9))
        bet_rooms = [room for room in all_rooms if room != predicted_room]

        # 智能选择投注房间（而不是总是选择第一个）
        target_room = self.select_best_bet_room(bet_rooms, predicted_room, confidence)
        bet_amount = self.config.get('base_bet_amount', 0.1)

        print(f"💰 单房间投注: 房间{target_room}，金额{bet_amount:.2f}")
        print(f"📊 策略: 避开预测房间{predicted_room}，投注房间{target_room}")

        bet_result = self.api_client.place_bet(target_room, bet_amount)

        if bet_result.success:
            self.total_bets += 1
            print(f"✅ 房间{target_room}投注成功")

            # 记录投注信息
            log_betting(self.current_issue, target_room, bet_amount, True)

            # 记录当前投注信息
            self.current_bet = {
                'issue': getattr(self, 'current_issue', 0),
                'bet_room': target_room,
                'bet_amount': bet_amount,
                'predicted_room': predicted_room,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat()
            }

            # 显示投注响应详情
            if hasattr(bet_result, 'data') and bet_result.data:
                response_data = bet_result.data
                print(f"📊 投注响应详情:")
                print(f"   消耗金额: {response_data.get('costMedal', 'N/A')}")
                print(f"   剩余余额: {response_data.get('myMedal', 'N/A')}")
                print(f"   投注房间: {response_data.get('roomNumber', 'N/A')}")
                print(f"   总投注量: {response_data.get('totalBuyQuantity', 'N/A')}")

            print(f"📝 记录投注: 房间{target_room}")
        else:
            print(f"❌ 房间{target_room}投注失败: {bet_result.message}")

            # 记录投注失败
            log_betting(self.current_issue, target_room, bet_amount, False)
    
    def process_new_result(self, state: GameState):
        """处理游戏状态（投注时机和开奖结果）"""

        if state.state == 1:  # 等待开奖状态 - 投注时机
            self.handle_betting_opportunity(state)
        elif state.state == 2:  # 已开奖状态 - 处理结果
            self.handle_lottery_result(state)

    def handle_betting_opportunity(self, state: GameState):
        """处理投注时机"""

        print(f"\n🎯 投注时机: 期号{state.issue}, 倒计时{state.countdown}秒")

        # 记录当前期号
        self.current_issue = state.issue

        # 检查是否已经投注过这一期
        if state.issue <= self.last_bet_issue:
            print(f"📊 期号{state.issue}已投注过，跳过")
            return

        # 进行预测和投注
        if self.should_place_bet():
            recent_history = self.history[-10:] if len(self.history) >= 10 else self.history
            prediction = self.predict_next_room(recent_history, 0.6)

            if prediction:
                self.execute_prediction_bet(prediction)
                self.last_bet_issue = state.issue  # 记录已投注期号
            else:
                print("📊 未找到匹配的预测规则")
        else:
            print("📊 当前不适合投注")

    def handle_lottery_result(self, state: GameState):
        """处理开奖结果"""

        print(f"\n🎯 新开奖: 期号{state.issue}, 开出房间{state.kill_number}")

        # 分析投注结果
        self.analyze_betting_result(state)

        # 更新历史记录
        if state.kill_number > 0:
            if not self.history or state.issue != getattr(self, 'last_issue', 0):
                self.history.append(state.kill_number)
                self.last_issue = state.issue

                # 保存历史数据
                self.save_history_data()

                print(f"📊 历史数据更新: 共{len(self.history)}期")

                # 重新生成动态规则
                if len(self.history) % 10 == 0:  # 每10期重新生成规则
                    self.generate_dynamic_rules()

    def analyze_betting_result(self, state: GameState):
        """分析投注结果"""

        if not self.current_bet or self.current_bet['issue'] != state.issue:
            print("📊 本期未投注，无需分析")
            return

        bet_room = self.current_bet['bet_room']
        actual_room = state.kill_number
        bet_amount = self.current_bet['bet_amount']
        predicted_room = self.current_bet['predicted_room']
        confidence = self.current_bet['confidence']

        print(f"\n📊 投注结果分析:")
        print(f"   预测房间: {predicted_room} (置信度: {confidence:.3f})")
        print(f"   投注房间: {bet_room}")
        print(f"   开出房间: {actual_room}")
        print(f"   投注金额: {bet_amount:.2f}元")

        # 判断投注结果
        if bet_room != actual_room:
            # 获胜：投注房间不是开出房间
            result = "获胜"
            profit = bet_amount * 0.1  # 假设10%收益
            self.total_wins += 1
            self.total_profit += profit

            print(f"🎉 投注结果: {result}!")
            print(f"💰 收益: +{profit:.2f}元")

        else:
            # 失败：投注房间是开出房间
            result = "失败"
            loss = bet_amount
            self.total_profit -= loss

            print(f"😞 投注结果: {result}")
            print(f"💸 损失: -{loss:.2f}元")

        # 计算胜率
        win_rate = (self.total_wins / self.total_bets) * 100 if self.total_bets > 0 else 0

        print(f"📈 统计信息:")
        print(f"   总投注: {self.total_bets}次")
        print(f"   获胜: {self.total_wins}次")
        print(f"   胜率: {win_rate:.1f}%")
        print(f"   总盈亏: {self.total_profit:+.2f}元")

        # 分析预测准确性
        prediction_correct = (predicted_room == actual_room)
        print(f"🎯 预测分析:")
        print(f"   预测准确: {'✅ 是' if prediction_correct else '❌ 否'}")
        if prediction_correct:
            print(f"   策略效果: 成功避开预测房间{predicted_room}")

        # 记录开奖结果
        log_result(state.issue, actual_room, result, profit if result == "获胜" else -loss if result == "失败" else 0)

        # 清除当前投注记录
        self.current_bet = None

    def save_history_data(self):
        """保存历史数据"""
        try:
            data = {
                'history': self.history,
                'total_bets': self.total_bets,
                'total_wins': self.total_wins,
                'total_profit': self.total_profit,
                'last_updated': datetime.now().isoformat(),
                'data_count': len(self.history)
            }
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ 保存历史数据失败: {e}")
    
    def start_enhanced_prediction(self):
        """启动增强预测系统"""
        
        if len(self.history) < 20:
            print("⚠️  历史数据不足，建议先使用频率策略收集数据")
            response = input("是否继续启动？(yes/no): ").lower().strip()
            if response not in ['yes', 'y', '是']:
                return
        
        print("🚀 启动增强预测系统...")
        print(f"策略: 动态规则({len(self.dynamic_rules)}个) + 静态规则")

        # 启动实时记录器
        from real_time_logger import get_logger
        logger = get_logger("enhanced_prediction_log")
        print(f"📝 实时记录器已启动，文件将保存为:")
        print(f"   - JSON: {logger.json_file}")
        print(f"   - CSV: {logger.csv_file}")
        print(f"   - Markdown: {logger.md_file}")

        self.is_running = True
        
        try:
            self.monitor.start_monitoring(callback=self.process_new_result)
        except KeyboardInterrupt:
            print("🛑 收到停止信号")
        finally:
            self.stop_enhanced_prediction()
    
    def stop_enhanced_prediction(self):
        """停止增强预测系统"""

        print("\n🛑 停止增强预测系统...")

        # 显示会话摘要
        try:
            summary = get_summary()
            print(f"\n📊 会话摘要:")
            print(f"   运行时长: {summary['session_duration']}")
            print(f"   总投注: {summary['total_bets']}次")
            print(f"   获胜: {summary['total_wins']}次")
            print(f"   胜率: {summary['win_rate']:.1f}%")
            print(f"   总盈亏: {summary['total_profit']:+.2f}元")
            print(f"\n📁 记录文件已保存:")
            print(f"   - JSON: {summary['files']['json']}")
            print(f"   - CSV: {summary['files']['csv']}")
            print(f"   - Markdown: {summary['files']['markdown']}")
        except Exception as e:
            print(f"⚠️ 获取摘要失败: {e}")

        self.is_running = False
        self.monitor.stop_monitoring()
        
        # 保存数据
        self.save_history_data()
        
        print("✅ 增强预测系统已停止")

def main():
    """主函数"""
    
    print("🚀 增强预测系统")
    print("=" * 50)
    print("结合历史数据和动态规则的完整预测系统")
    print()
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    system_config = {
        'base_bet_amount': 1,
        'max_consecutive_losses': 4,
        'max_daily_loss': 500.0
    }
    
    # 创建API客户端
    api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
    
    # 创建增强预测系统
    enhanced_system = EnhancedPredictionSystem(api_client, system_config)
    
    response = input("\n是否启动增强预测系统？(yes/no): ").lower().strip()
    
    if response in ['yes', 'y', '是']:
        enhanced_system.start_enhanced_prediction()
    else:
        print("系统未启动")

if __name__ == "__main__":
    main()
