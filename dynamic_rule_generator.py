#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态规则生成器
基于实时收集的游戏数据生成预测规则
"""

from collections import defaultdict, Counter
from typing import List, Dict, Tuple
import json

class DynamicRuleGenerator:
    """动态规则生成器"""
    
    def __init__(self):
        """初始化"""
        self.collected_data = []
        self.dynamic_rules = []
        
    def add_result(self, room_number: int):
        """添加开奖结果"""
        self.collected_data.append(room_number)
        
        # 当数据足够时，重新生成规则
        if len(self.collected_data) >= 20:  # 至少20个数据点
            self.generate_dynamic_rules()
    
    def generate_dynamic_rules(self):
        """基于当前数据生成动态规则"""
        
        print(f"🔄 基于{len(self.collected_data)}个实时数据生成规则...")
        
        rules = []
        
        # 生成3-5元条件规则
        for condition_length in [3, 4, 5]:
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, len(self.collected_data)):
                condition = tuple(self.collected_data[i-condition_length:i])
                next_val = self.collected_data[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            # 提取规则（降低阈值以适应小样本）
            min_support = 2  # 至少出现2次
            min_confidence = 0.6 if condition_length == 3 else 0.7 if condition_length == 4 else 0.8
            
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    if confidence >= min_confidence:
                        rules.append({
                            'condition': condition,
                            'predicted_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'length': condition_length,
                            'source': 'dynamic'
                        })
        
        # 按置信度排序
        rules.sort(key=lambda x: x['confidence'], reverse=True)
        
        self.dynamic_rules = rules
        
        print(f"✅ 生成了{len(rules)}个动态规则")
        if rules:
            print("最佳动态规则:")
            for i, rule in enumerate(rules[:5], 1):
                print(f"  {i}. {rule['condition']} -> {rule['predicted_value']} "
                      f"(置信度: {rule['confidence']:.3f}, 支持度: {rule['support']})")
        
        return rules
    
    def predict_next_room(self, recent_history: List[int], min_confidence: float = 0.6) -> Dict:
        """基于动态规则预测下一个房间"""
        
        if not self.dynamic_rules:
            return None
        
        # 尝试匹配动态规则
        for rule in self.dynamic_rules:
            if rule['confidence'] < min_confidence:
                continue
                
            condition = rule['condition']
            condition_length = len(condition)
            
            if len(recent_history) >= condition_length:
                recent_sequence = tuple(recent_history[-condition_length:])
                
                if recent_sequence == condition:
                    return {
                        'predicted_room': rule['predicted_value'],
                        'confidence': rule['confidence'],
                        'rule_type': 'dynamic',
                        'condition': condition,
                        'support': rule['support']
                    }
        
        return None
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        
        if len(self.collected_data) < 5:
            return {'message': '数据不足，无法生成统计'}
        
        counter = Counter(self.collected_data)
        
        return {
            'total_samples': len(self.collected_data),
            'frequency_distribution': dict(counter),
            'most_frequent': counter.most_common(1)[0],
            'least_frequent': counter.most_common()[-1],
            'dynamic_rules_count': len(self.dynamic_rules),
            'recent_sequence': self.collected_data[-10:] if len(self.collected_data) >= 10 else self.collected_data
        }

def test_with_current_data():
    """使用当前实际数据测试"""
    
    print("🧪 使用当前实际数据测试动态规则生成")
    print("=" * 50)
    
    # 从控制台观察到的实际开奖数据（按时间顺序）
    actual_results = [8, 5, 4, 5, 5, 6, 3, 2, 7, 1, 4, 6, 2, 8, 3, 7, 1, 5, 8, 4, 2, 6, 3, 7]
    
    generator = DynamicRuleGenerator()
    
    # 逐步添加数据，模拟实时收集
    print("逐步添加实际开奖数据...")
    
    for i, result in enumerate(actual_results, 1):
        generator.add_result(result)
        
        if i >= 20:  # 有足够数据后开始测试预测
            print(f"\n第{i}期后的预测测试:")
            
            # 使用最近5个数据进行预测
            recent = generator.collected_data[-5:]
            prediction = generator.predict_next_room(recent, 0.6)
            
            if prediction:
                print(f"  基于 {recent} 预测下期: {prediction['predicted_room']} "
                      f"(置信度: {prediction['confidence']:.3f})")
                
                # 如果还有下一期数据，验证预测准确性
                if i < len(actual_results):
                    actual_next = actual_results[i]
                    correct = prediction['predicted_room'] == actual_next
                    print(f"  实际开出: {actual_next}, 预测{'✅正确' if correct else '❌错误'}")
            else:
                print(f"  基于 {recent} 无法预测")
    
    # 显示最终统计
    stats = generator.get_statistics()
    print(f"\n📊 最终统计:")
    print(f"  总样本数: {stats['total_samples']}")
    print(f"  动态规则数: {stats['dynamic_rules_count']}")
    print(f"  最近序列: {stats['recent_sequence']}")
    print(f"  频率分布: {stats['frequency_distribution']}")

def create_enhanced_prediction_adapter():
    """创建增强的预测适配器"""
    
    print(f"\n🚀 创建增强预测适配器")
    print("=" * 30)
    
    # 这个函数将创建一个结合静态规则和动态规则的预测器
    enhanced_code = '''
class EnhancedPredictionAdapter:
    """增强的预测适配器，结合静态和动态规则"""
    
    def __init__(self):
        # 加载原有的静态规则
        from prediction_strategy_adapter import PredictionRuleAdapter
        self.static_adapter = PredictionRuleAdapter()
        rules = self.static_adapter.load_prediction_rules_from_analysis()
        self.static_adapter.categorize_rules(rules)
        
        # 创建动态规则生成器
        self.dynamic_generator = DynamicRuleGenerator()
        
    def add_real_time_data(self, room_number: int):
        """添加实时数据"""
        self.dynamic_generator.add_result(room_number)
    
    def predict_next_room(self, recent_history: List[int], min_confidence: float = 0.6):
        """预测下一个房间（优先使用动态规则）"""
        
        # 1. 首先尝试动态规则
        dynamic_prediction = self.dynamic_generator.predict_next_room(recent_history, min_confidence)
        if dynamic_prediction:
            return dynamic_prediction
        
        # 2. 如果动态规则无法预测，尝试静态规则
        static_prediction = self.static_adapter.predict_next_room(recent_history, min_confidence)
        if static_prediction:
            static_prediction['rule_type'] = 'static'
            return static_prediction
        
        return None
'''
    
    print("增强预测适配器代码已生成")
    print("这个适配器将:")
    print("1. 优先使用基于实时数据的动态规则")
    print("2. 动态规则无效时回退到静态规则")
    print("3. 持续学习和更新规则")
    
    return enhanced_code

def main():
    """主函数"""
    
    print("🔧 动态规则生成器")
    print("=" * 50)
    print("解决预测规则与实际数据不匹配的问题")
    print()
    
    # 1. 使用实际数据测试
    test_with_current_data()
    
    # 2. 创建增强适配器
    enhanced_code = create_enhanced_prediction_adapter()
    
    print(f"\n💡 解决方案总结:")
    print("问题根源: 静态预测规则与实际游戏数据不匹配")
    print("解决方案: 基于实时数据动态生成预测规则")
    print()
    print("下一步:")
    print("1. 将动态规则生成器集成到主系统")
    print("2. 修改预测适配器使用动态规则")
    print("3. 降低初始置信度阈值到0.6")
    print("4. 重新启动投注系统")

if __name__ == "__main__":
    main()
