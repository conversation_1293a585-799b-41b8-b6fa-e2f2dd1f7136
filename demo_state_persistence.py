#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示状态持久化功能
"""

import os
import json
from datetime import datetime

def demo_state_persistence():
    """演示状态持久化的工作原理"""
    
    print("🎯 状态持久化功能演示")
    print("=" * 50)
    
    print("💡 问题分析:")
    print("   您遇到的问题是：连败21期、22期后，系统显示连续失败0次")
    print("   这是因为每次重新启动投注时，系统状态被重置了")
    print()
    
    print("🔧 解决方案:")
    print("   1. 添加状态持久化文件 (system_state_YYYYMMDD.json)")
    print("   2. 系统启动时自动加载之前的状态")
    print("   3. 每次投注结果处理后自动保存状态")
    print()
    
    # 模拟状态文件
    state_file = f"system_state_{datetime.now().strftime('%Y%m%d')}.json"
    
    print("📊 模拟场景:")
    print("   假设您已经连败了2次...")
    
    # 模拟连败状态
    state_after_losses = {
        'consecutive_losses': 2,
        'consecutive_wins': 0,
        'daily_loss': 4.0,
        'total_profit': -4.0,
        'current_balance': 146.0,
        'total_bets': 2,
        'total_wins': 0,
        'last_update': datetime.now().isoformat()
    }
    
    # 保存状态
    with open(state_file, 'w', encoding='utf-8') as f:
        json.dump(state_after_losses, f, indent=2, ensure_ascii=False)
    
    print(f"💾 状态已保存到: {state_file}")
    print(f"   连败: {state_after_losses['consecutive_losses']}次")
    print(f"   连胜: {state_after_losses['consecutive_wins']}次")
    print(f"   余额: {state_after_losses['current_balance']:.2f}元")
    print()
    
    print("🔄 模拟系统重启...")
    print("   (这就是您每次重新运行投注时发生的情况)")
    print()
    
    # 模拟系统重启后加载状态
    print("📂 系统启动时加载状态:")
    with open(state_file, 'r', encoding='utf-8') as f:
        loaded_state = json.load(f)
    
    print(f"✅ 成功加载状态:")
    print(f"   连败: {loaded_state['consecutive_losses']}次")
    print(f"   连胜: {loaded_state['consecutive_wins']}次")
    print(f"   余额: {loaded_state['current_balance']:.2f}元")
    print()
    
    print("💰 基于持久化状态计算动态金额:")
    
    # 模拟动态金额计算
    base_amount = 1.5
    algorithm_bonus = 1.007
    
    # 马丁格尔调整 (因为连败2次)
    martingale_multiplier = 1.15 ** loaded_state['consecutive_losses']
    
    # 风险调整 (连败2次 = medium风险)
    risk_multiplier = 0.9  # medium风险
    
    calculated_amount = base_amount * algorithm_bonus * martingale_multiplier * risk_multiplier
    final_amount = max(1.0, calculated_amount)  # 最小1元
    
    print(f"   基础金额: {base_amount}元")
    print(f"   算法奖励: ×{algorithm_bonus:.3f}")
    print(f"   马丁格尔调整: ×{martingale_multiplier:.3f} (连败{loaded_state['consecutive_losses']}次)")
    print(f"   风险调整: ×{risk_multiplier} (medium风险)")
    print(f"   计算结果: {calculated_amount:.2f}元")
    print(f"   最终金额: {final_amount:.2f}元")
    print()
    
    print("🎉 现在系统会正确显示:")
    print(f"   💰 增强动态金额计算:")
    print(f"      基础金额: {base_amount}元")
    print(f"      连续失败: {loaded_state['consecutive_losses']}次")
    print(f"      连续获胜: {loaded_state['consecutive_wins']}次")
    print(f"      当前余额: {loaded_state['current_balance']:.2f}元")
    print(f"      算法奖励: ×{algorithm_bonus:.3f} (基于88.21%避开率)")
    print(f"      马丁格尔调整: ×{martingale_multiplier:.3f}")
    print(f"      风险调整: ×{risk_multiplier} (风险等级: medium)")
    print(f"      最终金额: {final_amount:.2f}元")
    print()
    
    print("📈 投注金额变化对比:")
    print("   修复前: 总是1.66元 (因为状态总是重置为0败0胜)")
    print(f"   修复后: {final_amount:.2f}元 (基于实际连败{loaded_state['consecutive_losses']}次)")
    print()
    
    # 模拟一次获胜后的状态更新
    print("🎉 模拟一次获胜后的状态更新:")
    
    state_after_win = {
        'consecutive_losses': 0,  # 重置连败
        'consecutive_wins': 1,    # 连胜+1
        'daily_loss': 2.0,        # 减少损失
        'total_profit': -2.0,     # 改善盈亏
        'current_balance': 148.0, # 增加余额
        'total_bets': 3,
        'total_wins': 1,
        'last_update': datetime.now().isoformat()
    }
    
    # 保存更新后的状态
    with open(state_file, 'w', encoding='utf-8') as f:
        json.dump(state_after_win, f, indent=2, ensure_ascii=False)
    
    print(f"💾 更新后状态:")
    print(f"   连败: {state_after_win['consecutive_losses']}次")
    print(f"   连胜: {state_after_win['consecutive_wins']}次")
    print(f"   余额: {state_after_win['current_balance']:.2f}元")
    
    # 重新计算动态金额
    new_calculated_amount = base_amount * algorithm_bonus * 1.1  # 低风险调整
    new_final_amount = max(1.0, new_calculated_amount)
    
    print(f"   新的投注金额: {new_final_amount:.2f}元 (无马丁格尔调整)")
    print()
    
    print("✅ 修复效果:")
    print("   1. 系统会记住连胜连败状态")
    print("   2. 马丁格尔策略正确生效")
    print("   3. 连胜奖励正确计算")
    print("   4. 风险调整基于真实状态")
    print("   5. 投注金额真正动态变化")
    print()
    
    # 清理演示文件
    try:
        os.remove(state_file)
        print(f"🗑️ 清理演示文件: {state_file}")
    except:
        pass

if __name__ == "__main__":
    demo_state_persistence()
    
    print("🎯 使用说明:")
    print("   现在当您运行真实投注时:")
    print("   1. 系统会自动创建状态文件 (system_state_YYYYMMDD.json)")
    print("   2. 每次投注结果处理后自动保存状态")
    print("   3. 重新启动时自动加载之前的状态")
    print("   4. 增强动态金额会基于真实的连胜连败状态计算")
    print()
    print("💡 您将看到:")
    print("   - 连败时投注金额逐步增加 (马丁格尔策略)")
    print("   - 连胜时投注金额适度增加 (连胜奖励)")
    print("   - 风险等级根据实际状态调整")
    print("   - 控制台显示正确的连胜连败次数")
