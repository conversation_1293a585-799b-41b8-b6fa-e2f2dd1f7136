#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险控制和监控系统
实现投注金额控制、连败保护、实时收益监控等风险管理功能
"""

import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt
import pandas as pd

@dataclass
class RiskMetrics:
    """风险指标"""
    current_balance: float
    daily_profit: float
    daily_loss: float
    win_rate: float
    consecutive_losses: int
    max_drawdown: float
    sharpe_ratio: float
    risk_level: str  # low, medium, high, critical

@dataclass
class AlertRule:
    """预警规则"""
    name: str
    condition: str
    threshold: float
    action: str  # stop, reduce, alert
    enabled: bool

class RiskControlMonitor:
    """风险控制监控器"""
    
    def __init__(self, config: Dict):
        """初始化风险控制监控器"""
        self.config = config
        self.is_monitoring = False
        self.monitoring_thread = None
        
        # 风险控制参数
        self.max_daily_loss = config.get('max_daily_loss', 10.0)
        self.max_consecutive_losses = config.get('max_consecutive_losses', 3)
        self.max_single_bet = config.get('max_single_bet', 1.0)
        self.stop_loss_percentage = config.get('stop_loss_percentage', 0.2)  # 20%
        self.take_profit_percentage = config.get('take_profit_percentage', 0.5)  # 50%
        
        # 当前状态
        self.initial_balance = config.get('initial_balance', 100.0)
        self.current_balance = self.initial_balance
        self.daily_start_balance = self.initial_balance
        self.peak_balance = self.initial_balance
        
        # 统计数据
        self.betting_history = []
        self.daily_stats = []
        self.risk_alerts = []
        
        # 预警规则
        self.alert_rules = self.create_default_alert_rules()
        
        # 风险状态
        self.risk_level = "low"
        self.emergency_stop = False
        
    def create_default_alert_rules(self) -> List[AlertRule]:
        """创建默认预警规则"""
        
        return [
            AlertRule("连续失败预警", "consecutive_losses", 2, "alert", True),
            AlertRule("连续失败停止", "consecutive_losses", 3, "stop", True),
            AlertRule("日损失预警", "daily_loss_rate", 0.1, "alert", True),
            AlertRule("日损失停止", "daily_loss_rate", 0.2, "stop", True),
            AlertRule("最大回撤预警", "max_drawdown", 0.15, "alert", True),
            AlertRule("最大回撤停止", "max_drawdown", 0.25, "stop", True),
            AlertRule("胜率过低预警", "win_rate", 0.6, "alert", True),
            AlertRule("余额不足停止", "balance_ratio", 0.1, "stop", True),
        ]
    
    def update_balance(self, amount: float, bet_result: bool):
        """更新余额"""
        
        if bet_result:
            # 获胜
            self.current_balance += amount * 0.1  # 1:1.1赔率的净收益
        else:
            # 失败
            self.current_balance -= amount
        
        # 更新峰值余额
        if self.current_balance > self.peak_balance:
            self.peak_balance = self.current_balance
        
        # 记录投注历史
        self.betting_history.append({
            'timestamp': datetime.now().isoformat(),
            'amount': amount,
            'result': bet_result,
            'balance': self.current_balance,
            'profit': amount * 0.1 if bet_result else -amount
        })
    
    def calculate_risk_metrics(self) -> RiskMetrics:
        """计算风险指标"""
        
        if not self.betting_history:
            return RiskMetrics(
                current_balance=self.current_balance,
                daily_profit=0.0,
                daily_loss=0.0,
                win_rate=0.0,
                consecutive_losses=0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                risk_level="low"
            )
        
        # 计算日收益和日损失
        today = datetime.now().date()
        today_records = [r for r in self.betting_history 
                        if datetime.fromisoformat(r['timestamp']).date() == today]
        
        daily_profit = sum(r['profit'] for r in today_records if r['profit'] > 0)
        daily_loss = abs(sum(r['profit'] for r in today_records if r['profit'] < 0))
        
        # 计算胜率
        total_bets = len(self.betting_history)
        wins = sum(1 for r in self.betting_history if r['result'])
        win_rate = wins / total_bets if total_bets > 0 else 0
        
        # 计算连续失败次数
        consecutive_losses = 0
        for record in reversed(self.betting_history):
            if not record['result']:
                consecutive_losses += 1
            else:
                break
        
        # 计算最大回撤
        max_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
        
        # 计算夏普比率（简化版本）
        if len(self.betting_history) > 1:
            returns = [r['profit'] / self.initial_balance for r in self.betting_history]
            avg_return = sum(returns) / len(returns)
            return_std = (sum((r - avg_return) ** 2 for r in returns) / len(returns)) ** 0.5
            sharpe_ratio = avg_return / return_std if return_std > 0 else 0
        else:
            sharpe_ratio = 0
        
        # 确定风险等级
        risk_level = self.determine_risk_level(consecutive_losses, max_drawdown, win_rate)
        
        return RiskMetrics(
            current_balance=self.current_balance,
            daily_profit=daily_profit,
            daily_loss=daily_loss,
            win_rate=win_rate,
            consecutive_losses=consecutive_losses,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            risk_level=risk_level
        )
    
    def determine_risk_level(self, consecutive_losses: int, max_drawdown: float, win_rate: float) -> str:
        """确定风险等级"""
        
        if consecutive_losses >= 3 or max_drawdown >= 0.25 or win_rate < 0.5:
            return "critical"
        elif consecutive_losses >= 2 or max_drawdown >= 0.15 or win_rate < 0.6:
            return "high"
        elif consecutive_losses >= 1 or max_drawdown >= 0.1 or win_rate < 0.7:
            return "medium"
        else:
            return "low"
    
    def check_alert_rules(self, metrics: RiskMetrics) -> List[Dict]:
        """检查预警规则"""
        
        triggered_alerts = []
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            should_trigger = False
            current_value = 0
            
            if rule.condition == "consecutive_losses":
                current_value = metrics.consecutive_losses
                should_trigger = current_value >= rule.threshold
            elif rule.condition == "daily_loss_rate":
                current_value = metrics.daily_loss / self.daily_start_balance
                should_trigger = current_value >= rule.threshold
            elif rule.condition == "max_drawdown":
                current_value = metrics.max_drawdown
                should_trigger = current_value >= rule.threshold
            elif rule.condition == "win_rate":
                current_value = metrics.win_rate
                should_trigger = current_value <= rule.threshold
            elif rule.condition == "balance_ratio":
                current_value = self.current_balance / self.initial_balance
                should_trigger = current_value <= rule.threshold
            
            if should_trigger:
                alert = {
                    'rule_name': rule.name,
                    'condition': rule.condition,
                    'threshold': rule.threshold,
                    'current_value': current_value,
                    'action': rule.action,
                    'timestamp': datetime.now().isoformat()
                }
                triggered_alerts.append(alert)
                
                # 记录预警
                self.risk_alerts.append(alert)
                
                # 执行预警动作
                if rule.action == "stop":
                    self.emergency_stop = True
                    print(f"🚨 紧急停止: {rule.name}")
                elif rule.action == "alert":
                    print(f"⚠️  风险预警: {rule.name}")
        
        return triggered_alerts
    
    def calculate_recommended_bet_size(self, confidence: float, base_amount: float) -> float:
        """计算推荐投注金额"""
        
        metrics = self.calculate_risk_metrics()
        
        # 基础金额调整
        risk_multiplier = {
            "low": 1.0,
            "medium": 0.8,
            "high": 0.5,
            "critical": 0.2
        }.get(metrics.risk_level, 0.2)
        
        # 基于置信度调整
        confidence_multiplier = min(confidence / 0.8, 1.5)  # 最大1.5倍
        
        # 基于余额比例调整
        balance_ratio = self.current_balance / self.initial_balance
        balance_multiplier = min(balance_ratio, 1.0)
        
        # 计算推荐金额
        recommended_amount = base_amount * risk_multiplier * confidence_multiplier * balance_multiplier
        
        # 确保不超过限制
        max_allowed = min(self.max_single_bet, self.current_balance * 0.1)  # 不超过余额的10%
        
        return min(recommended_amount, max_allowed)
    
    def should_stop_betting(self) -> bool:
        """判断是否应该停止投注"""
        
        if self.emergency_stop:
            return True
        
        metrics = self.calculate_risk_metrics()
        
        # 检查各种停止条件
        stop_conditions = [
            metrics.consecutive_losses >= self.max_consecutive_losses,
            metrics.daily_loss >= self.max_daily_loss,
            metrics.max_drawdown >= self.stop_loss_percentage,
            self.current_balance <= self.initial_balance * 0.1,  # 余额不足10%
            metrics.risk_level == "critical"
        ]
        
        return any(stop_conditions)
    
    def generate_risk_report(self) -> Dict:
        """生成风险报告"""
        
        metrics = self.calculate_risk_metrics()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'metrics': asdict(metrics),
            'balance_info': {
                'initial_balance': self.initial_balance,
                'current_balance': self.current_balance,
                'peak_balance': self.peak_balance,
                'total_return': (self.current_balance - self.initial_balance) / self.initial_balance
            },
            'recent_alerts': self.risk_alerts[-10:],  # 最近10个预警
            'recommendations': self.generate_recommendations(metrics)
        }
        
        return report
    
    def generate_recommendations(self, metrics: RiskMetrics) -> List[str]:
        """生成风险管理建议"""
        
        recommendations = []
        
        if metrics.consecutive_losses >= 2:
            recommendations.append("连续失败次数较多，建议降低投注金额或暂停投注")
        
        if metrics.max_drawdown >= 0.15:
            recommendations.append("最大回撤较大，建议检查策略有效性")
        
        if metrics.win_rate < 0.6:
            recommendations.append("胜率偏低，建议重新评估预测模型")
        
        if metrics.daily_loss > self.max_daily_loss * 0.8:
            recommendations.append("接近日损失限制，建议谨慎投注")
        
        if metrics.risk_level in ["high", "critical"]:
            recommendations.append("风险等级较高，建议暂停投注并分析原因")
        
        if not recommendations:
            recommendations.append("当前风险控制良好，可继续按计划投注")
        
        return recommendations
    
    def save_risk_report(self, filename: Optional[str] = None):
        """保存风险报告"""
        
        if filename is None:
            filename = f"risk_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = self.generate_risk_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"风险报告已保存: {filename}")
    
    def start_monitoring(self):
        """开始风险监控"""
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.start()
        print("风险监控已启动")
    
    def stop_monitoring(self):
        """停止风险监控"""
        
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join()
        print("风险监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        
        while self.is_monitoring:
            try:
                metrics = self.calculate_risk_metrics()
                alerts = self.check_alert_rules(metrics)
                
                # 每分钟输出一次状态
                print(f"风险监控 - 余额: {self.current_balance:.2f}, "
                      f"风险等级: {metrics.risk_level}, "
                      f"胜率: {metrics.win_rate:.3f}")
                
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                print(f"风险监控异常: {e}")
                time.sleep(10)

def main():
    """主函数"""
    
    print("=== 风险控制和监控系统 ===")
    
    # 示例配置
    config = {
        'max_daily_loss': 10.0,
        'max_consecutive_losses': 3,
        'max_single_bet': 1.0,
        'stop_loss_percentage': 0.2,
        'take_profit_percentage': 0.5,
        'initial_balance': 100.0
    }
    
    # 创建风险控制监控器
    risk_monitor = RiskControlMonitor(config)
    
    print("风险控制参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print(f"\n预警规则:")
    for rule in risk_monitor.alert_rules:
        print(f"  {rule.name}: {rule.condition} {rule.threshold} -> {rule.action}")
    
    print(f"\n要使用风险控制系统，请:")
    print("1. 在投注系统中集成RiskControlMonitor")
    print("2. 每次投注后调用update_balance()更新余额")
    print("3. 投注前调用should_stop_betting()检查是否应停止")
    print("4. 定期调用generate_risk_report()生成报告")

if __name__ == "__main__":
    main()
