#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
序列连续性验证
检查69个数字序列和2000个数字序列是否来自同一个随机数生成器
"""

import numpy as np
from typing import List, Tuple, Dict
from collections import Counter
import matplotlib.pyplot as plt

class SequenceContinuityChecker:
    def __init__(self, seq1_file: str, seq2_file: str):
        """初始化连续性检查器"""
        self.seq1 = self.load_sequence(seq1_file)
        self.seq2 = self.load_sequence(seq2_file)
        self.seq1_name = seq1_file
        self.seq2_name = seq2_file
        
        print(f"序列1 ({seq1_file}): {len(self.seq1)} 个数字")
        print(f"序列2 ({seq2_file}): {len(self.seq2)} 个数字")
        
    def load_sequence(self, filename: str) -> List[int]:
        """加载序列"""
        with open(filename, 'r', encoding='utf-8') as f:
            return [int(line.strip()) for line in f if line.strip()]
    
    def statistical_comparison(self) -> Dict:
        """统计特征比较"""
        print("=== 统计特征比较 ===")
        
        # 基础统计
        stats1 = {
            'mean': np.mean(self.seq1),
            'std': np.std(self.seq1),
            'min': np.min(self.seq1),
            'max': np.max(self.seq1),
            'median': np.median(self.seq1)
        }
        
        stats2 = {
            'mean': np.mean(self.seq2),
            'std': np.std(self.seq2),
            'min': np.min(self.seq2),
            'max': np.max(self.seq2),
            'median': np.median(self.seq2)
        }
        
        print("基础统计比较:")
        print(f"{'指标':<10} {'序列1':<12} {'序列2':<12} {'差异':<10}")
        print("-" * 50)
        
        for key in stats1.keys():
            diff = abs(stats1[key] - stats2[key])
            print(f"{key:<10} {stats1[key]:<12.4f} {stats2[key]:<12.4f} {diff:<10.4f}")
        
        # 频率分布比较
        freq1 = Counter(self.seq1)
        freq2 = Counter(self.seq2)
        
        print(f"\n频率分布比较:")
        print(f"{'数字':<6} {'序列1频率':<12} {'序列2频率':<12} {'差异':<10}")
        print("-" * 50)
        
        freq_diffs = []
        for i in range(1, 9):
            f1 = freq1.get(i, 0) / len(self.seq1)
            f2 = freq2.get(i, 0) / len(self.seq2)
            diff = abs(f1 - f2)
            freq_diffs.append(diff)
            print(f"{i:<6} {f1:<12.4f} {f2:<12.4f} {diff:<10.4f}")
        
        avg_freq_diff = np.mean(freq_diffs)
        print(f"\n平均频率差异: {avg_freq_diff:.4f}")
        
        return {
            'stats1': stats1,
            'stats2': stats2,
            'freq_diffs': freq_diffs,
            'avg_freq_diff': avg_freq_diff
        }
    
    def pattern_similarity_analysis(self) -> Dict:
        """模式相似性分析"""
        print(f"\n=== 模式相似性分析 ===")
        
        results = {}
        
        for length in range(2, 5):
            # 提取模式
            patterns1 = Counter()
            patterns2 = Counter()
            
            for i in range(len(self.seq1) - length + 1):
                pattern = tuple(self.seq1[i:i + length])
                patterns1[pattern] += 1
            
            for i in range(len(self.seq2) - length + 1):
                pattern = tuple(self.seq2[i:i + length])
                patterns2[pattern] += 1
            
            # 计算模式重叠
            common_patterns = set(patterns1.keys()) & set(patterns2.keys())
            total_patterns = set(patterns1.keys()) | set(patterns2.keys())
            
            overlap_ratio = len(common_patterns) / len(total_patterns) if total_patterns else 0
            
            print(f"{length}位模式:")
            print(f"  序列1模式数: {len(patterns1)}")
            print(f"  序列2模式数: {len(patterns2)}")
            print(f"  共同模式数: {len(common_patterns)}")
            print(f"  重叠比例: {overlap_ratio:.4f}")
            
            # 找出最常见的共同模式
            common_pattern_counts = []
            for pattern in common_patterns:
                count1 = patterns1[pattern]
                count2 = patterns2[pattern]
                common_pattern_counts.append((pattern, count1, count2))
            
            common_pattern_counts.sort(key=lambda x: x[1] + x[2], reverse=True)
            
            if common_pattern_counts:
                print(f"  最常见的共同模式:")
                for pattern, c1, c2 in common_pattern_counts[:5]:
                    print(f"    {pattern}: 序列1={c1}次, 序列2={c2}次")
            
            results[f'length_{length}'] = {
                'patterns1_count': len(patterns1),
                'patterns2_count': len(patterns2),
                'common_count': len(common_patterns),
                'overlap_ratio': overlap_ratio,
                'common_patterns': common_pattern_counts[:10]
            }
        
        return results
    
    def transition_matrix_comparison(self) -> Dict:
        """状态转换矩阵比较"""
        print(f"\n=== 状态转换矩阵比较 ===")
        
        # 构建转换矩阵
        def build_transition_matrix(sequence):
            transitions = np.zeros((8, 8))
            for i in range(len(sequence) - 1):
                current = sequence[i] - 1  # 转换为0-7索引
                next_val = sequence[i + 1] - 1
                transitions[current][next_val] += 1
            
            # 归一化为概率
            for i in range(8):
                row_sum = np.sum(transitions[i])
                if row_sum > 0:
                    transitions[i] /= row_sum
            
            return transitions
        
        trans1 = build_transition_matrix(self.seq1)
        trans2 = build_transition_matrix(self.seq2)
        
        # 计算转换矩阵差异
        matrix_diff = np.abs(trans1 - trans2)
        avg_diff = np.mean(matrix_diff)
        max_diff = np.max(matrix_diff)
        
        print(f"转换矩阵平均差异: {avg_diff:.4f}")
        print(f"转换矩阵最大差异: {max_diff:.4f}")
        
        # 显示差异最大的转换
        max_diff_pos = np.unravel_index(np.argmax(matrix_diff), matrix_diff.shape)
        from_state, to_state = max_diff_pos
        print(f"最大差异转换: {from_state+1} -> {to_state+1}")
        print(f"  序列1概率: {trans1[from_state, to_state]:.4f}")
        print(f"  序列2概率: {trans2[from_state, to_state]:.4f}")
        
        return {
            'trans1': trans1,
            'trans2': trans2,
            'avg_diff': avg_diff,
            'max_diff': max_diff,
            'max_diff_transition': (from_state+1, to_state+1)
        }
    
    def continuity_test(self) -> Dict:
        """连续性测试 - 检查序列1是否可能是序列2的子序列"""
        print(f"\n=== 连续性测试 ===")
        
        # 在序列2中寻找序列1的最佳匹配位置
        best_match_pos = 0
        best_match_count = 0
        
        # 滑动窗口搜索
        for start_pos in range(len(self.seq2) - len(self.seq1) + 1):
            matches = 0
            for i in range(len(self.seq1)):
                if self.seq2[start_pos + i] == self.seq1[i]:
                    matches += 1
            
            if matches > best_match_count:
                best_match_count = matches
                best_match_pos = start_pos
        
        match_rate = best_match_count / len(self.seq1)
        
        print(f"最佳匹配位置: {best_match_pos}")
        print(f"匹配数量: {best_match_count}/{len(self.seq1)}")
        print(f"匹配率: {match_rate:.4f}")
        
        # 显示匹配详情（前20个）
        if best_match_count > 0:
            print(f"\n匹配详情 (前20个):")
            print("位置  序列1  序列2  匹配")
            print("-" * 25)
            for i in range(min(20, len(self.seq1))):
                s1_val = self.seq1[i]
                s2_val = self.seq2[best_match_pos + i]
                match = "✓" if s1_val == s2_val else "✗"
                print(f"{i+1:4d}  {s1_val:4d}  {s2_val:4d}   {match}")
        
        return {
            'best_match_pos': best_match_pos,
            'best_match_count': best_match_count,
            'match_rate': match_rate
        }
    
    def run_continuity_check(self) -> Dict:
        """运行完整的连续性检查"""
        print("开始序列连续性验证...")
        print(f"序列1前10个: {self.seq1[:10]}")
        print(f"序列2前10个: {self.seq2[:10]}")
        print()
        
        # 各项分析
        stats_comparison = self.statistical_comparison()
        pattern_analysis = self.pattern_similarity_analysis()
        transition_analysis = self.transition_matrix_comparison()
        continuity_result = self.continuity_test()
        
        return {
            'statistical': stats_comparison,
            'patterns': pattern_analysis,
            'transitions': transition_analysis,
            'continuity': continuity_result
        }
    
    def generate_conclusion(self, results: Dict) -> str:
        """生成结论"""
        conclusion = "\n=== 连续性分析结论 ===\n"
        
        # 统计相似性
        avg_freq_diff = results['statistical']['avg_freq_diff']
        if avg_freq_diff < 0.02:
            conclusion += "✓ 频率分布高度相似\n"
        elif avg_freq_diff < 0.05:
            conclusion += "~ 频率分布较为相似\n"
        else:
            conclusion += "✗ 频率分布差异较大\n"
        
        # 模式相似性
        pattern_overlap = results['patterns']['length_3']['overlap_ratio']
        if pattern_overlap > 0.7:
            conclusion += "✓ 模式高度重叠\n"
        elif pattern_overlap > 0.5:
            conclusion += "~ 模式中等重叠\n"
        else:
            conclusion += "✗ 模式重叠度低\n"
        
        # 转换矩阵相似性
        trans_diff = results['transitions']['avg_diff']
        if trans_diff < 0.05:
            conclusion += "✓ 状态转换模式高度相似\n"
        elif trans_diff < 0.1:
            conclusion += "~ 状态转换模式较为相似\n"
        else:
            conclusion += "✗ 状态转换模式差异较大\n"
        
        # 连续性匹配
        match_rate = results['continuity']['match_rate']
        if match_rate > 0.8:
            conclusion += "✓ 发现高度连续性匹配\n"
        elif match_rate > 0.5:
            conclusion += "~ 发现中等连续性匹配\n"
        else:
            conclusion += "✗ 未发现明显连续性\n"
        
        # 总结
        conclusion += f"\n总体评估: "
        if avg_freq_diff < 0.03 and pattern_overlap > 0.6 and trans_diff < 0.08:
            conclusion += "两个序列很可能来自同一个随机数生成器"
        elif match_rate > 0.7:
            conclusion += "序列1很可能是序列2的连续子序列"
        else:
            conclusion += "两个序列可能来自不同的随机数生成器或不同的生成时期"
        
        return conclusion

def main():
    """主函数"""
    checker = SequenceContinuityChecker("随机生成1-8.txt", "随机2000个数字.txt")
    results = checker.run_continuity_check()
    
    conclusion = checker.generate_conclusion(results)
    print(conclusion)

if __name__ == "__main__":
    main()
