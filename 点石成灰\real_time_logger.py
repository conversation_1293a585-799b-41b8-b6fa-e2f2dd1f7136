#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据记录器
将系统运行数据实时写入文档
"""

import json
import csv
import time
from datetime import datetime
from typing import Dict, List, Any
import os

class RealTimeLogger:
    """实时数据记录器"""
    
    def __init__(self, base_filename: str = "betting_log"):
        """初始化记录器"""
        self.base_filename = base_filename
        self.session_start = datetime.now()
        
        # 生成带时间戳的文件名
        timestamp = self.session_start.strftime('%Y%m%d_%H%M%S')
        self.json_file = f"{base_filename}_{timestamp}.json"
        self.csv_file = f"{base_filename}_{timestamp}.csv"
        self.md_file = f"{base_filename}_{timestamp}.md"
        
        # 初始化数据结构
        self.session_data = {
            'session_info': {
                'start_time': self.session_start.isoformat(),
                'session_id': timestamp,
                'total_bets': 0,
                'total_wins': 0,
                'total_profit': 0.0
            },
            'betting_records': []
        }
        
        # 初始化文件
        self.init_files()
        
        print(f"📝 实时记录器已启动")
        print(f"   JSON文件: {self.json_file}")
        print(f"   CSV文件: {self.csv_file}")
        print(f"   Markdown文件: {self.md_file}")
    
    def init_files(self):
        """初始化记录文件"""
        
        # 初始化JSON文件
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(self.session_data, f, indent=2, ensure_ascii=False)
        
        # 初始化CSV文件
        csv_headers = [
            '时间', '期号', '预测房间', '预测置信度', '规则类型',
            '可选房间', '投注房间', '选择策略', '投注金额',
            '开奖房间', '投注结果', '盈亏', '累计盈亏', '胜率'
        ]
        
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(csv_headers)
        
        # 初始化Markdown文件
        with open(self.md_file, 'w', encoding='utf-8') as f:
            f.write(f"# 🎯 投注系统实时记录\n\n")
            f.write(f"**会话开始时间**: {self.session_start.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"## 📊 实时统计\n\n")
            f.write(f"- **总投注次数**: 0\n")
            f.write(f"- **获胜次数**: 0\n")
            f.write(f"- **胜率**: 0.0%\n")
            f.write(f"- **总盈亏**: +0.00元\n\n")
            f.write(f"## 📋 投注记录\n\n")
            f.write(f"| 时间 | 期号 | 预测房间 | 置信度 | 投注房间 | 选择策略 | 开奖房间 | 结果 | 盈亏 |\n")
            f.write(f"|------|------|----------|--------|----------|----------|----------|------|------|\n")
    
    def log_prediction(self, issue: int, predicted_room: int, confidence: float, rule_type: str):
        """记录预测信息"""
        
        current_time = datetime.now()
        
        # 创建预测记录
        prediction_record = {
            'timestamp': current_time.isoformat(),
            'time_str': current_time.strftime('%H:%M:%S'),
            'issue': issue,
            'predicted_room': predicted_room,
            'confidence': confidence,
            'rule_type': rule_type,
            'status': 'predicted'
        }
        
        # 添加到会话数据
        self.session_data['betting_records'].append(prediction_record)
        
        print(f"📝 记录预测: 期号{issue}, 预测房间{predicted_room}, 置信度{confidence:.3f}")
    
    def log_room_selection(self, issue: int, available_rooms: List[int], selected_room: int, 
                          strategy: str, selection_details: Dict):
        """记录智能房间选择过程"""
        
        # 找到对应的预测记录
        for record in reversed(self.session_data['betting_records']):
            if record['issue'] == issue and record['status'] == 'predicted':
                # 更新记录
                record.update({
                    'available_rooms': available_rooms,
                    'selected_room': selected_room,
                    'selection_strategy': strategy,
                    'selection_details': selection_details,
                    'status': 'room_selected'
                })
                break
        
        print(f"📝 记录房间选择: 期号{issue}, 选择房间{selected_room}, 策略{strategy}")

        # 立即保存文件
        self.write_all_files()
    
    def log_betting(self, issue: int, bet_room: int, bet_amount: float, bet_success: bool,
                   amount_calculation: Dict = None):
        """记录投注信息

        Args:
            issue: 期号
            bet_room: 投注房间
            bet_amount: 投注金额
            bet_success: 投注是否成功
            amount_calculation: 投注金额计算详情
        """

        # 找到对应的记录
        record_found = False
        for record in reversed(self.session_data['betting_records']):
            if record['issue'] == issue and 'selected_room' in record:
                # 更新记录
                record.update({
                    'bet_room': bet_room,
                    'bet_amount': bet_amount,
                    'bet_success': bet_success,
                    'status': 'bet_placed' if bet_success else 'bet_failed',
                    'amount_calculation': amount_calculation or {}
                })
                record_found = True
                break

        # 如果没有找到对应记录，创建新记录
        if not record_found:
            new_record = {
                'timestamp': datetime.now().isoformat(),
                'issue': issue,
                'selected_room': bet_room,  # 使用投注房间作为选择房间
                'strategy': '直接投注',
                'bet_room': bet_room,
                'bet_amount': bet_amount,
                'bet_success': bet_success,
                'status': 'bet_placed' if bet_success else 'bet_failed',
                'amount_calculation': amount_calculation or {}
            }
            self.session_data['betting_records'].append(new_record)

        # 注意：不在这里更新total_bets，而是在log_result时更新
        # 这样确保只有完成的投注才被计入统计

        print(f"📝 记录投注: 期号{issue}, 投注房间{bet_room}, 金额{bet_amount}, 成功{bet_success}")

        # 立即保存文件
        self.write_all_files()
    
    def log_result(self, issue: int, actual_room: int, result: str, profit: float, room_stats: Dict = None):
        """记录开奖结果"""

        # 找到对应的记录
        for record in reversed(self.session_data['betting_records']):
            if record['issue'] == issue and record.get('status') == 'bet_placed':
                # 更新记录
                record.update({
                    'actual_room': actual_room,
                    'result': result,
                    'profit': profit,
                    'room_stats': room_stats or {},
                    'status': 'completed'
                })

                # 更新会话统计 (只有完成的投注才计入统计)
                self.session_data['session_info']['total_bets'] += 1
                if result == '获胜':
                    self.session_data['session_info']['total_wins'] += 1

                self.session_data['session_info']['total_profit'] += profit

                # 立即写入所有文件
                self.write_all_files()

                break

        print(f"📝 记录结果: 期号{issue}, 开奖房间{actual_room}, 结果{result}, 盈亏{profit:+.2f}")
        if room_stats:
            print(f"📊 房间统计已记录: {len(room_stats)}个房间")
    
    def write_all_files(self):
        """写入所有记录文件"""
        
        # 更新JSON文件
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(self.session_data, f, indent=2, ensure_ascii=False)
        
        # 更新CSV文件
        self.write_csv_file()
        
        # 更新Markdown文件
        self.write_markdown_file()
        
        print(f"📝 已更新所有记录文件")
    
    def write_csv_file(self):
        """写入CSV文件"""
        
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            headers = [
                '时间', '期号', '预测房间', '预测置信度', '规则类型',
                '可选房间', '投注房间', '选择策略', '投注金额',
                '开奖房间', '投注结果', '盈亏', '累计盈亏', '胜率'
            ]
            writer.writerow(headers)
            
            # 写入数据
            cumulative_profit = 0
            completed_bets = 0
            wins = 0
            
            for record in self.session_data['betting_records']:
                if record.get('status') == 'completed':
                    completed_bets += 1
                    if record.get('result') == '获胜':
                        wins += 1
                    
                    cumulative_profit += record.get('profit', 0)
                    win_rate = (wins / completed_bets) * 100 if completed_bets > 0 else 0
                    
                    row = [
                        record.get('time_str', ''),
                        record.get('issue', ''),
                        record.get('predicted_room', ''),
                        f"{record.get('confidence', 0):.3f}",
                        record.get('rule_type', ''),
                        str(record.get('available_rooms', [])),
                        record.get('bet_room', ''),
                        record.get('selection_strategy', ''),
                        f"{record.get('bet_amount', 0):.2f}",
                        record.get('actual_room', ''),
                        record.get('result', ''),
                        f"{record.get('profit', 0):+.2f}",
                        f"{cumulative_profit:+.2f}",
                        f"{win_rate:.1f}%"
                    ]
                    writer.writerow(row)
    
    def write_markdown_file(self):
        """写入Markdown文件"""
        
        session_info = self.session_data['session_info']
        total_bets = session_info['total_bets']
        total_wins = session_info['total_wins']
        total_profit = session_info['total_profit']
        win_rate = (total_wins / total_bets) * 100 if total_bets > 0 else 0
        
        with open(self.md_file, 'w', encoding='utf-8') as f:
            f.write(f"# 🎯 投注系统实时记录\n\n")
            f.write(f"**会话开始时间**: {self.session_start.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**最后更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"## 📊 实时统计\n\n")
            f.write(f"- **总投注次数**: {total_bets}\n")
            f.write(f"- **获胜次数**: {total_wins}\n")
            f.write(f"- **胜率**: {win_rate:.1f}%\n")
            f.write(f"- **总盈亏**: {total_profit:+.2f}元\n\n")
            
            f.write(f"## 📋 详细投注记录\n\n")
            f.write(f"| 时间 | 期号 | 投注房间 | 投注金额 | 开奖房间 | 结果 | 盈亏 |\n")
            f.write(f"|------|------|----------|----------|----------|------|------|\n")

            # 写入完成的记录
            for record in self.session_data['betting_records']:
                if record.get('status') == 'completed':
                    # 格式化时间
                    timestamp = record.get('timestamp', '')
                    if timestamp:
                        try:
                            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            time_str = dt.strftime('%H:%M:%S')
                        except:
                            time_str = ''
                    else:
                        time_str = ''

                    f.write(f"| {time_str} | ")
                    f.write(f"{record.get('issue', '')} | ")
                    f.write(f"{record.get('bet_room', '')} | ")
                    f.write(f"{record.get('bet_amount', 0):.2f}元 | ")
                    f.write(f"{record.get('actual_room', '')} | ")
                    f.write(f"{record.get('result', '')} | ")
                    f.write(f"{record.get('profit', 0):+.2f}元 |\n")

            # 添加房间统计信息
            self.write_room_statistics(f)

            # 添加LCG算法选择详情
            self.write_lcg_details(f)

    def write_room_statistics(self, f):
        """写入房间统计信息"""

        # 收集所有期号的房间统计数据
        room_stats_by_issue = {}
        for record in self.session_data['betting_records']:
            if record.get('status') == 'completed' and 'room_stats' in record:
                issue = record.get('issue')
                room_stats = record.get('room_stats', {})
                if issue and room_stats:
                    room_stats_by_issue[issue] = room_stats

        if not room_stats_by_issue:
            return

        f.write(f"\n## 🏠 房间统计信息\n\n")

        # 为每个期号写入房间统计
        for issue, room_stats in room_stats_by_issue.items():
            f.write(f"### 期号 {issue}\n\n")
            f.write(f"| 房间号 | 投入金额 | 分享金额 | 投入道具 | 金额+道具累计 | 房间人数 | 人均投入 |\n")
            f.write(f"|--------|----------|----------|----------|------------|----------|----------|\n")

            # 计算总计数据
            total_medal = 0.0
            total_share = 0.0
            total_buy_stroke = 0
            total_stroke = 0.0
            total_users = 0

            for room_num in range(1, 9):  # 房间1-8
                if room_num in room_stats:
                    stats = room_stats[room_num]
                    medal = stats.get('total_medal', 0.0)
                    share = stats.get('share_medal', 0.0)
                    buy_stroke = stats.get('total_buy_stroke', 0)
                    stroke = stats.get('total_stroke', 0.0)
                    users = stats.get('user_count', 0)
                    avg_per_user = medal / users if users > 0 else 0.0

                    total_medal += medal
                    total_share += share
                    total_buy_stroke += buy_stroke
                    total_stroke += stroke
                    total_users += users

                    # 验证totalStroke计算公式: 投入金额*10 + totalBuyStroke
                    expected_stroke = medal * 10 + buy_stroke
                    stroke_match = "✓" if abs(stroke - expected_stroke) < 0.1 else "✗"

                    f.write(f"| 房间{room_num} | {medal:.1f}元 | {share:.1f}元 | {buy_stroke}道具 | {stroke:.1f}元{stroke_match} | {users}人 | {avg_per_user:.2f}元 |\n")
                else:
                    f.write(f"| 房间{room_num} | 0.0元 | 0.0元 | 0道具 | 0.0元 | 0人 | 0.00元 |\n")

            # 写入汇总信息
            avg_total = total_medal / total_users if total_users > 0 else 0
            # 验证总计算公式
            expected_total_stroke = total_medal * 10 + total_buy_stroke
            total_match = "✓" if abs(total_stroke - expected_total_stroke) < 0.1 else "✗"
            f.write(f"| **总计** | **{total_medal:.1f}元** | **{total_share:.1f}元** | **{total_buy_stroke}道具** | **{total_stroke:.1f}元{total_match}** | **{total_users}人** | **{avg_total:.2f}元** |\n")

            # 添加房间分析
            if room_stats:
                f.write(f"\n#### 📊 房间分析\n\n")

                # 找出投入最多和最少的房间
                sorted_rooms = sorted(room_stats.items(), key=lambda x: x[1].get('total_medal', 0), reverse=True)
                if sorted_rooms:
                    max_room = sorted_rooms[0]
                    min_room = sorted_rooms[-1]

                    f.write(f"- **投入最多**: 房间{max_room[0]} ({max_room[1].get('total_medal', 0):.1f}元, {max_room[1].get('user_count', 0)}人)\n")
                    f.write(f"- **投入最少**: 房间{min_room[0]} ({min_room[1].get('total_medal', 0):.1f}元, {min_room[1].get('user_count', 0)}人)\n")

                # 找出人数最多和最少的房间
                sorted_by_users = sorted(room_stats.items(), key=lambda x: x[1].get('user_count', 0), reverse=True)
                if sorted_by_users:
                    max_users_room = sorted_by_users[0]
                    min_users_room = sorted_by_users[-1]

                    f.write(f"- **人数最多**: 房间{max_users_room[0]} ({max_users_room[1].get('user_count', 0)}人, {max_users_room[1].get('total_medal', 0):.1f}元)\n")
                    f.write(f"- **人数最少**: 房间{min_users_room[0]} ({min_users_room[1].get('user_count', 0)}人, {min_users_room[1].get('total_medal', 0):.1f}元)\n")

                # 找出道具投入最多的房间
                sorted_by_buy_stroke = sorted(room_stats.items(), key=lambda x: x[1].get('total_buy_stroke', 0), reverse=True)
                if sorted_by_buy_stroke:
                    max_buy_room = sorted_by_buy_stroke[0]
                    f.write(f"- **道具投入最多**: 房间{max_buy_room[0]} ({max_buy_room[1].get('total_buy_stroke', 0)}道具, {max_buy_room[1].get('total_medal', 0):.1f}元)\n")

                # 找出总投入最多的房间(金额+道具)
                sorted_by_total_stroke = sorted(room_stats.items(), key=lambda x: x[1].get('total_stroke', 0), reverse=True)
                if sorted_by_total_stroke:
                    max_stroke_room = sorted_by_total_stroke[0]
                    f.write(f"- **总投入最多**: 房间{max_stroke_room[0]} ({max_stroke_room[1].get('total_stroke', 0):.1f}元总投入)\n")

                # 计算投入分布
                medal_values = [stats.get('total_medal', 0) for stats in room_stats.values()]
                if medal_values:
                    avg_medal = sum(medal_values) / len(medal_values)
                    f.write(f"- **平均投入**: {avg_medal:.1f}元/房间\n")
                    f.write(f"- **投入差异**: {max(medal_values) - min(medal_values):.1f}元\n")

                # 计算分享金额统计
                share_values = [stats.get('share_medal', 0) for stats in room_stats.values()]
                if share_values and sum(share_values) > 0:
                    total_share_medal = sum(share_values)
                    f.write(f"- **总分享金额**: {total_share_medal:.1f}元\n")

                # 计算道具投入统计
                buy_stroke_values = [stats.get('total_buy_stroke', 0) for stats in room_stats.values()]
                if buy_stroke_values:
                    total_buy_strokes = sum(buy_stroke_values)
                    avg_buy_per_room = total_buy_strokes / len(buy_stroke_values) if buy_stroke_values else 0
                    f.write(f"- **总道具投入**: {total_buy_strokes}道具 (平均{avg_buy_per_room:.1f}道具/房间)\n")

                # 计算总投入统计(金额+道具)
                stroke_values = [stats.get('total_stroke', 0) for stats in room_stats.values()]
                if stroke_values:
                    total_strokes = sum(stroke_values)
                    avg_stroke_per_room = total_strokes / len(stroke_values) if stroke_values else 0
                    f.write(f"- **总投入(金额+道具)**: {total_strokes:.1f}元 (平均{avg_stroke_per_room:.1f}元/房间)\n")

                    # 验证计算公式
                    medal_values = [stats.get('total_medal', 0) for stats in room_stats.values()]
                    expected_total = sum(medal_values) * 10 + total_buy_strokes
                    formula_match = "✓" if abs(total_strokes - expected_total) < 0.1 else "✗"
                    f.write(f"- **计算公式验证**: 投入金额×10 + 道具投入 = 总投入 {formula_match}\n")

            f.write(f"\n")

    def write_lcg_details(self, f):
        """写入LCG算法选择详情"""

        f.write(f"\n## 🎯 LCG算法选择详情\n\n")

        # 写入LCG算法选择详情
        for i, record in enumerate(self.session_data['betting_records']):
            if record.get('status') == 'completed':
                f.write(f"### 期号{record.get('issue', '')} - 第{i+1}次投注\n\n")
                f.write(f"- **选择策略**: {record.get('strategy', 'LCG算法')}\n")
                f.write(f"- **投注房间**: {record.get('bet_room', '')}\n")

                # 显示投注金额和详细计算
                bet_amount = record.get('bet_amount', 0)
                f.write(f"- **投注金额**: {bet_amount:.2f}元")

                # 添加动态金额计算详情
                amount_calc = record.get('amount_calculation', {})
                if amount_calc:
                    f.write(f" (动态计算)\n")
                    f.write(f"  - 基础金额: {amount_calc.get('base_amount', 0):.2f}元\n")

                    # 马丁格尔调整
                    martingale = amount_calc.get('martingale_adjustment', 0)
                    if martingale > 0:
                        consecutive_losses = amount_calc.get('consecutive_losses', 0)
                        f.write(f"  - 马丁格尔调整: +{martingale:.2f}元 (连败{consecutive_losses}次)\n")

                    # 连胜奖励
                    win_bonus = amount_calc.get('win_streak_bonus', 0)
                    if win_bonus > 0:
                        consecutive_wins = amount_calc.get('consecutive_wins', 0)
                        f.write(f"  - 连胜奖励: +{win_bonus:.2f}元 (连胜{consecutive_wins}次)\n")

                    # 算法奖励
                    algo_bonus = amount_calc.get('algorithm_bonus', 0)
                    if algo_bonus > 0:
                        f.write(f"  - 算法奖励: +{algo_bonus:.2f}元\n")

                    # 风险调整
                    risk_adjustment = amount_calc.get('risk_adjustment', 0)
                    risk_level = amount_calc.get('risk_level', 'unknown')
                    if risk_adjustment != 0:
                        f.write(f"  - 风险调整: {risk_adjustment:+.2f}元 (风险等级: {risk_level})\n")

                    # 余额保护
                    balance_protection = amount_calc.get('balance_protection', 0)
                    if balance_protection < 0:
                        balance_ratio = amount_calc.get('balance_ratio', 1.0)
                        f.write(f"  - 余额保护: {balance_protection:.2f}元 (余额比例: {balance_ratio:.1%})\n")

                    # 计算公式
                    f.write(f"  - **计算公式**: {amount_calc.get('base_amount', 0):.0f}")
                    if martingale > 0:
                        f.write(f" + {martingale:.0f}(马丁格尔)")
                    if win_bonus > 0:
                        f.write(f" + {win_bonus:.0f}(连胜)")
                    if algo_bonus > 0:
                        f.write(f" + {algo_bonus:.0f}(算法)")
                    if risk_adjustment != 0:
                        f.write(f" {risk_adjustment:+.0f}(风险)")
                    if balance_protection < 0:
                        f.write(f" {balance_protection:.0f}(余额保护)")
                    f.write(f" = {bet_amount:.0f}元\n")
                else:
                    f.write(f"\n")

                f.write(f"- **开奖房间**: {record.get('actual_room', '')}\n")
                f.write(f"- **投注结果**: {record.get('result', '')}\n")
                f.write(f"- **盈亏**: {record.get('profit', 0):+.2f}元\n\n")

                # 添加选择详情
                details = record.get('selection_details', {})
                if details:
                    f.write(f"**选择详情**:\n")
                    for key, value in details.items():
                        # 安全格式化值，处理可能的元组或其他复杂类型
                        if isinstance(value, (tuple, list)):
                            value_str = str(value)
                        elif isinstance(value, (int, float)):
                            value_str = f"{value}"
                        else:
                            value_str = str(value)
                        f.write(f"- {key}: {value_str}\n")
                    f.write(f"\n")

    def get_summary(self) -> Dict:
        """获取会话摘要"""
        
        session_info = self.session_data['session_info']
        total_bets = session_info['total_bets']
        total_wins = session_info['total_wins']
        total_profit = session_info['total_profit']
        win_rate = (total_wins / total_bets) * 100 if total_bets > 0 else 0
        
        return {
            'session_duration': str(datetime.now() - self.session_start),
            'total_bets': total_bets,
            'total_wins': total_wins,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'files': {
                'json': self.json_file,
                'csv': self.csv_file,
                'markdown': self.md_file
            }
        }

# 全局记录器实例
_logger_instance = None

def get_logger(base_filename: str = "betting_log") -> RealTimeLogger:
    """获取全局记录器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = RealTimeLogger(base_filename)
    return _logger_instance

def log_prediction(issue: int, predicted_room: int, confidence: float, rule_type: str):
    """记录预测信息"""
    logger = get_logger()
    logger.log_prediction(issue, predicted_room, confidence, rule_type)

def log_room_selection(issue: int, available_rooms: List[int], selected_room: int, 
                      strategy: str, selection_details: Dict):
    """记录房间选择"""
    logger = get_logger()
    logger.log_room_selection(issue, available_rooms, selected_room, strategy, selection_details)

def log_betting(issue: int, bet_room: int, bet_amount: float, bet_success: bool,
               amount_calculation: Dict = None):
    """记录投注"""
    logger = get_logger()
    logger.log_betting(issue, bet_room, bet_amount, bet_success, amount_calculation)

def log_result(issue: int, actual_room: int, result: str, profit: float, room_stats: Dict = None):
    """记录结果"""
    logger = get_logger()
    logger.log_result(issue, actual_room, result, profit, room_stats)

def get_summary() -> Dict:
    """获取摘要"""
    logger = get_logger()
    return logger.get_summary()

if __name__ == "__main__":
    # 测试记录器
    logger = RealTimeLogger("test_log")
    
    # 模拟一次完整的投注过程
    logger.log_prediction(123456, 8, 0.750, "dynamic")
    logger.log_room_selection(123456, [1,2,3,4,5,6,7], 4, "映射策略", {"mapping": "8->4"})
    logger.log_betting(123456, 4, 10.0, True)
    logger.log_result(123456, 2, "获胜", 1.0)
    
    print("📊 测试完成，请查看生成的文件")
