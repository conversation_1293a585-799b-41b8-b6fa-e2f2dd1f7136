#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最优投注策略
基于游戏规则：选择数字≠开出数字则获胜，赔率1:1.1
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import json

class OptimalBettingStrategy:
    def __init__(self, sequence: List[int]):
        """初始化最优投注策略"""
        self.sequence = sequence
        self.n = len(sequence)
        self.prediction_rules = self.load_prediction_model()
        
    def load_prediction_model(self) -> Dict:
        """加载之前训练的预测模型"""
        try:
            with open("prediction_model.json", 'r', encoding='utf-8') as f:
                model_data = json.loads(f.read())
            print("成功加载预测模型")
            return model_data
        except FileNotFoundError:
            print("预测模型文件未找到，使用基础策略")
            return {'rules': [], 'strategies': {}}
    
    def calculate_number_probabilities(self, recent_sequence: List[int]) -> Dict[int, float]:
        """计算每个数字出现的概率"""
        print("=== 计算数字出现概率 ===")
        
        probabilities = {}
        
        # 1. 尝试使用预测规则
        rule_predictions = {}
        if self.prediction_rules.get('rules'):
            for rule in self.prediction_rules['rules'][:100]:  # 使用前100个最佳规则
                condition = tuple(rule['condition'])
                condition_length = len(condition)
                
                if len(recent_sequence) >= condition_length:
                    current_condition = tuple(recent_sequence[-condition_length:])
                    if current_condition == condition:
                        predicted_value = rule['next_value']
                        confidence = rule['confidence']
                        
                        if predicted_value not in rule_predictions:
                            rule_predictions[predicted_value] = []
                        rule_predictions[predicted_value].append(confidence)
        
        # 2. 基于规则的概率
        for value in range(1, 9):
            if value in rule_predictions:
                # 使用规则预测的平均置信度
                avg_confidence = np.mean(rule_predictions[value])
                probabilities[value] = avg_confidence
            else:
                # 使用基础概率（均匀分布）
                probabilities[value] = 1/8
        
        # 3. 结合频率分析
        recent_counter = Counter(recent_sequence[-200:])  # 最近200个数字
        total_recent = len(recent_sequence[-200:])
        
        for value in range(1, 9):
            recent_freq = recent_counter.get(value, 0) / total_recent
            # 加权平均：规则预测70% + 频率分析30%
            probabilities[value] = probabilities[value] * 0.7 + recent_freq * 0.3
        
        # 4. 归一化
        total_prob = sum(probabilities.values())
        for value in probabilities:
            probabilities[value] /= total_prob
        
        print("各数字出现概率:")
        for value in range(1, 9):
            print(f"  数字 {value}: {probabilities[value]:.4f}")
        
        return probabilities
    
    def calculate_betting_values(self, probabilities: Dict[int, float]) -> Dict[int, float]:
        """计算每个数字的投注价值"""
        print(f"\n=== 计算投注价值 ===")
        
        betting_values = {}
        
        for bet_number in range(1, 9):
            # 计算选择这个数字的期望收益
            win_probability = sum(prob for num, prob in probabilities.items() if num != bet_number)
            lose_probability = probabilities[bet_number]
            
            # 期望收益 = 获胜概率 × 1.1 - 失败概率 × 1
            expected_return = win_probability * 1.1 - lose_probability * 1.0
            
            betting_values[bet_number] = expected_return
            
            print(f"  投注数字 {bet_number}: 获胜概率 {win_probability:.4f}, "
                  f"期望收益 {expected_return:.4f}")
        
        return betting_values
    
    def find_optimal_bet(self, recent_sequence: List[int]) -> Dict:
        """找到最优投注"""
        print(f"\n=== 寻找最优投注 ===")
        
        # 1. 计算概率
        probabilities = self.calculate_number_probabilities(recent_sequence)
        
        # 2. 计算投注价值
        betting_values = self.calculate_betting_values(probabilities)
        
        # 3. 找到最优投注
        best_bet = max(betting_values.keys(), key=lambda k: betting_values[k])
        best_value = betting_values[best_bet]
        
        # 4. 计算风险指标
        predicted_number = max(probabilities.keys(), key=lambda k: probabilities[k])
        prediction_confidence = probabilities[predicted_number]
        
        # 5. 投注建议
        recommendation = {
            'optimal_bet': best_bet,
            'expected_return': best_value,
            'predicted_number': predicted_number,
            'prediction_confidence': prediction_confidence,
            'win_probability': sum(prob for num, prob in probabilities.items() if num != best_bet),
            'should_bet': best_value > 0,  # 只有期望收益为正才建议投注
            'confidence_level': 'high' if prediction_confidence > 0.3 else 'medium' if prediction_confidence > 0.2 else 'low'
        }
        
        print(f"\n最优投注建议:")
        print(f"  建议投注数字: {best_bet}")
        print(f"  期望收益: {best_value:.4f}")
        print(f"  预测最可能开出: {predicted_number} (置信度: {prediction_confidence:.4f})")
        print(f"  获胜概率: {recommendation['win_probability']:.4f}")
        print(f"  是否建议投注: {'是' if recommendation['should_bet'] else '否'}")
        print(f"  置信度等级: {recommendation['confidence_level']}")
        
        return recommendation
    
    def simulate_betting_performance(self, test_size: int = 1000) -> Dict:
        """模拟投注性能"""
        print(f"\n=== 模拟投注性能 (最后 {test_size} 个数据) ===")
        
        if self.n < test_size + 50:
            test_size = max(100, self.n - 50)
        
        start_idx = self.n - test_size
        
        total_bets = 0
        total_wins = 0
        total_return = 0
        bet_history = []
        
        for i in range(start_idx, self.n):
            # 使用前面的数据进行预测
            recent_sequence = self.sequence[:i]
            
            if len(recent_sequence) < 10:  # 需要足够的历史数据
                continue
            
            # 获取投注建议
            recommendation = self.find_optimal_bet(recent_sequence[-50:])  # 使用最近50个数字
            
            if recommendation['should_bet']:
                actual_number = self.sequence[i]
                bet_number = recommendation['optimal_bet']
                
                # 判断是否获胜
                won = (bet_number != actual_number)
                
                total_bets += 1
                if won:
                    total_wins += 1
                    total_return += 1.1  # 获胜得到1.1倍
                else:
                    total_return -= 1.0  # 失败损失1倍
                
                bet_history.append({
                    'position': i,
                    'bet_number': bet_number,
                    'actual_number': actual_number,
                    'won': won,
                    'expected_return': recommendation['expected_return'],
                    'prediction_confidence': recommendation['prediction_confidence']
                })
        
        # 计算性能指标
        win_rate = total_wins / total_bets if total_bets > 0 else 0
        average_return = total_return / total_bets if total_bets > 0 else 0
        roi = (total_return / total_bets) * 100 if total_bets > 0 else 0
        
        print(f"模拟结果:")
        print(f"  总投注次数: {total_bets}")
        print(f"  获胜次数: {total_wins}")
        print(f"  胜率: {win_rate:.4f} ({win_rate*100:.2f}%)")
        print(f"  总收益: {total_return:.2f}")
        print(f"  平均收益: {average_return:.4f}")
        print(f"  投资回报率: {roi:.2f}%")
        
        return {
            'total_bets': total_bets,
            'total_wins': total_wins,
            'win_rate': win_rate,
            'total_return': total_return,
            'average_return': average_return,
            'roi': roi,
            'bet_history': bet_history[-20:]  # 最后20次投注记录
        }
    
    def generate_betting_recommendations(self, count: int = 10) -> List[Dict]:
        """生成未来投注建议"""
        print(f"\n=== 生成未来 {count} 期投注建议 ===")
        
        recommendations = []
        current_sequence = self.sequence.copy()
        
        for round_num in range(1, count + 1):
            # 获取投注建议
            recommendation = self.find_optimal_bet(current_sequence[-50:])
            
            # 添加轮次信息
            recommendation['round'] = round_num
            recommendation['sequence_length'] = len(current_sequence)
            
            recommendations.append(recommendation)
            
            # 为下一轮预测，假设按照预测结果更新序列
            predicted_next = recommendation['predicted_number']
            current_sequence.append(predicted_next)
            
            print(f"\n第 {round_num} 期建议:")
            print(f"  投注数字: {recommendation['optimal_bet']}")
            print(f"  期望收益: {recommendation['expected_return']:.4f}")
            print(f"  是否投注: {'是' if recommendation['should_bet'] else '否'}")
            
            if not recommendation['should_bet']:
                print(f"  建议: 本期期望收益为负，建议观望")
        
        return recommendations
    
    def run_optimal_strategy(self) -> Dict:
        """运行最优策略分析"""
        print("开始最优投注策略分析...")
        print(f"基于 {self.n} 个历史数据")
        print(f"游戏规则: 选择数字≠开出数字获胜，赔率1:1.1")
        print()
        
        # 1. 当前最优投注
        current_recommendation = self.find_optimal_bet(self.sequence[-50:])
        
        # 2. 性能模拟
        performance = self.simulate_betting_performance()
        
        # 3. 未来建议
        future_recommendations = self.generate_betting_recommendations(10)
        
        return {
            'current_recommendation': current_recommendation,
            'performance_simulation': performance,
            'future_recommendations': future_recommendations
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机23607.txt")
    strategy = OptimalBettingStrategy(sequence)
    results = strategy.run_optimal_strategy()
    
    print(f"\n=== 最优投注策略总结 ===")
    
    current = results['current_recommendation']
    performance = results['performance_simulation']
    
    print(f"\n当前投注建议:")
    print(f"  建议投注数字: {current['optimal_bet']}")
    print(f"  期望收益: {current['expected_return']:.4f}")
    print(f"  是否建议投注: {'是' if current['should_bet'] else '否'}")
    
    print(f"\n历史性能模拟:")
    print(f"  胜率: {performance['win_rate']*100:.2f}%")
    print(f"  投资回报率: {performance['roi']:.2f}%")
    print(f"  总投注次数: {performance['total_bets']}")
    
    # 统计未来建议
    future_bets = [r for r in results['future_recommendations'] if r['should_bet']]
    print(f"\n未来10期建议:")
    print(f"  建议投注期数: {len(future_bets)}")
    print(f"  建议观望期数: {10 - len(future_bets)}")
    
    if performance['roi'] > 0:
        print(f"\n🎯 策略显示正收益！建议谨慎试用")
    else:
        print(f"\n⚠️  策略显示负收益，建议谨慎或观望")
    
    print(f"\n💡 记住：这仍然是一个对庄家有利的游戏，请合理控制风险！")
