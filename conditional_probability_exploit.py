#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于条件概率异常的逆向尝试
利用发现的条件概率异常构建预测模型
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter

class ConditionalProbabilityExploiter:
    def __init__(self, sequence: List[int]):
        """初始化条件概率利用器"""
        self.sequence = sequence
        self.n = len(sequence)
        self.conditional_rules = {}
        
    def extract_strong_rules(self, min_confidence: float = 0.6, min_support: int = 3) -> Dict:
        """提取强条件规则"""
        print("=== 提取强条件规则 ===")
        
        strong_rules = {}
        
        for condition_length in range(1, 6):
            print(f"\n分析 {condition_length} 元条件:")
            
            # 统计条件和后续值
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(condition_length, self.n):
                condition = tuple(self.sequence[i-condition_length:i])
                next_val = self.sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            # 找出强规则
            rules = []
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= min_support:
                    # 找出最可能的下一个值
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    
                    if confidence >= min_confidence:
                        rules.append({
                            'condition': condition,
                            'next_value': best_next,
                            'confidence': confidence,
                            'support': total,
                            'frequency': next_counts[best_next]
                        })
            
            # 按置信度排序
            rules.sort(key=lambda x: x['confidence'], reverse=True)
            
            if rules:
                print(f"  发现 {len(rules)} 个强规则:")
                for rule in rules[:10]:  # 显示前10个
                    print(f"    {rule['condition']} -> {rule['next_value']} "
                          f"(置信度: {rule['confidence']:.3f}, 支持度: {rule['support']})")
                
                strong_rules[condition_length] = rules
        
        return strong_rules
    
    def build_prediction_model(self, strong_rules: Dict) -> Dict:
        """构建预测模型"""
        print(f"\n=== 构建预测模型 ===")
        
        # 将规则按优先级排序（优先使用长条件、高置信度的规则）
        all_rules = []
        for length, rules in strong_rules.items():
            for rule in rules:
                rule['priority'] = length * 1000 + rule['confidence'] * 100
                all_rules.append(rule)
        
        all_rules.sort(key=lambda x: x['priority'], reverse=True)
        
        print(f"总共 {len(all_rules)} 个规则，按优先级排序")
        
        # 测试模型在历史数据上的表现
        correct_predictions = 0
        total_predictions = 0
        prediction_log = []
        
        for i in range(5, self.n):  # 从第6个数字开始预测
            predicted = None
            used_rule = None
            
            # 尝试应用规则（从最长条件开始）
            for rule in all_rules:
                condition_length = len(rule['condition'])
                if i >= condition_length:
                    current_condition = tuple(self.sequence[i-condition_length:i])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        used_rule = rule
                        break
            
            if predicted is not None:
                actual = self.sequence[i]
                is_correct = (predicted == actual)
                
                if is_correct:
                    correct_predictions += 1
                
                total_predictions += 1
                
                prediction_log.append({
                    'position': i,
                    'condition': used_rule['condition'],
                    'predicted': predicted,
                    'actual': actual,
                    'correct': is_correct,
                    'confidence': used_rule['confidence']
                })
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        print(f"历史预测准确率: {accuracy:.4f} ({correct_predictions}/{total_predictions})")
        
        return {
            'rules': all_rules,
            'accuracy': accuracy,
            'prediction_log': prediction_log[-20:],  # 最后20个预测
            'total_predictions': total_predictions
        }
    
    def predict_next_values(self, model: Dict, count: int = 10) -> List[int]:
        """预测接下来的值"""
        print(f"\n=== 预测接下来的 {count} 个值 ===")
        
        predictions = []
        current_sequence = self.sequence.copy()
        
        for step in range(count):
            predicted = None
            used_rule = None
            
            # 尝试应用规则
            for rule in model['rules']:
                condition_length = len(rule['condition'])
                if len(current_sequence) >= condition_length:
                    current_condition = tuple(current_sequence[-condition_length:])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        used_rule = rule
                        break
            
            if predicted is None:
                # 如果没有匹配的规则，使用最频繁的数字
                counter = Counter(self.sequence)
                predicted = counter.most_common(1)[0][0]
                used_rule = {'condition': 'fallback', 'confidence': 0.0}
            
            predictions.append(predicted)
            current_sequence.append(predicted)
            
            print(f"  步骤 {step+1}: 条件 {used_rule['condition']} -> 预测 {predicted} "
                  f"(置信度: {used_rule['confidence']:.3f})")
        
        return predictions
    
    def validate_model(self, model: Dict, test_ratio: float = 0.2) -> Dict:
        """验证模型"""
        print(f"\n=== 模型验证 ===")
        
        # 使用最后20%的数据进行验证
        test_start = int(self.n * (1 - test_ratio))
        test_sequence = self.sequence[test_start:]
        
        correct = 0
        total = 0
        
        for i in range(5, len(test_sequence)):
            predicted = None
            
            # 构建当前序列（包括训练数据）
            current_full_sequence = self.sequence[:test_start] + test_sequence[:i]
            
            # 尝试预测
            for rule in model['rules']:
                condition_length = len(rule['condition'])
                if len(current_full_sequence) >= condition_length:
                    current_condition = tuple(current_full_sequence[-condition_length:])
                    if current_condition == rule['condition']:
                        predicted = rule['next_value']
                        break
            
            if predicted is not None:
                actual = test_sequence[i]
                if predicted == actual:
                    correct += 1
                total += 1
        
        validation_accuracy = correct / total if total > 0 else 0
        
        print(f"验证集准确率: {validation_accuracy:.4f} ({correct}/{total})")
        
        return {
            'validation_accuracy': validation_accuracy,
            'correct_predictions': correct,
            'total_predictions': total
        }
    
    def run_exploitation(self) -> Dict:
        """运行条件概率利用"""
        print("开始基于条件概率的逆向尝试...")
        print(f"序列长度: {self.n}")
        print()
        
        # 提取强规则
        strong_rules = self.extract_strong_rules()
        
        if not strong_rules:
            print("未发现足够强的条件规则")
            return {'success': False}
        
        # 构建预测模型
        model = self.build_prediction_model(strong_rules)
        
        # 验证模型
        validation = self.validate_model(model)
        
        # 预测未来值
        future_predictions = self.predict_next_values(model, 20)
        
        return {
            'success': True,
            'strong_rules': strong_rules,
            'model': model,
            'validation': validation,
            'future_predictions': future_predictions
        }

def load_sequence(filename: str) -> List[int]:
    """加载序列数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return [int(line.strip()) for line in f if line.strip()]

if __name__ == "__main__":
    sequence = load_sequence("随机2000个数字.txt")
    exploiter = ConditionalProbabilityExploiter(sequence)
    results = exploiter.run_exploitation()
    
    if results['success']:
        print(f"\n=== 条件概率利用总结 ===")
        print(f"历史预测准确率: {results['model']['accuracy']:.4f}")
        print(f"验证集准确率: {results['validation']['validation_accuracy']:.4f}")
        print(f"预测的未来20个值: {results['future_predictions']}")
        
        if results['validation']['validation_accuracy'] > 0.15:
            print("\n🎯 发现了可利用的模式！这个方法可能有效。")
        else:
            print("\n❌ 条件概率方法效果有限。")
    else:
        print("条件概率利用失败")
