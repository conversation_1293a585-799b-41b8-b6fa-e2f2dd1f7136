#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集对比分析
比较69个、2000个和10000个数据集的差异
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import matplotlib.pyplot as plt

class DatasetComparisonAnalyzer:
    def __init__(self):
        """初始化数据集对比分析器"""
        self.datasets = {}
        
    def load_all_datasets(self):
        """加载所有数据集"""
        datasets = {
            'small_69': '随机生成1-8.txt',
            'medium_2000': '随机2000个数字.txt', 
            'large_10000': '随机10000个数字.txt'
        }
        
        for name, filename in datasets.items():
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    sequence = [int(line.strip()) for line in f if line.strip()]
                self.datasets[name] = sequence
                print(f"加载 {name}: {len(sequence)} 个数字")
            except FileNotFoundError:
                print(f"文件 {filename} 未找到")
    
    def basic_statistics_comparison(self) -> Dict:
        """基础统计对比"""
        print("\n=== 基础统计对比 ===")
        
        results = {}
        
        print(f"{'数据集':<12} {'长度':<8} {'均值':<8} {'标准差':<8} {'最小值':<8} {'最大值':<8}")
        print("-" * 60)
        
        for name, sequence in self.datasets.items():
            stats = {
                'length': len(sequence),
                'mean': np.mean(sequence),
                'std': np.std(sequence),
                'min': np.min(sequence),
                'max': np.max(sequence),
                'median': np.median(sequence)
            }
            
            print(f"{name:<12} {stats['length']:<8} {stats['mean']:<8.3f} "
                  f"{stats['std']:<8.3f} {stats['min']:<8} {stats['max']:<8}")
            
            results[name] = stats
        
        return results
    
    def frequency_distribution_comparison(self) -> Dict:
        """频率分布对比"""
        print(f"\n=== 频率分布对比 ===")
        
        results = {}
        
        print(f"{'数据集':<12}", end="")
        for i in range(1, 9):
            print(f"{'数字'+str(i):<8}", end="")
        print("卡方值")
        print("-" * 80)
        
        for name, sequence in self.datasets.items():
            counter = Counter(sequence)
            expected_freq = len(sequence) / 8
            
            frequencies = []
            chi_square = 0
            
            for i in range(1, 9):
                count = counter.get(i, 0)
                freq = count / len(sequence)
                frequencies.append(freq)
                chi_square += (count - expected_freq) ** 2 / expected_freq
            
            print(f"{name:<12}", end="")
            for freq in frequencies:
                print(f"{freq:<8.3f}", end="")
            print(f"{chi_square:<8.3f}")
            
            results[name] = {
                'frequencies': frequencies,
                'chi_square': chi_square,
                'uniformity_test': chi_square < 14.067  # α=0.05, df=7
            }
        
        return results
    
    def pattern_complexity_comparison(self) -> Dict:
        """模式复杂度对比"""
        print(f"\n=== 模式复杂度对比 ===")
        
        results = {}
        
        for name, sequence in self.datasets.items():
            print(f"\n{name} 模式分析:")
            
            # 分析不同长度的模式数量
            pattern_counts = {}
            for length in range(2, 6):
                patterns = set()
                for i in range(len(sequence) - length + 1):
                    pattern = tuple(sequence[i:i + length])
                    patterns.add(pattern)
                
                pattern_counts[length] = len(patterns)
                theoretical_max = 8 ** length
                coverage = len(patterns) / theoretical_max
                
                print(f"  {length}元模式: {len(patterns)}/{theoretical_max} ({coverage:.3f})")
            
            # 计算序列复杂度（近似Kolmogorov复杂度）
            # 使用LZ77压缩比作为复杂度指标
            sequence_str = ''.join(map(str, sequence))
            try:
                import zlib
                compressed = zlib.compress(sequence_str.encode())
                compression_ratio = len(compressed) / len(sequence_str)
                print(f"  压缩比: {compression_ratio:.3f} (越低越有规律)")
            except:
                compression_ratio = None
            
            results[name] = {
                'pattern_counts': pattern_counts,
                'compression_ratio': compression_ratio
            }
        
        return results
    
    def conditional_probability_comparison(self) -> Dict:
        """条件概率对比"""
        print(f"\n=== 条件概率强度对比 ===")
        
        results = {}
        
        for name, sequence in self.datasets.items():
            print(f"\n{name} 条件概率分析:")
            
            strong_rules_count = 0
            total_rules_count = 0
            max_confidence = 0
            
            # 分析3元条件
            condition_stats = defaultdict(lambda: defaultdict(int))
            condition_counts = defaultdict(int)
            
            for i in range(3, len(sequence)):
                condition = tuple(sequence[i-3:i])
                next_val = sequence[i]
                
                condition_stats[condition][next_val] += 1
                condition_counts[condition] += 1
            
            for condition, next_counts in condition_stats.items():
                total = condition_counts[condition]
                if total >= 3:
                    total_rules_count += 1
                    best_next = max(next_counts.keys(), key=lambda k: next_counts[k])
                    confidence = next_counts[best_next] / total
                    max_confidence = max(max_confidence, confidence)
                    
                    if confidence >= 0.6:
                        strong_rules_count += 1
            
            strong_rule_ratio = strong_rules_count / total_rules_count if total_rules_count > 0 else 0
            
            print(f"  总规则数: {total_rules_count}")
            print(f"  强规则数 (置信度≥0.6): {strong_rules_count}")
            print(f"  强规则比例: {strong_rule_ratio:.3f}")
            print(f"  最大置信度: {max_confidence:.3f}")
            
            results[name] = {
                'total_rules': total_rules_count,
                'strong_rules': strong_rules_count,
                'strong_rule_ratio': strong_rule_ratio,
                'max_confidence': max_confidence
            }
        
        return results
    
    def continuity_analysis(self) -> Dict:
        """连续性分析"""
        print(f"\n=== 数据连续性分析 ===")
        
        results = {}
        
        # 检查小数据集是否是大数据集的子序列
        small_seq = self.datasets.get('small_69', [])
        medium_seq = self.datasets.get('medium_2000', [])
        large_seq = self.datasets.get('large_10000', [])
        
        if small_seq and large_seq:
            print("检查69个数字序列是否在10000个数字序列中:")
            
            best_match_pos = 0
            best_match_count = 0
            
            # 滑动窗口搜索
            for start_pos in range(len(large_seq) - len(small_seq) + 1):
                matches = 0
                for i in range(len(small_seq)):
                    if large_seq[start_pos + i] == small_seq[i]:
                        matches += 1
                
                if matches > best_match_count:
                    best_match_count = matches
                    best_match_pos = start_pos
            
            match_rate = best_match_count / len(small_seq)
            print(f"  最佳匹配位置: {best_match_pos}")
            print(f"  匹配率: {match_rate:.3f} ({best_match_count}/{len(small_seq)})")
            
            results['small_in_large'] = {
                'match_position': best_match_pos,
                'match_rate': match_rate,
                'match_count': best_match_count
            }
        
        if medium_seq and large_seq:
            print("\n检查2000个数字序列是否在10000个数字序列中:")
            
            # 检查前2000个是否匹配
            if len(large_seq) >= len(medium_seq):
                matches = sum(1 for i in range(len(medium_seq)) 
                            if large_seq[i] == medium_seq[i])
                match_rate = matches / len(medium_seq)
                print(f"  前2000个匹配率: {match_rate:.3f} ({matches}/{len(medium_seq)})")
                
                results['medium_in_large_prefix'] = {
                    'match_rate': match_rate,
                    'match_count': matches
                }
        
        return results
    
    def run_comparison_analysis(self) -> Dict:
        """运行完整对比分析"""
        print("开始数据集对比分析...")
        
        # 加载数据集
        self.load_all_datasets()
        
        if not self.datasets:
            print("没有找到数据集文件")
            return {}
        
        # 各项对比分析
        basic_stats = self.basic_statistics_comparison()
        frequency_comparison = self.frequency_distribution_comparison()
        pattern_comparison = self.pattern_complexity_comparison()
        conditional_comparison = self.conditional_probability_comparison()
        continuity_analysis = self.continuity_analysis()
        
        return {
            'basic_statistics': basic_stats,
            'frequency_distribution': frequency_comparison,
            'pattern_complexity': pattern_comparison,
            'conditional_probability': conditional_comparison,
            'continuity': continuity_analysis
        }
    
    def generate_conclusions(self, results: Dict) -> str:
        """生成分析结论"""
        conclusions = "\n=== 分析结论 ===\n"
        
        # 数据质量对比
        if 'conditional_probability' in results:
            cp_results = results['conditional_probability']
            
            conclusions += "1. 条件概率强度对比:\n"
            for name, data in cp_results.items():
                conclusions += f"   {name}: 强规则比例 {data['strong_rule_ratio']:.3f}, "
                conclusions += f"最大置信度 {data['max_confidence']:.3f}\n"
        
        # 数据连续性
        if 'continuity' in results and results['continuity']:
            conclusions += "\n2. 数据连续性:\n"
            continuity = results['continuity']
            
            if 'small_in_large' in continuity:
                match_rate = continuity['small_in_large']['match_rate']
                if match_rate > 0.8:
                    conclusions += "   69个数字序列很可能是10000个序列的连续子集\n"
                elif match_rate > 0.5:
                    conclusions += "   69个数字序列可能与10000个序列有部分重叠\n"
                else:
                    conclusions += "   69个数字序列与10000个序列来源不同\n"
        
        # 预测能力差异
        conclusions += "\n3. 预测能力差异原因:\n"
        conclusions += "   - 小数据集可能存在偶然的局部模式\n"
        conclusions += "   - 大数据集揭示了真实的随机性质\n"
        conclusions += "   - 不同数据集可能来自不同的生成时期或参数\n"
        
        conclusions += "\n4. 建议:\n"
        conclusions += "   - 验证数据集的连续性和一致性\n"
        conclusions += "   - 如果数据不连续，需要分别建模\n"
        conclusions += "   - 考虑随机数生成器可能发生了参数变化\n"
        
        return conclusions

def main():
    """主函数"""
    analyzer = DatasetComparisonAnalyzer()
    results = analyzer.run_comparison_analysis()
    
    conclusions = analyzer.generate_conclusions(results)
    print(conclusions)

if __name__ == "__main__":
    main()
