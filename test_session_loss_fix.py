#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试会话损失修复 - 验证风险控制使用当前会话损失而不是历史累积损失
"""

import os
import json
from datetime import datetime
from api_framework import GameAPIClient
from optimized_random_betting_system import OptimizedRandomBettingSystem

def test_session_loss_fix():
    """测试会话损失修复"""
    
    print("🧪 测试会话损失修复")
    print("=" * 60)
    
    # 创建模拟API客户端
    class MockAPIClient:
        def __init__(self):
            self.base_url = "http://mock"
            self.headers = {}
        
        def get_game_state(self):
            return None
        
        def place_bet(self, room, amount):
            class MockResult:
                def __init__(self):
                    self.success = True
                    self.total_amount = amount
                    self.data = {'room': room, 'amount': amount}
            return MockResult()
    
    # 模拟一个有历史损失的状态文件
    state_file = f"system_state_{datetime.now().strftime('%Y%m%d')}.json"
    
    # 创建模拟的历史状态 (有大量历史损失)
    mock_state = {
        "consecutive_losses": 1,
        "consecutive_wins": 0,
        "daily_loss": 45.0,  # 历史累积损失
        "total_profit": -45.0,  # 历史累积亏损
        "current_balance": 155.0,  # 从200元降到155元 (历史损失45元)
        "total_bets": 50,
        "total_wins": 40,
        "last_update": datetime.now().isoformat()
    }
    
    print(f"📝 创建模拟历史状态文件: {state_file}")
    with open(state_file, 'w', encoding='utf-8') as f:
        json.dump(mock_state, f, indent=2, ensure_ascii=False)
    
    print(f"📊 模拟历史状态:")
    print(f"   历史累积损失: {mock_state['total_profit']:.2f}元")
    print(f"   当前余额: {mock_state['current_balance']:.2f}元")
    print(f"   从初始200元损失: {200 - mock_state['current_balance']:.2f}元")
    
    # 创建系统配置
    config = {
        'base_bet_amount': 2,
        'max_bet_amount': 20,
        'min_bet_amount': 1,
        'max_consecutive_losses': 5,
        'max_daily_loss': 50,  # 日损失限制50元
        'initial_balance': 200,
        'auto_report_interval': 10,
        'risk_monitoring': True,
        'real_time_logging': True
    }
    
    print(f"\n🎯 创建系统实例...")
    api_client = MockAPIClient()
    system = OptimizedRandomBettingSystem(api_client, config)
    
    print(f"\n📊 系统状态分析:")
    print(f"   初始余额: {system.initial_balance:.2f}元")
    print(f"   当前余额: {system.current_balance:.2f}元")
    print(f"   会话开始余额: {system.session_start_balance:.2f}元")
    print(f"   历史累积损失: {system.initial_balance - system.current_balance:.2f}元")
    print(f"   当前会话损失: {system.session_start_balance - system.current_balance:.2f}元")
    
    # 测试风险控制
    print(f"\n🛡️ 测试风险控制:")
    print(f"   日损失限制: {system.max_daily_loss:.2f}元")
    
    # 检查当前是否可以投注
    can_bet_before = system.can_place_bet()
    print(f"   修复前风险控制: {'✅ 允许投注' if can_bet_before else '🛑 阻止投注'}")
    
    # 模拟一些小额投注和结果，测试会话损失计算
    print(f"\n🎲 模拟当前会话的投注...")
    
    # 模拟投注1: 失败 -3元
    print(f"\n--- 模拟投注1 ---")
    bet_info = {
        'issue': 130001,
        'room': 1,
        'amount': 3.0,
        'timestamp': datetime.now().timestamp(),
        'strategy': 'LCG算法'
    }
    system.bet_history.append(bet_info)
    system.total_bets += 1
    
    print(f"投注: 房间1, 金额3元")
    system.process_result(130001, 1)  # 失败
    
    print(f"会话损失: {system.session_start_balance - system.current_balance:.2f}元")
    
    # 模拟投注2: 失败 -4元
    print(f"\n--- 模拟投注2 ---")
    bet_info = {
        'issue': 130002,
        'room': 2,
        'amount': 4.0,
        'timestamp': datetime.now().timestamp(),
        'strategy': 'LCG算法'
    }
    system.bet_history.append(bet_info)
    system.total_bets += 1
    
    print(f"投注: 房间2, 金额4元")
    system.process_result(130002, 2)  # 失败
    
    print(f"会话损失: {system.session_start_balance - system.current_balance:.2f}元")
    
    # 检查风险控制
    can_bet_after = system.can_place_bet()
    session_loss = system.session_start_balance - system.current_balance
    
    print(f"\n📊 最终状态:")
    print(f"   会话开始余额: {system.session_start_balance:.2f}元")
    print(f"   当前余额: {system.current_balance:.2f}元")
    print(f"   当前会话损失: {session_loss:.2f}元")
    print(f"   历史累积损失: {system.initial_balance - system.current_balance:.2f}元")
    print(f"   日损失限制: {system.max_daily_loss:.2f}元")
    
    print(f"\n🛡️ 风险控制结果:")
    if session_loss < system.max_daily_loss:
        print(f"   ✅ 会话损失{session_loss:.2f}元 < 限制{system.max_daily_loss:.2f}元")
        print(f"   ✅ 应该允许继续投注")
    else:
        print(f"   🛑 会话损失{session_loss:.2f}元 >= 限制{system.max_daily_loss:.2f}元")
        print(f"   🛑 应该阻止投注")
    
    print(f"   实际风险控制: {'✅ 允许投注' if can_bet_after else '🛑 阻止投注'}")
    
    # 验证修复是否成功
    expected_allow = session_loss < system.max_daily_loss
    actual_allow = can_bet_after
    
    if expected_allow == actual_allow:
        print(f"\n✅ 修复成功！风险控制现在基于当前会话损失")
        success = True
    else:
        print(f"\n❌ 修复失败！风险控制仍有问题")
        success = False
    
    # 清理测试文件
    if os.path.exists(state_file):
        os.remove(state_file)
        print(f"🗑️ 已清理测试文件: {state_file}")
    
    return success

if __name__ == "__main__":
    success = test_session_loss_fix()
    if success:
        print(f"\n🎉 会话损失修复测试通过！")
        print(f"💡 现在风险控制将基于当前会话的损失，而不是历史累积损失")
        print(f"💡 这样可以避免历史损失影响当前会话的投注")
    else:
        print(f"\n❌ 测试失败，需要进一步调试")
